{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "200", "ordre": 1, "codeGareArrivee": "610", "dateTimeDepart": "2025-05-15T08:44:00+01:00", "dateTimeArrivee": "2025-05-15T10:10:00+01:00", "durationTrajet": "01:26:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 4313, "numeroCommercial": "803", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:44:00+01:00", "dateHeureArrivee": "2025-05-15T10:10:00+01:00", "codeGareDepart": "200", "codeGareArrivee": "610", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:26:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "200", "ordre": 2, "codeGareArrivee": "610", "dateTimeDepart": "2025-05-15T10:44:00+01:00", "dateTimeArrivee": "2025-05-15T12:05:00+01:00", "durationTrajet": "01:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1556, "numeroCommercial": "805", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:44:00+01:00", "dateHeureArrivee": "2025-05-15T12:05:00+01:00", "codeGareDepart": "200", "codeGareArrivee": "610", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "200", "ordre": 3, "codeGareArrivee": "610", "dateTimeDepart": "2025-05-15T12:44:00+01:00", "dateTimeArrivee": "2025-05-15T14:05:00+01:00", "durationTrajet": "01:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 4312, "numeroCommercial": "807", "hasPlacement": false, "dateHeureDepart": "2025-05-15T12:44:00+01:00", "dateHeureArrivee": "2025-05-15T14:05:00+01:00", "codeGareDepart": "200", "codeGareArrivee": "610", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "200", "ordre": 4, "codeGareArrivee": "610", "dateTimeDepart": "2025-05-15T14:44:00+01:00", "dateTimeArrivee": "2025-05-15T16:08:00+01:00", "durationTrajet": "01:24:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 4305, "numeroCommercial": "809", "hasPlacement": false, "dateHeureDepart": "2025-05-15T14:44:00+01:00", "dateHeureArrivee": "2025-05-15T16:08:00+01:00", "codeGareDepart": "200", "codeGareArrivee": "610", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:24:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "200", "ordre": 5, "codeGareArrivee": "610", "dateTimeDepart": "2025-05-15T16:44:00+01:00", "dateTimeArrivee": "2025-05-15T18:07:00+01:00", "durationTrajet": "01:23:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 4301, "numeroCommercial": "811", "hasPlacement": false, "dateHeureDepart": "2025-05-15T16:44:00+01:00", "dateHeureArrivee": "2025-05-15T18:07:00+01:00", "codeGareDepart": "200", "codeGareArrivee": "610", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:23:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsoAoqTLY3Jbu6O2dZlfMAI3PsQjM/d7GlQqPdssKjWYZ/Ijkce6hSUTXQkhJRTU9vzapd3EYXzGsXePYLkd1K4zOpc5XldiYLPwYiYdL2i44mWoi1yCiHok/83voguPq2Oywop9Xj4SUwTBlJ8i2yAgMeu1fy8NewuzOGY34ZFgSJYL1agcwp69K1UCh7aI/yAFunk7c8t84dPht9r8OnzBcBtX09cJeAtc87x4kwwV6nAQKpgo4D+CEbsjXLIivGi9dLAYLcNdoT0Rs3fcvWNo9/xyssc5nkHJrKH57Gx60l1qKqJR/0+CXhQxM3zO20Hv/UeNDPWlbsviZ+idx+eTRyzog5clAVezCJOAmzW2TEtTDCb/wkNmOYvMLPegxLyYVyXAxyoIQ9mklSBncJ5KcK/MnebFpZDwIR06DCBEK2d3w6NoeHOMBAxGZ4y/38FI2q9UHSZFDsO8t2i7n89yUyaylJ916+1ezm84LFe4m2Bj7VqIUEoxWWDD7D2hY0nJD1Lkfx4w81nwVcKHz3gAqQa4zSy74Q5H/4PJ01jVT5dLmbWD6SJEXKpzc7VPffj7sydDvDA1UqdKzEkMz0VPtrwWGWenSkz1jpQYTUr+qQZX9uqyErarKOq9ZDJiuKjtg8yzSV+NW6AxnyX32scJj1CMzw+m7f8bbOoOIljHrEOMqosMj9X61c7DO764ZhOBYbe8ZZ9SPuiKWSYrpfw17sAlL0KYgZu/VaromRxefTho1UITFzoHGsuI5XLPfoDM9OrkomM1elcdQm9g9cnYI1qMgilUv75ura3H60TU+XJWQc/uVciUhqZSyPOxOpP72DyVg0jsItdxWieC9R8ikk2u8b/+0h3dHIQQ9suvTUmZaVX1o4PrW6/N0rSVucKnZZ+u6nWiUoRz5HtLOBTj4bkdpx8Je+Wc/HweuERn+rUuGhKqH7WfRYCQQ8xbpdNouQfzjBAfeyWVtJBEPY3MgrbxGPx7IlUSOwlYhm5YlFxwaBu2jfEFuXPHDAoYMy69D/HsDThZ0SP9NEGkUtYyRRm8JFf48hBRT3jAL6HXPPOO/3LAymJEBo8oWrYjbuIMB381hM8c2lkQxeiNZXtmclKqsigqxcarOUQ+0m6+l7aLGNbppba+N8pUGx+1/ros8AchCZtcMO+gIVxxLr4T4wROGE9VGUaA9TyZLqNBVuw164p3JC1TkEbdZ0VsH0rAV199WfvVj7Q4iVpqwtRR4F8XZxTE+p42SjsLFuHsID9b2+h5lVIcZocfmftk88IfWh5DRTAGf11ALYYzZvC0lE8Xo+kHl4g3o+u3Pxc9/ptHlDcUmlWn5pTJzvc+69y7oxEFlVGcNS9fh0G9olSr03NzF3ni891KIxBxkqYvIA9XAi2CfU9WUWez61CSaq8MA3No9/RLpzYt01wtWkp/W0BdErA8CA8bcdscZ9+rLOvL5V2CWQqgc/2kY+EfV2mcMbsh0cXJ9WcnAeuolIM21BMOtxelHSuWup/7oGK2+3ihltXgBNKr5wFNWjOBtbbpQAMZxDUA1iCZf+1tjHKBJXXtfK6GDPn17Fm2Dpy8sRUpV9dw/eA7FvmcMdmkBMmQRsEu+hVSlPT9DofQFvGPCUPIyyIxTwgE2q/6etVvqw4Lq3QncVy9swXnQX4vXXtKyLNukRBPsRmHaU7ipeGWNuPKlqUwi9rsf5Hf9scacnn5FY38GFn00C/QdErI6Oh6/8PgODFnA2IFHOvxnmJeYuYKefpUms3Ok5BBusbakikpq/SG/f2koIL6iwVryXP4oC/kQ22lbKgQJAntfp3Abfnu3mxoi1feaL+OhgHqFoEXO3LhCkfxjejTyEnNQqApP46unPA6OwLxOovKIzB3LnozZIJfeUmwPQJWKr2hi2e4vewAldevHJ01ud1L6zxYW5CZ/xzOkK3/FNOE5TiGEGv4Z7y2T0Xjj2oyKGLiNyDLxaVFWn+W0g3CYMkar72UxDtwjryV3/Cl0og5oHkSzKQajaSbPwoI41yiNBOg+HZK6gCWBapd2a+Gwh+omroiYW2uq+xywmaJ3hFYIPCPLz1xt5HcsbppIsiGGwJqXfu6/Y/j/k3pYX0+Zs3wL+/vHDLYcRmZylRCfnriRLE/FihKPeJLhxTq6xRiiP0f8chhx9DCQUqJAX0EHDVWEv2r0aI3fvh43UxzI2ltrCS7dpA8yPUPymKZcHCXyscrdUf0y5sCnL6NQ0E98jG04zmhjbmVNWJbgP5ATpquF/xnSyBsfmgQi6SbObNOSZJyeysZ3p0UMhyyhs6NgGKVhkjXaS5gsMNMLi3EdafEdwSVC7/L+jk9lk13K+CVTY3OCW9lqHJq+z2NbdKDRZHtXGKS27WwFbpRxIuLhaVhzWqf1KGD/1VariHkRMWz/4IN+nA8bfDfA4K8EygNVPyGd0S52gIFtWrUKdx9QpgLFZIVp7qSQqu0ZOAPgWYAIe/IJ+NKuxOcoU1FgFwBG0F+I4H6vrLdgRBnsUcrz46z0FiHjFn0ZakRrNs4+PTn3tfb+0daR1ji0We1WLLGaHVmtJuMK7nlj2eqY8iZDTzDd6yT2DG+iHspkkUqFWh/sRrNKnKKUK+qGEzQiIxb5gaRt/tjyqkhkJTe3nylp7B+qBGY90jGxQqtkYM9tLGAIEXqJEQDHx1BtM4YAEzQtrZlQL2EzzV9K0YEiNyEnzyqUTmSCYWRCHgPwUjJJQKOWo+ylZiSMOuFhxAe8f/pnTSOi0G6ZoQiwL/rDITUCHOCrCAo0nXQjLb6xF1d0kn1HVzrqeaodfDUtj2oyAVUusgCwsNKVbN5BZtgg3uztlTE5mguhlJ5lxSQB0A6ImGWsIZlXgUmgfpolMzNONzT87DmhszsW9jRxxl7C7Nfq0rpwSo8u+esBRYi68wDG++GxFMkshedbvC29es4zxRl9p6vQKaWsUaq4I6XuXfLRTc3DMnMHpqpCLqQG052lJKdY371LptIbDyj5MTRx2+GafEYcXFlmf7EnzCT4Iin5K2fv/ztKlqjv6474OQWaaCcw0ImIy9w1FrIym//2GImxNO+8D2F7Q0/SfvYZs00TbBQOBIl5HGV0ipkvBDdkyRCJE8jO4RWXFDTNpVdB8P+Fi4NeGENvAuXheFWyaFmDCioZ7CWceUKGIp9nTFGvMVDo+kf+op+kQ3QV15O/xLDZwcPtBoZW3Z67n46bFu7F7RY34jiH7fQdm2nt4wU8vXHAXzF5EjoEO+3nEct5ZSjpES/sOb0DEvR0CC3z7BrxvmFuVuwqu5452u8JpFJsZUz2oW4aab5L2FxHumxnBWBRCq+2Nx8VF2ptV6msrtTg5vyHLheyjW5weZDBYJEmro6x194/63lDqwwTalfIu+qEnr1w9fApoSciwRkD5AbpW7Qfom5WzCQl5WaqIQ1wE9XTqh4BzYdX/3DAzV5U+OmsXgMoiozpVocEDAzVCogUw73yoVORBuL/Io78iKFDwI6XxPCnbr8q/OD1iBvZzsQiaY9c11DDy2YTjoKZRuIJO9aulREHrA/cVKOUNwROXhZNeW0MYyfgSpe0NsNFklmGhOliADupYvTo5UjXBIcg2Mqc7vwyt4T3CdDNRY6e9cb6HWsaYu+S8c80FVx/s9htSClhUsEyh6QjzWi5eCLLHyV38C5XyZyVoznvZ3DNaoK+wzk3+oYFn5bomcuqIa5K7dsiAPiYiRaIHdTazssYb20JM+F9aFaaSEoc0msnN/2ME+zWid6W7wJVr/XMhXmH+GBsMqRLCtz12iN6xITX8gL8lsX4ZQx3XAfY4AiaU4S07Z9GwhNwMg+tq119kDB9SdaKVoCP4DJLwq2GfwAac9NJS5Kk2sQIsN80nnUGt0E6Kg5OaC0KFMWXx8OZ47uwPjiqIuK8BlZuEN1LKsns0blwGfXakFobPyyiHJM9+NcrPIGr+o4HvFc5WHzxLPDXKixKPy3vAQKR7PPsD9pW2Xx54+H9l71DilmyMS9lRvlaUxKhy5hO3r2gSrdwDhbTs1af0FmBbbjMAt3nWo1pCcguDM/R0CMbaFyupyPhnr6kbF4r37SBwmZLo7dH6ANJLTgHy8BobFqo6vX0W8fSLPyAIrPj/A1wyDdV4DUvTfy19DXQkflqL4Xy9aVI/G+PTjYmvZx47WTDR5EZr8Uuy0PDxnKRKGSwAtMTu9VMUjh8AvW8h7NgcIJGJYvGUIqzxvKinoiYNvX+JRc8mdlEBJtvGD5EmIVlhgcyoKEzFMkPSHJe8TEtBIcwkDBkaWglPj7q/bcpf3EGrkKEcs5ohVuZDREMG2M+Rr5KsWHNSysxcO14bRx87NExYTxzYSpTSE5+F5jdMgvNvmNfut+7NElEzsPsLsXCuV649Jxr9ljTit2T75E1YtDipfphvXojxF94mXa1ExWuEfSWi1G6uMGMzRjnwFZ4rRuJk593XbT1+rWE4mY4nE7+79fW/4B+Vb6rPMKn8OKrNgaynyHs3iNos2NG4DEju9B0kPuyOf5i1vP9d9BtENLS+Dz8McmAh23M4Muh3n2yi0j3ImRLqdYcXFO8WuhiosVL4JKkxllcZDlTzpLZvHW52BB2rZQQ8srI+rRf40FWiCqZ4paeLpEn9kzLJyT0uKdk/yvxXkY5c7r/R2YbsNhL9GTuucatX9EbNVjLdAsPf/b45zBZwZQV/XkvnPLu5WMrLyyYNAtaVafWd8AHEmG0ByivXJ+VSq2C0gX0ZLD5vS8NvsrH+JCGqHVK58RJC8u5GqdoCZeFL7j6vZ0RFu2ihzpYzjafEKholTqqEuvnvHpxJzgbuFf4402dykrPAuhUTuIR5fsHjux91eciz9hlcYJ0QBaoj2Z+Bw4Sn/RKaJCbMQ5L6YoaL/dHBF/W/xQCORJlUw3wahoCyFjepRvn8D86e6pU45eOXjbEOy81ni5vAF4lp8A2x5ddK979j3bVOjiiyv3IQBveh27+EbGxcS79m0cTSG48NcyAz9NNcOYR+KCJffnqH5hXkzMu0/KThmPXKa3LKZIfwUE14dOfCqMHBHQi6gPs6A2qbZC1TOqpXnOcAC5LZcLmve4VGjmaNC7DYtmo1aVgDJbXLmsuNjxKdnUHZxxG/VGnSocKwDep5q5HthbmqFr4t4khwZCEGIRqO3ynfn29I+WnV2+9HMGcDJ5LSNPcPRPRQlf0fbGetoRipnxGlZ4eklLvC2GNwAnyNv3/l38GlBP2ZRxNRfLdWDCgnTUB5IH5yRpR66Gz+DSm42bTy6ir7pZCeEQ0ggV3PpDQzwTS3Ko4xyMHTJlD4IxZmi+obsNTcl3mLrE1HJgvkWl4fU4SUObODRGOav3uXuZyg8md8bcuzr4uk6d36WEvDmC3eGn07gInvOSFCfN1620w5NqolFqRR3BFu8AnWfrLQhcLqhztxipXxxe6QnUCengVSiT+yKPq9qwHLjIbiD48Y7aO0ICT8zvkab4f3ptE4rgVWKCl489xm9HVaTLmTk2HX3SUHNfeV7Z4VOWq48LPZStapUniJKIHM7h/HgRfsVmPCFgkePmTACindmdi+v84M9WJXHunmXL4QC1cEiPkXDDhse+396HeuhPBt/nvEYeuiCco9NmWUaEVpEhDRhVmm6H0Ss+BgiFhWk6Ugn8sW6a9YR02B2q1OE+gpMajkalHbJRCqOblhL5/h3v3axNrPzBkjmy3tbPh+eKD78AI6bvi5uA4NiuSHemGbqYqC+/ItHhvGDEYDlsKMaBTFvYT9Zc5KxO6HorTiFfSMz2c6Y8huYBqFjr22rHEitaAnbApMz2Ea453qSo1lKapI0ghH0u6osZwv4/2JWkm2/pzR45A7ef6kiW40Uff4cJM26NcuEaN+wWEs9/gEZ1x/PNOCdd68aakNxmKzz5yhimHDLsg1CWQNrLlByhG+aSMVxTIyeQk/Ar0wskQtxY9FmzA7gjOuk6zJwfH7JuX7eOHxQRmElhePMuHzhFNYSdt3cvFAvqPJGXBUrQzohWrsZr4W6BXsfJuzDkT1mNv/tVnQOw3jbTK+vZv8OtrINJz1jIm56OdP+2aoKJ7iULKMxRVm7ajkgEaOSyBOOTDq8g9NqVQ61IffVDqzw7uDv0FFxzt1oZ7g+qIJrLD9JrNfJQaj5L9gThGu1f6NCaElrV8kAu7miomGUh6rlGBxp8ZjGjr+WjBQuGr/XQ+zXNd5FUSVVjDAwExgWf8MvZ4nS/sUT7nM+f1CGG1EmFJJWzTDpLP9ujn9L/hprygGK8G99Gv5tCqiepyBEiIqoIXTNeyypj2CRsF8KemVmnkWeva7QmMfwNexCYhAoMVlU8dTHq8Ecfdqfia63kFRBpAfn4q9aeDRWBcz8tYWCC5HvGEiI3Qc+/k4rVDGIlnUegZqi2NOn9RztlsxlXBuawfoSxvCfSIZwwV6NQUi0oNK07Gv998FeGUJj5C8hI0SNv2V0knK+nDBmjyW3ugJ5wsjkUz7/kxFZze8Csu2Wq8cudY/0sVHg9BGVaoUcIqw5foje53qIPJmuuILQGxTJmVg3j1D4FO3xsQJkOYJtD6ACDbxzy1D6z1PaY6MSKbMacaw6XXSOy8GlatlWYES3rI+mG7BXKh6EZgOtF9gl/B16dJooI6lp9Qx2312c8wo6QOF9zziWGfxbnoK2gwsXjfBZ7HnVlb32lFJWwnrNJqWYGHKX9H9QzUFJXN0SMBQdt/wNOKFG9wu5XRARS22uWTzbU6y206kuBxCawdCmVm9yKq0GumUetyWAvnmhKw27STYW0co0HE2crTJ1/APV2CQMxzkPdYdqJ/rc6GaqqXPfRxgpHkPDIHJJSYYO9Ey2BUSgdeozXKdqTkwff7jOOuWTdt4a+A/6n6z9lMPfwVYznImKlWArpGUKr49puiFwejfMHYBTcER+iaCgxG7Fs4BKt0WPRY30HzCNWrXnr1p3P2Jb2Dpz7iBAEv+soeIlwFDXdn7BV4rPXSj7r4C69YlLd7sJgu3AL9kAyAR4Tl0ALI28FJ/eq14wtb5x/R7lpvrmcu289gI7tCh3ouj39n8tzeQL3HlbnmJB3vbdn7vJBvw7ZzynEKYDczheRlU144YDXrhENCXVYHlis9w0i0CT1ZNLqylhj1QbL6dZh0Oq88nJFxASFL2BVx3LTjR0UIvZVyThPE65Eyd7i8imG+SiwlDygTEHL66ohUbTE+jcCS+QDkkJoUgaXencVocml/cD2Dpoo8Cy3IT4JnizisCGIOYTY7IF1fqKxklgqjHkft7sodK6UmLAB23AGt8jlMpVsdNG3sPzgEohOgFD7vEufK9lfanBIBPAFsC7brPX2/M9crH+WRvO0jbI+oEOraxcLj5Be8lUcZfqhtxquNhRGSbBV5FfS4GrG9jXAkWcrPRh8ESLOCg/qY29iFLJowu2TSczSANzoddKahZwGhwHNZKOZ9oHXqyu+aamkCBunmWga4lD7KABf6EimOU5e0FRr+H3oRiND89Rx4VP9YF6Qi4wz96NcER/gBvKyIWBW/rnBFph+nYDNqFSIvtX3LJFw7cE9f4M0PvgILWmm7DmhhfFyn64TAaaZz80XY1Wf0tah7U6jGKmSKZ53w83d1848W3Ti1aqs+N735NwEo0NoqShLIKkGBL0zbGWZ2Q9FPY2tppwxC6km/ob6yY6cEW6ggqekhuKZ6GjsZf2x3tcB8PvZfGfDgVzTfpchBVj1dTGJgN7guT4UUtiry7jw7TramEn4PkF+kaLM82ngshsVq1EKiWzlWpkuoIVe00huMLLmWD/2AOVpDDbUx40BXgX+DtTbpdIxRx+mj+ZD0FlFBb0LEsr8fDD9JR0KYUOVEvJu3jGemOte6fhZbSAO9YuANPCPeCno864GfvV1781D1XWTnpXu/wwHd1WbotnU46Pj7FBw4yl80zvduGep4EJINDfvbKG+Sv4IEi7sZ2cbkGWJttyZw1hScGlo38/Fj+k8JUEnjO0aOt+DMrqiM3L/NRrede/6eIhB9R4Y3YqOvUmkBaJOPhY2O4W7s3zG2Gwdb/8R7/8sBwm8QTaNHKFn2rvB+O5wQADfZyzcuuDVS1TzJ2u/Pq2JsT5WVInerrPwI7+tBNQTD5/TejgHJ4CXYkAOuxGhNexOXJ845CfN2jLuxFOsdOOWvqgwAPfkvSyDECu+luGa3DbBVUBLvGcH3rCW9Ii0GBYqYwEdKmiBmiCKmn2OXbNz32HjwJlbwTFKO24ZRwVCUaJEAvVvIPUp/D0iz+WJRnn/mdr3j+gsNwbavLyka2tHrBOPgHvvI9DnjL0YkeDCyk44aK9+axz5o67+0L4T9htStHa0MJspoYHFhd0ugSTlEs8YTJBhAV4vMOszXKX0M184TmycshanmfsDzqYY9r1RVr++lXx1TzIbNtOPDUSKz3tScGa+KUSbyiHa6ZDvpJ93OeP44LhW4XlXwdnlcJk/u/M2HnAJwjFuwHS2eW9KOCz0K5+T5qFH80nj6XofvB/pzM19Jj6cxRuKIQtZKnKJ+0Lvm0cNTyvBaHIvjFhMtHfEuhMaMqxQRtx7tq1R3LftDZKeChEZNrxF+5fNEkOQRbxoPCa5KIwZp/4etQ+0tPK0sRLFUw9tbcxxJWiN/7UXgy42aBVzUHVKt6rmmmaYwLKJTiL8hHbEenJsMzcUzgrNKZoHiLjWxHHZ1gSeVxcpZ0/emUz9ECyldyhCpNHYT6ZVONPy6n9rXqiIS2n+T6qUkWipR3jW+meM7YIRIQzHJw1kG6eMFZuru8DfdX76ylpMWwg8bn/wo/Wb6z2hrSTxhDr51uvGS7Rqg1OJxl53XNgmZ4sLC4Sgfzlw96X7ueS73dgJC92GKVaZ7Lwq/DA14gqzSs3FbRIKf8sT4tY97qC0ekZlv5qIV91UPUOad+PFg03DST1lL4mkgd3IIYd+5dsDRqgq99T3t5dnfewX0fBcWrDHk44uWoaDU6xwfVxp8dNMsRdUnOADWTSNeTmWHVMxWdX/Ku0zK+BaS/kwMc/GLjFNDXhoko+ij7tkuWkJKiTMxcJQf2N8i7+B42fIdgTwbnhecocsbQW/NluYh8NI3sau6I3k6Sa87IwqqACUt9LMB2wAvo/R9r7NI7m7K0H6Zndeawe4afXrGFJxn4oU7Wj/BOg+4dEmFjWpto20gZfFbRxGKF/nlzGHV+TtdAsiwVnPDGysvY8r6VMALKNXHmUJebR8NiAyEnVXpMPXknq3pPUSWWiMKFaaud25kvHbVUf8n2pV7BiB+4qGSLqDNlah7TsnTe9Y1vHGFrRS+deodFYAYR6tzljZNWIUJPH8JrsQBBReuBVRq6QrMXdvtTSumRdSyJsXYgup83SU4lhIipT63p/5jnWNjQhclAW0knifVySjukOYLzOHTYehl+o9tXvqwQG+igtz8qcaa1tXbCfFMtwGMLUWSWV3k6gG0gOgiN+QWgoVCsCs/dkYQlh+a/fEaRdHZZWBCk0Gq954S4Pgi7xL/mMAdvvWRLWJ3RMOrW7rxBN3N3HX/fDCXouiC7KyYU1tDUhoCdeQSXKEB9hCMbV90evfUL2zXHDIVh4PIHsgXPWPkAZe1ymcFG7XGE+SLghDy7BHa0rUafmAugI9kqDqiTN9kpHCXzdFrvSB0STSrEGMVTWw1128sG2Ds0seSghwgg6BXi3xnu35Z5UW2KUFBbSJaQoQrDM2ryYGBWArua1oUVz4OK+ZfstWCnLIQW0MxW+N1xcBDYeUNVqFnw6ETPhgOiD0AYeYdLgma+hvLoqMEq6lqydB5WVfh5G2q/ZpB194N4Gz7lu6yDV1SYkBkogJS7YXiyQ4ZbQBDJlLgW3St2d9n4qSPSWqNwXXaTQQGrc67bQvczrQ9DY87qoFS3/Rkw3IKxeQSCqh9ipboOCsineKOyE0j5AuTc9fIy5rLrna8vqsPwOFSHsrTFFz1wTCRGmkqbubzzLvrlr09P82j/PuwyHhMn6rCGm5nV4ZK8A/bmfT90z73m4GWsVFTgeY+NZJW2MqMflSE="}]}]}, {"codeGareDepart": "200", "ordre": 6, "codeGareArrivee": "610", "dateTimeDepart": "2025-05-15T17:44:00+01:00", "dateTimeArrivee": "2025-05-15T19:05:00+01:00", "durationTrajet": "01:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 4295, "numeroCommercial": "813", "hasPlacement": false, "dateHeureDepart": "2025-05-15T17:44:00+01:00", "dateHeureArrivee": "2025-05-15T19:05:00+01:00", "codeGareDepart": "200", "codeGareArrivee": "610", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "200", "ordre": 7, "codeGareArrivee": "610", "dateTimeDepart": "2025-05-15T18:44:00+01:00", "dateTimeArrivee": "2025-05-15T20:10:00+01:00", "durationTrajet": "01:26:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 4309, "numeroCommercial": "815", "hasPlacement": false, "dateHeureDepart": "2025-05-15T18:44:00+01:00", "dateHeureArrivee": "2025-05-15T20:10:00+01:00", "codeGareDepart": "200", "codeGareArrivee": "610", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:26:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "200", "ordre": 8, "codeGareArrivee": "610", "dateTimeDepart": "2025-05-15T19:51:00+01:00", "dateTimeArrivee": "2025-05-15T21:00:00+01:00", "durationTrajet": "01:09:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 6456, "numeroCommercial": "V10004", "hasPlacement": false, "dateHeureDepart": "2025-05-15T19:51:00+01:00", "dateHeureArrivee": "2025-05-15T21:00:00+01:00", "codeGareDepart": "200", "codeGareArrivee": "610", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:09:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "CASA VOYAGEURS", "to": "EL JADIDA", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}