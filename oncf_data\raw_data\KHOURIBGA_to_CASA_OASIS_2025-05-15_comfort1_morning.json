{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "167", "ordre": 1, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T07:35:00+01:00", "dateTimeArrivee": "2025-05-15T10:04:00+01:00", "durationTrajet": "02:29:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1168, "numeroCommercial": "900", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:35:00+01:00", "dateHeureArrivee": "2025-05-15T10:04:00+01:00", "codeGareDepart": "167", "codeGareArrivee": "191", "codeGamme": "58", "codeClassification": "TL", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "02:29:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [{"groupement": "1", "listeCriteres": [{"critereId": 1, "designationAr": "بجوار النافذة", "designationEn": "Window seat", "designationFr": "<PERSON><PERSON><PERSON>", "isSelected": false, "isDegradable": true}, {"critereId": 2, "designationAr": "بجوار الممر", "designationEn": "Aisle seat", "designationFr": "Couloir", "isSelected": false, "isDegradable": true}]}], "configurationVenteEnum": 1, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 64, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 64, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "167", "ordre": 2, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T11:30:00+01:00", "dateTimeArrivee": "2025-05-15T14:01:00+01:00", "durationTrajet": "02:31:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1167, "numeroCommercial": "902", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:30:00+01:00", "dateHeureArrivee": "2025-05-15T14:01:00+01:00", "codeGareDepart": "167", "codeGareArrivee": "191", "codeGamme": "58", "codeClassification": "TL", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "02:31:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [{"groupement": "1", "listeCriteres": [{"critereId": 1, "designationAr": "بجوار النافذة", "designationEn": "Window seat", "designationFr": "<PERSON><PERSON><PERSON>", "isSelected": false, "isDegradable": true}, {"critereId": 2, "designationAr": "بجوار الممر", "designationEn": "Aisle seat", "designationFr": "Couloir", "isSelected": false, "isDegradable": true}]}], "configurationVenteEnum": 1, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 64, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 64, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "167", "ordre": 3, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T18:15:00+01:00", "dateTimeArrivee": "2025-05-15T20:42:00+01:00", "durationTrajet": "02:27:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1166, "numeroCommercial": "904", "hasPlacement": false, "dateHeureDepart": "2025-05-15T18:15:00+01:00", "dateHeureArrivee": "2025-05-15T20:42:00+01:00", "codeGareDepart": "167", "codeGareArrivee": "191", "codeGamme": "58", "codeClassification": "TL", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "02:27:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [{"groupement": "1", "listeCriteres": [{"critereId": 1, "designationAr": "بجوار النافذة", "designationEn": "Window seat", "designationFr": "<PERSON><PERSON><PERSON>", "isSelected": false, "isDegradable": true}, {"critereId": 2, "designationAr": "بجوار الممر", "designationEn": "Aisle seat", "designationFr": "Couloir", "isSelected": false, "isDegradable": true}]}], "configurationVenteEnum": 1, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 64, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 64, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": false, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "KHOURIBGA", "to": "CASA OASIS", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}