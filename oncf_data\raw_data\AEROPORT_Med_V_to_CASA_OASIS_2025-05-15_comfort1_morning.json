{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "190", "ordre": 1, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T05:56:00+01:00", "dateTimeArrivee": "2025-05-15T06:19:00+01:00", "durationTrajet": "00:23:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 6668, "numeroCommercial": "V90004", "hasPlacement": false, "dateHeureDepart": "2025-05-15T05:56:00+01:00", "dateHeureArrivee": "2025-05-15T06:19:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:23:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 2, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T06:50:00+01:00", "dateTimeArrivee": "2025-05-15T07:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 766, "numeroCommercial": "A8", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:50:00+01:00", "dateHeureArrivee": "2025-05-15T07:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 3, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T07:50:00+01:00", "dateTimeArrivee": "2025-05-15T08:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 723, "numeroCommercial": "A10", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:50:00+01:00", "dateHeureArrivee": "2025-05-15T08:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsLc1MvQi/FgAHlceb4CuX2aKjhBQmMTbBz8+6bikgFwDoV33hXV2+WgC6w3+feia6ZMuhSgdGwSh1zczka2l3yLv6yooB8vWtDoXMtAx7FBPu2reKkgqonbyCTkcibYkeqDHUNObUHGoBo18oKWYOfxA8uBqpIqzZDwehCQY5ZLLuPq06wUStNRh7Bk+7K6rEa3/mrghguKPmNzhRldIWUuluy0oiAUCbFAw1FTTOMykhd5s7VGSwIA6lXHTIwkJKrwcmqz1iZ3co9TNFzk41HepkZxCIXb7gZl4QZLco8Ev4EkCW1717GF4pp/LC6h7r8y99nPoXBFc6vl2ZfLL0yePBk4fsurmoCSNjhO0JQ9DCIzvLNOiL69XpHxZzqCRiC6LPYBaLvvSxC63CbgbeS2BLv2BQ/z18zwVhiMPBOD1QwY39dG+qP5Qxkb5vNmq7DQGZqfseYQgwtwwl/4AsCtA5tTl0GDwF2uzRgCXeQuu79uxYu9oTZeuU51CFWfq7CW4/j8nYNCOPSkT4b/eSCk5Za1mV6vDDd7VbJO0x5/nju38CXFf+w64NHEsjH2UIqD0vyJnim1Ojn8bbdL0yyVh1bHlij/fQpvflp7d6djVttkwiNepGoVvmqo36iQJwhEtF0PnStzCdIuTvHw61I7OtAEyTaaB/rN3omsKgCcbzIe9dK3k+Ysbouslq7ItyWEQYyyIgbn3EaaoHQ0XfQPyiwjZMFzXqtHxJOGOxwMXmj4oSqBMCDKmPj3QNyqQ5+PTbUi7g9KADZKf+sRumQmbKt40AmewcY9Al9KuvAvTzZL680SbnYLP9OxfOsKA+wPjGLwfPuLmFvrY2EixJnxhfcbYv2hzdPoTJPy6agLJ3j6PPjBdW25eTTL5PCm9/6c69LhoW1sCtUGH09Op+StMQlQ3BIo2bBf0pS4pGvDNL9oQVqnAoaPciPff82jkR50xfKvpq6YK6nVrXZVhz6k1eV2GuW+S52Zt8Fd13tK+GyEe/pQzocyNmubxZMGpULygDvFqc0Y03Ahz7G4VQWySR5CTO8YoMhiE8nYUQcgOYdGOGA16ZhD79wlFr1bEAlZLSaRwKNLrH9W0Pa9vga2BMu7JqJ66y57kFK1JzsKLwHp5z1ktndOtmPFjJuHaoofYXnPewGMQEBKCL7uk6+1Fj0I2RB30T581gY4VOKahsU6ErBZd2l8Jijh80UWMR8J7ic83x0ZfiQYqPKJb8v3vMXXYCMx3E/RsYpChspXBNO8EiQBolX8C7umyXSPIDdQR7i6FVKyFFDSfklyvLWACjwCMaVR9gQl3ODNQRCnq2f6/8MQU/QG+HTUy4Qn1vlCct2Ontwif6bIK4xL/bEo9NOY1FfXDn8vl88wadLmyorEBOxgBWofTQBE7od+5EEEZT6RfXznISmnF+Va3fplBexsxDgfGsKjf/J0YQpkwZdpM1A7cUWDo6GLrJVaYxOzyh1VD1C96+Ti7JJTpQP4D1hdtFRTkQdQGWTW281hHNsiH2Ad/ApUOsBYy1kahJiYBT7OPvaIXdDOXtIzFI3t72f07MgRlVvS5+BG4nuv8Q4y5xyWpbz8YZi0/UVElXFzqw14grE+4kKOEPwrX6HeNM+p8SZwKciHBYv4lZY40Hf3IO9NSbIZ5efr7caMxai4+7beAbistKxn0FHxYuWuQHLgW23GVA291fumh6OvQUMkw+JpzGi09D6y62fy2hUyJJce/7Ofbh3pAYJW62dQ1iw1Fa1w5C1M/FjXAeAHhe7Gb8w16gkjkNPq1+jHhB2w73rR4j/rPer31UR2XUl+CbtSduPKXK0wWxy7E9d7ZjnR8nbpwLpiOjusWk5oguZQmHi3eS4bheh4Sfn76VRgW2V2+a9sAqUodsogLAkA5ARhW99VuY93+oCG0+dBQOsufiT0FKmaayOiW0WPvnkzP18DbHzoihbKBqris6FURB/PTmoR0RNxVNkK9BmvJeoGOxmdHPNLlYR+mxbcXDx5l3YUNop0YD0wjkT9dLafqkmRLY35CEhLx5Jg/5OREJCsDr7/LBbvHQewuTtYcShYwaCj+5r+S1fRDeBCh2UsGRV/0sl5TX8JkHNQPGnix2L3bgzjzfyb1LXEScyekRK9G8GnCFaj9n1N3TX+nJqHnI/VRvXaNIIHyuVa2MCCZIaEdB7F8lGnv0nktG2LywC7MrhbnrhBajNh3cGUR+8PVo6Mb7EI0tM5YfzscNxzCrdOkkUiFHH1K+6wqaPj6DIHOIxl8Ys3xlG70ANMLQr8GaJG5VKsOmdmE0coNdsFolg3+sE+ogmsPAZAS/v794MnVIXC4hsdph3PpeOu+bLuCDpvKVePWE9Js1ymZMXZ/5KR/JsC2QwJs77TASFt2Ywuwn/c05Or8RjJ1DOO3xoBcFHAdSyuzzAUtR3gafYf8IlAqnUwwFPNq4cLkNAq7YfDLKk+FKzoV/s/PfokpBZQCFzH/ufPdaOfhh/OMvweaAmQW+JlPO9UqaYi92vsIQWy1OMli8WxQaVseRL8z1JVFqQtscIEQNa+b0BHWgos8JkMH1GSfKjN2Yq3U2gSEJ3lncgQgtsXfdO2MXn+rnnRM9mGnU+axRElJJJ0v3ejbMCKM4UBHEXIvIhJkO3RhEP5mgjtkmpwU55u97Hr9vhQ9wusdSLKVbZ7Hwua4NDoiWv5FXA1p8kW+D+pHr42TeMzN/tiop36hfJnW1Y0a4t0/5AeYTdAQUaRnXvW4JpoNTzvUSHP4HOlH4EBSGRvcQV0MhRXjssM/D75xKPX/tB1L0C30+5jZWsIA8NhV79432S/sK1ViAAasc6bdG7jooT/XPkfzQzkSJx4ZvYxHXQ2M1voAFLhzioyyp7SK4h2dN8K0lLQ1GF9R2pRisLSFL7YsO21NMsrp428JA3l566zRP/0atw4F5aE3wiOD/RuvuwvjbaLqB1eG4ni60sr8BNItbC3ByC9JdbUA22jzfjWVsf60/VDEn2tGOPb1Z79hCzFkoaoG1zVtxFgM7+8hbiINcCb6I65CQtQWOAFz18EA5r286rhvfqXeafWIAovYvkNEesuxxVeLiQp9UHnHKdZwG9vHFezMyPd0sH6R8b0acOZcNH3H3PgpBwlzJLMFhC3uiEg8pjCdQUnUSYWZQZggU8VfZLvko+W813LdxxXxr3ECX48PhIb5QveuQ3SyrZlFTV1vatfgyTm67392ZCJoRTTw+VQnCLPCT8bOk37VyjNZtJHyyXE8MWkMRTHPQiv8VSx7ox9beOgeQ+kcREJm8yiEruhe/LLLAf4BG9Mdj+Ih39beKtxeRRh4ZBJHVbenG+5W3kRdyykivB05fynqbazJf9bwq1yMdm+Vs5uJ17Q5wBXvRGyzmmJ3QpAfsd22Fs8pqK7cDjp4XwUkYq9hBAean8Qx7SLKbSMeXNUqNEi6PM3EpyueWh8SdELl8pPYbiu71taNXlpJcGDWe1DOK93rt7CxsDaCXlNxAHNc/bPgjrscn9Sa3TM3KmFci7a45ni/zc4N8Cg7Oke5qiW9esUg5IYb+Jaeln+NPwQvf7RzRvfsWRXtRmfnG5by7MUGqjuIcHJMBkwDex5XKn4fBXmR4eiwb0Lvz5U7BeuJWVL0rqPgTgw5NxrtKsZDSx/VmsC3FmlUyfzu+XUNrsshmohCoGpkO+5XL59XSnOv4rE1S/1RIzGs4yfMcchxMxLUiNtflCwDEt/KCU/SH3GAZYQmUgkP8qXmaWuKMJ9vVRZVUU+1DIh/0G3z2f/2TV5DlRYsTQojwaMVN8tg2MvMuIwIpRDC2ucZXgGabHYM+i2SobGcnWWImIn1TpuR8jlj6A5yyo8l0IQ7GL6j6s1AUmGjgaPnFadsEifjseWf64ET3Mv1L/ZODVNuG66dEB7rY70uFB7xe8iB93hGk+MrTr+Af70ePLGgB4iR2edz0L15K5+NswlXMla7ScsPT5yiNZkGzqYLGn51EFc1E9xFEIJ6HIO+jvvZfZRyJuOfonM/eGUOwOJxGjADTLQxWlk1NRonSd/pzKgG1UhYfX4vFfEnhQWGwW2zhZNEo3/moLSTazAfDVLPzVATt1ttYqcoO0rQtuvpEEGiUGU9XRzYnikroS7ZD+jP8XK0vPXaxiOD2g3tdb7WJQETKloTeGscOdRlG20xRHSupg0X1KjjERkJXHRGMWKwZvKphA1+DN5kHLi0wiZPmkD0//kyjroUOamFUIpS1x0Qz0IxIdig8M/8yMIDZmCo5iV29Kasvn2WWJGQrQ3e3qbgW412Q+nSO2kC/dVgGpUK7qkrwk0XQ3GYUwlpU3MKQtkjiHlu0M+F5Z7UEGH75SCXvJBKl1BtEj6RQrelMxQX6AEpYlWeLMkOUt5syIZ431t8aNq9p7SrDepUSg+CXmf1ZbPONHKH86fNkzx/SKP5zOMWnjZdXq8HR92/lN+j92g72VgiCd6JL04MQZole+RrFeFUGSTv5e8RdH9bGvLT51aIoyYWepJywJwoFzhv4nswGB4UwQBts5dpnVNgAXvNjz+NX4qxKPGv5kU5MAPOVNCZaP/P1xxH8mn6s8cgez8sin7xt1aTnF8V4sqAT3//Vn5TAAaENUqnRxYjBs44BxeQy502a8fyjT2sqfp/BS42WsR+z2jbCki42AHdKnif4FoXGCXEZmwswTnivi1u3RUuJRVO4qStDhQxVuRcNxRuNB+whzXA2y2w6IyWyk6RkScCfb+St/3wu2niZBs25/osZ9vn2EwivFS5kBe/0dOUbrC5g/XRRxgSoTjB20GBS/KZoGPzYOTrRfZAx71IKg24CFdi17mw5uwlJH8aGML439SNWol+clfGTX1hyiOQBRxTec4L8r8ExW9tpB8HbUpEuBJQ/5ZUfUQD21E0JCTuFoOAWKvwogBQWQnTTBuwioqYQFJYq7YhdQHiPzS++8UxAM3fEmZL6SCJw2qO+6OGFYgKmr3fxgvWwEmW/ax0HzetdgSp+INgtbnLhtvtFv8mDjC7VC1Cjk8fsr2+dMkssD/DvCgBaVhO69T0FPYQsek8vDPQQ+3wD9EteAeYyrZ/tCathzz9JiequVUztg3z34M5ub/DSlGHrN4PI2wpeNOZ3WNczumU7vyW3792i/wlMuo9shtjU7qd5MdlXMbIEiGj9MBNm2pD+GIIKSHJleOHf/u7JRCVdk6X3IEouyoh6eM1SuPStn3o75mXxZ3eSopJajJSpmH22GdoG+YRDFq2OwKmAHbSN0vtr1rtwjMiLTRTZraA2ZNxag+BV77mvJvrokH/TN3DZeQyuSDD+Vht+2oEkzAeHot0flJEgiS88l4ppBRGM8Nyf2dpGfzojfiWki3DLThwNZAUpmmuNYobopc7qwuA18IQNtR4+keZyClw70/LbcMI2IdMV4WFLoGxZBWRRrfV31sho2r4DfEFsYzRCjP+yQgWkBH4m4ebwV2YHCD70mCQpMkYNL5StP3rLPESoSl+m1y9Yr3rXeLBybIB9rpDRdva1iWG+jBt+rEyIfp5RqAzPZYRnJWZA+157SjGUdmPj5fX6SaLwgjwPRBomcSVIm1NSm25zuZ5Utgfk95UogfnZh2bVi8hP5Khf3btsDclX+38yu4Oc8XTC1w0nZITrCxnUcGTBUxWy6GJ2BNIEfGszQa/BmMgD/RVY1CevEoRAeQiif3E9qLWWA0assgf6WgWIHEjfww0L+5IKgm2cxlOUu4/Agck1wRr02iqGlFwMw7esh4f70bjOFgNXY8OGYL9IFs3Jwi8jDP0U9f0sSaUsQaUZof521rK/a09kSohHZIkXWGSXhsKRrrcFV1oXbB1hi9dwpjX2j7qlSg5T6YsErlpKx0FkxHdYas8q/SCm3uDREpxFgUPNJLtkDHmxzhKqzIKKKQVn4tN5Knqj9CV3UFYUIHr4iQ7zJW29OJ5jrY4qkAaWWeO3UB/zhOwuAI3VhdFUnF2ocVcSeNEaBxdWOA2rEFzJ5ptPoYEgMYQ4xLaDQFVabgO+Ck15qvW/tSwQUV4IwIkPZvwqx4C5ZOD7JvfdS18zLNYxW610YDYTtCnVF4qj9eBwUU8dxf7jXz5gnSMxD4F6NgOsoY/WvqjmyEaH2IbRAIZglQ5VReadc2il/oDTj/GHF/UpQX4qz0jbW8XXG3FhhfORjgu+/XBHrBhV1FE6CPzNfcPxT2oJq5jDhldtwxdGuWjUCye33J1wVNLgWn7c7mLQIMNVEdZWNvo7MgOqU5kijIpGUGbsoNvj/fm6Cbig5SWeKOBdBLDFaBi3WURI/8LaYpqmRWFbHvGocNWxDKS/yeLY8iPTIRzmQ0ZlqZR3Hm4qzWWTlAZhBYBnIYG+MvgElVBF7KkAjGfLCEnFHHmK7o57nNYeKPuQxRqZ/FyFrwDCXDpX6m+f+ga3JhJN2uXob7JohdKR/9xbdUTAknsR5hRGc3OzvLaz0O+sOTXQMNB8YvzYMVH0VSOqb86yqgTUNRp3bJBVhHq6sFXfpyP9zOsrnNk2k/r36owoXP1JfGcHShC1K+DXzqThmbhzh/lrzJWWf8mRzCBi+69xhQBbiN61sRKXj+5gNaYvAV9eCR7Eden1J1XJhYwk2ML1DKv13/rtphzz2MR8wtHW2IV71YZCxLRla3/tDusPnJpck01susbeaibvEmvlv7sbWE73YLD5B7Cm2Nm8MOEkt+SFEUUzJr8HLNL+JwgoF0vSne3wAD1swYCxArjEGoU4Q6XHWa7JoCo2btvByRZncZgvBNHgV96fU+LWZdpm5wWAW+4iKw9DEx8Q33LStmPv6Sh4VMhVKBBAJhAIQ38MAejtLdAFoPlGDKQk31M/WQ2mJ6p9ufxAjlu0JJOJTaq2OVZNMS0tFxApv8Yn2Eo2OogaoT0j3zJaTcw9cvuXE1R2jL7h/q/Ne39mQxqipQIkm3XvVgX7yUtU43m9UcOGdQpFfrjRXTeXwVb3GwIr1knux6FApXonmPMRlaKdAZOPQi4tUyppmo2C+x2MkgoOTs2MLiYXlzvBEx1TnFPZ9e8SckZTTcE5Xw7ZBAvar1m0oiCk0vVJFt3q2EMB64WL+lir65+08QF5Lg6TEkfHfJN9K5gOm2cRhodkV1RexjyGfYubqYw4TYopSVwfrDtde1q6X4Kx569S4PxEqyE5PUY40xVz597Qgkw/ASTOHCDjZIeAvFvIdZMd+TAD4uH04pRJ/011zLpcDBPvCl9G1HC67aiTIbNndKeGzQHp8Xjmuw8I2iOleJ7pV8jZiU/XnLabvXo8F/m7AwbAqdlCZPGyplpg9VksCMxPPHAr/3uc20lY6CdgG0h2iqXo1U/9jWv21/rCpCaDVPGZt6OQaxccAODV/F77EWtZpXFInAqwetkOWWzSRJzauzfH9xvFihfz5fgi5sj6U/ArEFYymwrIfqY57DkFKsqqIpLibDPew9dqC0VuOIg5afBGs0rUu/Nzj2zNtDIm38MI8TNAo4kYOiYxrNdIB4U3kVlg7BwW54XXPEHDPP3pi7W47AOmW3zkwG0NAVf/s1LhH19PmiEZBDUWsS6rqK8qUBI/MeSKAjf/uO+VKgoju8Kd7NGIfuarjphDdoBlrNaz02U5EZIUV8dtpJ/lk0LeAu6H5zJ2cvo2Tj9KMFy2/VI46WZbdYK7vxlPqdCxyCGPQHIYld+zzZpX64o1lDSJuJAeEpTU8bPh+OvMXHsPX4j3kWbZX+/AWZ6EI+qzlC729RvyJDdYrPIh/hDWsGskgLfAmnsedMDgcl0ecPPueZh9joql6CEcz5nPfkBEiSZn7NTALu6m1Rt1BjyaDkG6LSsVIusE0A7fkbq67bPfM5qWZ9JWsPSLKRwz2XobRUHDfP9H9u+sVasTrYz0zdChT9CWDgjHPEOXYHoPHGHRKGgC1MKUr2jSgBBlSAFZCxTcXGVmAJqkIsdoc9W2LlDXKMVW0Xr1rmjGqsNSaMmNVtt4jSfgmqr/lD/8xYimfNMDml+mUINMeWpoxwvJLZRljQZzXfZEwMewg3dmlxzgdp1AHoi3HOxLKTC4X+S5Io+GghK0LXn/T/IFnooE50QxBvdl1NFsW+xANLsstlIwyKiKfzocEeG0gxhZvYtmubDV7cwry7iwr2jKHbNKGvv73j7bLd/DPwEvjth9vnEw+ix+FocBXO0QxJDoYWkLy4j7qXo1aQvjUpqvw6onagMBIA5I+ojaHnIWKgJYnBDD27JIy2yvWuXE9djZ6AU/4t+vN4/YcbRn4e+FfwIHgqLF9bsd0z/nZ3esjCTtwmrbnfJ2kqrz7wHZJnl75N8DFwDpX/kYy+oqZsphDTSNihjID84TdvzMFTI0qdQjAHZxgJ4gEjKrYAvV44wUnlNT429fwvTRSmYy+T+1FyyFY+aj+BWDHgDyhxuLUGPCAynpOmEkdJkRDnpMAH4KFpYaME3Npbu+QTdwUr4TCt1UlV8T1cRKoGzeqN4j8Pjm3Gw+jhUEd+fg5mNqmCcVeO3i5FwakjND5PmZVVW77QpiUiv+9bxY+j9d6C7SOg2cDfNuiV9LIHVe6geZuIG/pKgPVmv33lDd2XKpi4itxzYrR/H577ffLSRLtMtALWhg45VJ9YqhUdAfXxRKafva1ZcvgCmOorMEJmQzauyriG3PUDSN6J2ri5hRKCaVSgfGAuYS/5xXme0CHGLFqG8hNn8Fxv4AMqPu2HGy+HPXeehDdYC9s4KZRIy2o+paEN0UGFcFgxaY7QSJof3QdHAxSVu6+t7//bWsNfLOrDT0sHUEqgzkZK0ByfIHErmCxDNiF9ezaK4irPNhJiVp0ntt3OQA/mr55yXnVSumd/ExjDB1CtiZIggy5rDnKS81Z0Ou5AQrRsjQvfeupKxuXosUMI3hh1ur1z+L2nUpCziBsWKmEgK0uBdvgj+AgiXcDvo7jpOueoQenb/iBaMXXi4kVKCSRXt08oZVqPxQaLPsPHZdAWladTVttrEX4YmWoVx7HUb0Bojh4IPN1I/JROSXcwtlLHCd1vHFZ+lxSWQGS+iMLGXd/2iRfRbd+yspG3/Ib533G2CG1vVZRnI3fcOxvdIsxNuge9sILBob4i9HmyoAMYoOMs0Nl4mOY/cwySypMOuOyTFOxcwOt8HKUHp8qZffM5G4iFY7MPO3fN33U6zlp6KBTCc1VGBFbP3I6PUA6QCa8bb4wLXCb3Nv1BLw7hEjqC91VAS/aCvBzX8JvMDLStn/VCai1ZFS5oWfT8JAJZSiIl8nqmTtLW2PqwpV/Ay/ldwZa503VXPm4m0xP3d+fG7dZY4ikPM8jhCe59AHGbPsIUXiRuF1kegQan634uK5Fc7dbM562TR0ZF/Ci74BelKS7QjgIPuu7veH04eIZ8594ZrcOzGmx7X1IyY11iL0tfJ6OX+t+ZQoeMUvJzhbkp8yJhbx9Y9RD/gemWIAttd6n/cFdF4hAEThMHTKn093nLimmjHhB/vTJzUMDFGaLIG6oYQykoo3fTyoFhFoyxJ41S2xj3NpNKemEmTTSFTNXYQ32ISeWJU+Nd78Q+x37dxyPJPG3k+e+KiZuT9x+D9/A4uTlyjxvDwDLjvrnTs70Z0XArbl/J4sx577ZvXrmf2QcfFx2JzlKnfvvQQ4LrTlow/qPNgUDrDsKdNqUPnnJoCxW0l36p9Y1zys8yU8xkuT9QWpIdZCBuq201lf1WpP10S1+pdBYyo7VF/2z1T+bwjCfqCpRRkwxItf8BuGncnVzS+Da0Vc7Oz7rcvPVMePVlkvVL/9R6WEBVKdKHB6Xl75KLkSIIGjfJ4KhSIjf6Y0Z8eH7+zu7YNuptYb2F7QD9OcK7+sm386w+4OXmo27Wfq1xdZixNbePaaowbBAIMz9vNEbOlUD3ImT2UyfKV7u4xVccV9TOO7J10fh2eZ9cUJYEQNaZVdRh4wz5EwO29gnN93p7VTzJmvwZqCZritpJkvSpsI9evfjeGMAry8P/GIZIKJBubewReRTlL4zR+jGQv00PbFnIhx/QzTTNrHKqEDKbmdH1U="}]}]}, {"codeGareDepart": "190", "ordre": 4, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T08:50:00+01:00", "dateTimeArrivee": "2025-05-15T09:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 722, "numeroCommercial": "A12", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:50:00+01:00", "dateHeureArrivee": "2025-05-15T09:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 5, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T09:50:00+01:00", "dateTimeArrivee": "2025-05-15T10:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 717, "numeroCommercial": "A14", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:50:00+01:00", "dateHeureArrivee": "2025-05-15T10:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 6, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T10:50:00+01:00", "dateTimeArrivee": "2025-05-15T11:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 713, "numeroCommercial": "A16", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:50:00+01:00", "dateHeureArrivee": "2025-05-15T11:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 7, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T11:50:00+01:00", "dateTimeArrivee": "2025-05-15T12:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 728, "numeroCommercial": "A18", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:50:00+01:00", "dateHeureArrivee": "2025-05-15T12:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 8, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T12:50:00+01:00", "dateTimeArrivee": "2025-05-15T13:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 737, "numeroCommercial": "A20", "hasPlacement": false, "dateHeureDepart": "2025-05-15T12:50:00+01:00", "dateHeureArrivee": "2025-05-15T13:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 9, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T13:50:00+01:00", "dateTimeArrivee": "2025-05-15T14:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 742, "numeroCommercial": "A22", "hasPlacement": false, "dateHeureDepart": "2025-05-15T13:50:00+01:00", "dateHeureArrivee": "2025-05-15T14:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 10, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T14:50:00+01:00", "dateTimeArrivee": "2025-05-15T15:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 745, "numeroCommercial": "A24", "hasPlacement": false, "dateHeureDepart": "2025-05-15T14:50:00+01:00", "dateHeureArrivee": "2025-05-15T15:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 11, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T15:50:00+01:00", "dateTimeArrivee": "2025-05-15T16:15:00+01:00", "durationTrajet": "00:25:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 743, "numeroCommercial": "A26", "hasPlacement": false, "dateHeureDepart": "2025-05-15T15:50:00+01:00", "dateHeureArrivee": "2025-05-15T16:15:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:25:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "AEROPORT Med V", "to": "CASA OASIS", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}