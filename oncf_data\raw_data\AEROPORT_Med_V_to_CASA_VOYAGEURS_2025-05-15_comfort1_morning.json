{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "190", "ordre": 1, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T05:56:00+01:00", "dateTimeArrivee": "2025-05-15T06:26:00+01:00", "durationTrajet": "00:30:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 6668, "numeroCommercial": "V90004", "hasPlacement": false, "dateHeureDepart": "2025-05-15T05:56:00+01:00", "dateHeureArrivee": "2025-05-15T06:26:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:30:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsLc1MvQi/FgAHlceb4CuX2aKjhBQmMTbBz8+6bikgFwDoV33hXV2+WgC6w3+feia6ZMuhSgdGwSh1zczka2l3yLv6yooB8vWtDoXMtAx7FBPu2reKkgqonbyCTkcibYkeqDHUNObUHGoBo18oKWYOfxA8uBqpIqzZDwehCQY5ZLLuPq06wUStNRh7Bk+7K6rEa3/mrghguKPmNzhRldIWUuluy0oiAUCbFAw1FTTOMykhd5s7VGSwIA6lXHTIwkJKrwcmqz1iZ3co9TNFzk41HepkZxCIXb7gZl4QZLco8Ev4EkCW1717GF4pp/LC6h7r8y99nPoXBFc6vl2ZfLL0yePBk4fsurmoCSNjhO0JQ9DCIzvLNOiL69XpHxZzqCRiC6LPYBaLvvSxC63CbgbeS2BLv2BQ/z18zwVhiMPBOD1QwY39dG+qP5Qxkb5vNmq7DQGZqfseYQgwtwwl/4AsCtA5tTl0GDwF2uzRgCXeQuu79uxYu9oTZeuU51CFWfq7CW4/j8nYNCOPSkT4b/eSCk5Za1mV6vDDd7VbJO0x5/nju38CXFf+w64NHEsjH2UIqD0vyJnim1Ojn8bbdL0yyVh1bHlij/fQpvflp7d6djVttkwiNepGoVvmqo36iQJwhEtF0PnStzCdIuTvHw61I7OtAEyTaaB/rN3omsKgCcbzIe9dK3k+Ysbouslq7Ity9r8+pg7cp5Q8vcmT0Ejk188rvLB8he3YymmtUHT42LYGTU81quYnDbfj6xpvXdivzUM+cQLjgXtnwWyIlsvDy++D1GAfZQINANofRRTDy7gvcWJS7XRAaAiupYVvue25KT0aPVODCamAxQFmtupFRF7FqorMexbXhbCdl9FhQlZiQ804bOCxFpY7yuIALtUcFb3gxO2WYAOJWcfCyrf2tw7mWgPE3/K/XTlZ9zYr1C6BOGHoLk3sS5nwW+HsEbiyZF4M1KRGCnGkBpDv8zRAhMB4Y7WE+Kfpr1Rg7E0QPPpptk1wcx0ovgMFps6qUw6m1wraH7z/WqlTf9WMAm3iBl+BL6nAwLz7XsuRqlP4PCscXbuADW01WDeprFbDAH+wjUZxrCBlCQER//hntttSXbbXebqAY1ekox1A77z/e+l3M/a/irs2W0g7nQZqxq7XcQWcgEVY5I6ZAVB26WMlfPgkJtEEFe1fu0FlWhL9sGVUtIOlf85VHftj26cJBUYBXsAklIVxf+nYn6+LU7ST1tco1MDO+ZOmv/TodzqxANqroUB75V+D8l3mBfmFIINewj2qIJdqga6v9J5KmuGz8l2WNpWL7yFlRzoVlHHaKeyLSRAJBavnR9U8hhEJt5yu+BUziTrO8ZPb9JUdLL7aNr0Ui5STj1JlxAdf8jPDspMYUKyDUmAlXHeiIrZLbGcmk7wr+O6sEXff6MZG5VyceoM37Os6lmpl0aNw4m43tYm7aiwekbv4gP9sixzu1uS5BwH3isjMfgLAzRk7LkcSQMlAFV1pBm73NRoqiayF0vKWynYs40LTr4zh12+2pn024+ALYSPlxwUxuTYqiO7yEj+tbk0VBLcT+LyCvPZMWI4ym4PvoQDFYSCGAC3kUDlMWCqqYScKRsiw/tqZRF/ndC+Pelc1oQKcU6ubztjszaZMZKhaoqmH61aR2TYEG+F2TMRkwoIzz/Z/1xos6bqXWCleg2TNaxc3XnCum0Jwqphm4L4tKMcyXXY7+TT5BIf4zpzeoSZjRHjyFCMTkV/le+rChgF36jbESLKWVxDuPtajZgdT6lxeQ229KY9aUoo6dXVyHM7X0ATTOxyw8LEJZ7jK5BGpR0hjPybC+bqbCxZI6nC5z0TecSgWkAuYC9BmC6btP68WU4RAyBwOrRILxjTECWEAtpRjTlMsPhOP/efWgCPH0fT0JW6xmHMoWi4LlagtEYDJjv5vJEMxqOcngbCHKvdVkMq6CHaMd2Du+Xl7pgRo19J2JCv2jPA49O1bYkgLc7pEmC/e3sSPXCRSg72fKFHOfMemp41rv9BWn0fu74xoou5TrYG5BSUGzVHHjeJoN1jgmjh4Brm1Emxg4bBWbfwaoT3YBjDjcCqfsvOPedve16vJZug/XYDnN4YeCDj552shWmGs4F4N9P1tMAR2NNMyTy2ajc6gtAxMhD1kuGLsnnxZ0ZdjaS8BTlh42deak6ziD8COBRdMjRqlFzCP0SKCvIefTqi1EAzmHd8QL3eNYAuYuci3v53mFhUCMJmVNbHCkXX2x+gQmOh/Tl2soz5kxVviSxY2vb8R7uJY4qAiS5zFsw1Z+9WGpSGQwSoh9EhNiKLcmngezuSdLQrMULB1vk4nZJJWUqkCuyV+vmnUw3jh7ZHKmzoHuQ87AlgxALAI8HTGqv0XIM9EeC4WkWBwxQsldPq9c5mqxWqKBGY/0mVaJkfrCKz8f0N2pKAXc6VcW57l9VWBncJajliUnvqZM1cmGruOzYo4s6utUPDtVPW0S7U9ISmk84EA0W7suubrDprrAOgONxMF9iV4O7d6w4lSsCjxDOnIG+cJ90QLFgeto7Dib3/dxn+kEXC9oKrrByLZai01Gs/YHW/oXf9Nk1PrkO3XfTg3ouN1QP6jIcWf0B/oIpe5sP/0Yi78T6efujiIbGsJo5i6xTGW4bdTSGEHtFypARGYIyQOowGrLYFAEXouh22cccZazS17ilHRZiAlViUhV+o0k6/wtrdnNHiVQo//fxSJcslXl28UFWc4g6nos/ePM+U1ynm93K+PLwF8FNSqW9y55QeRdnHpaPcekdBbaRLDe7DnkUAt1eAo7WfaXKuWrwTlhLPpTaEgGOCedfqC43pnL8ejXtGkLC4gEuyuJf0oA3SVQm/QZL9Wjyzr8aeJpqzgI7KIm/Cewi/jooisbwGy0//HTM8Q019CQo6JQFFORVdJtNV2o0SLjWICmk+QVI4lRA8M9NtFEwvz/DVzLONRmOnxIUCB495V8JhJbWdPtiODN7cMXVsCLFTAqULwIkh2Oox9QwNjn3+/jy3z2PpLCbAjJB6Xde2wDEozA1SNBc47RQRJPi+8VSqnR0WC+o6Ao34+vXhUdkNqxBhlDXGI5rHgyvHmpWI5bSjjZDi6D2g2WrlULGm6DKxqOj+5L6ECaBcTyYWjiW0wXdkOPeHHSUJz8ugyO3qzxmcdI3KJG63f9GR9OtsDi4HvBp+gEB/uokEeyEbz0QPWQlz+jeEyHk4HCXUBf9TbvPYuRhrpDi+ktS/yLNWjiMTwUa3fDnbmNVuegupol2f9ee57G/bZfhcFgQDC2/+qYKmhFNNdKROLtLT9vY24TIe/LwMl7elE5DT/eSSRUdMqJk1FGVc5NZydi/+Gnt0dMRDs0i/9SaqXNDN6vl8qkHYbc6U8RogmiO6H26mNf5AzOqg2xbHjfkBqH8kPVHd2wyh5Ip7yk13078NgrY/ReF2JMpL8OVDBnnx1FSA5A9g99gI+1Necp3pnLCodAYA6MPjvH4uYXs0Kt210Pf/E/6qSUIpO0VUO1AcKKeKoVG5CG9Ba93tB43a9FQ9GnVL6I1dI9C0FV083RbOdxy9ZO6eA8aAKbj8yLiuzpQfvzGeTh3ytlWqyxKiLte4lIa1OukMrxtP4xDd1/8EkZwwYNOIJSI47iqlnbl+CgURgIRF5IiG9USGJdqaeG5Fk4PWAGOF6u/2WqByYgqholy57PPNVV/aIGH8vfK6LXGira4vpnWxXZU1mIomj1KQ2qEgxc21mMJr8G4hk4CQAm22sWbV6tsIFt0q4VBn1PnmiQmS6vBRZUDkQDTxfvTkwbfi/cIbcsAubICN/26k0DNElX6zlPhrxkqhbjP/6XzXBzO3RAT8bQa5xx0cTlYruqcdIpoJ6u3siRnaYb3TGnev9h6jbRWPoK9Csg0rbyK+/esL2DCi1EbEDbT/L5UXdxumlv79rkBc76u4/DNxZP/EaetyLZ3IU+lqnoVqsrQTm85nnEQn+QuHRNowvXUku/L+8pBbIZYxGI86f0l6zm6VkzQ089K/3JHk9/oiV6XlDudP7skHs6NXUt31FAhCLWFDx8J8FKeJIL4c8ZGPL0wTPNdRWL1ZlIpmACcXql0IHUhU9pXmf6BO/SZnVMIVglRp0q/z4n3LEYKzI9vyIz6vyimlBjqm1f5CKpAld++USRVtwnOsV9NUau3MhYwxtvNsTk71vNmK9ib8d0bOgwrGUwAiLhzg1xXATsRBm0HUL+gf1I4oWjLP5BSussxp8CU3sx3sonGaYe31qEq2YREMlMo9xNXHeVpJI68IIEEoNPQoIA58PwYkQd5uZCzk0GhQKwYwgrZGRo9GPP9eA7PZdXXBKsseTmFIt5i2R8hztW+KFOcXMHJos93ALt5tDX2lpRc6GMNdB8HV34p4n5q92xqnKGJr9WTjgZaYKmJUlzmL6nDvSKT7jESv0LeUEPM3HwbTRQjvG1YS4h1iq/SgvhHHYiefkWprJPKZMtyJEEnawyj5q/oN3RcvrVzDJruFFDFlfdmOzh3RPXpByHFjinSeIfLImIiIy5mE1Wj8F7phIlq11h2nZTq3x0WwklBEVI0r1t0qKyjbzhCDbG/vZXnvuVf/CKtfxnYxhAnS5YQOdwfoRzCNb/9puz2DBL06lb/p2vIQERaU751StAbCrlx3HNN3ReOV+R49ZGXbaez+96JrinD/hKZPx48xl90WHKgFD0DN0WE4sAx1ZDYX4DqkLjtoBvrtWQB7c1L4VSyK+5PmZivFfA5KARRamIPZDfJq0lhzHGxfsqjScdwTp6WXAWDie2io5szcBgoxwVuYr3JViJYjRUOwokQqw1vdmjQce4DSry1M66qmqwdOS0bs5hJuUolY9qOUrLCUqcv8RgO/ZT+cWpjyKKYYJ53FIYeKMkaQyNx+kvDJ7cSXspYeGSt9GpFav68vjbhY14fNZwJYq5gU/NEFXQtPh5Y+fq/Q++jLnN4ozdy9IrthTe3EynP6LpxYNmxm6r8O2/uyhaCnWDf/sI7S0JP1KhIRzuxNZezvT3llDZOAygECW2zv5f7z8UVF0y7C9ZdfCKfUj0igu6rwEsbC4SFKKlVX2fI4rx2w7nZ6DeuUrbX+Hqduss6SOg089GatpdOCM/uYlBo7lJxJZKygEgOckpCAQnWpoXQB6r6IHrtIDkFimRFnAGkBMX7YuOZmdrm3Ikw+hKbGH2TryVxxByGGkFZxdLXsQxDfiGClLlOe+8zUm1/LuAUA5qdcJpCV5PSJ7LWmTI/yKReh8vIFwROXy1wsq51fQVLrCxP/eMmwDS8yRevoUctfmUllWw3YCB13QhgXR8evBhgHVXoAZyschmilbDj26hUci02fY1l3JKnaaFfnH86qFO4+kanfVCn8eZ4mzNXSMpCKX82sbvYAwt74udoTE+2kSaoW866HNpeqGZcOC6bPuUpk7z910TqwIZ4iuYvGGdQuvdTmQ8Bfa+RITLnN3s45uUjs02KB/5EMYu1Ywtky96e9kQvetKXNMM6w+XohHUutiLzGktltJYOBCqIYWZ05Gg6GRAZJvN2UnfcZIUt2xd5LfuHtUETcG2s6N7hRj4AWNVym/WiERywvP6TY6pP3zQYvuvYWJY3FHup7mZWEwhqmeD0rM1pfkrumOvZvujiydV0sWstH0bDVBbWvYWZkQEuuhTQaG6ZueauLEA7OVHPnnTmiFWa1IVcQELPXruk4w68dsGcyfvoayKglOLMAWeg6ul+gvL39DdSNM3UMJ1MmgZe5Z8Ern4OhF7tOItiAAMGOwwOsVnseR3gPsHfDbb5dF2CHtFG+Uoq+R5CRJIx5E5o3KcTs8bKtovJBGh4rwZErlx4a2AJ1TPaFYtJIWqsDf7cLVIKKtNOKpZXUJ/GeA7FEgDRAzLCpX83Myoiq07MOoGeRqrOMfwUh2hYojFa46G+zTvBbT9RsNWuhYCs7EGZOb1JmrWQ0O9+r8501NtYnQ/ELho82NR63utlaj/zGHg2D4ZaHcScGsunDSP5YuUCBqGb6DkA7xFpDZizgkN5pKvd65bUrKX1vIdKDRJ3/6LYTTODKJpyLfMsTb86yGlL0fcrPqr2iOhpLHxQQSLSWUhnV92AKoQeguGLDqv0OjcqkoF7Gxq59Ngll8i9sWdn/jyBuYFO4uTBrqj3/CnZQNAT4kkpEkSn2Vb9pj6gBiQaZ5pGaoPTouMkEKHfyLsX7a7GvIrtPCMocpQn9OtkzXEvh6aOOJflEUaIcLRPuV1QVh8B3YP+/qBi/ez7Qs9LYQqu0712UF4UQOIIyyvVceEqZF1x4D57e6asIcCpgNbgWaRtpzwCSZyxC4ZTIk9CtDOSan2YEId5iW/ol6ieJY73TXsvOAT89OpqFoncICF4Kt7lry6h8MnvOxQk2XPMkquXpu5EnESi4EGXt6cqij7Wg7RrlFfsV2Un9266+JZ1HIAVxjVDkmOD+TEG+Rhan22dxz4zt9GmHl05MAg8TZ9OsUXbzBEkPdxOIENetnYQ1TYJiY3P387wq3TOcv9MPZG+70M8Z70dlZxQKC7hWbAk4L+M8Z8hxslOiiJlCNYo7OzhqjGQ1Z5v7wKkCM6f9NKz6kVnenaKFZglmXudpR13lkDJ9LSqimodDYZm2oQiDpKwsJUs/afrZoHtoTzPPMZc5ulCFOscsJcPkT8yapDOGD0ECivdwPXmDgTx303yPa1yJHmy5sLVWye2EYV2AsmSFxWfneqzLGaeqRCwXeOSztpmw5KF6NYWpfFSV+FltaBHydnxDThex2yUkuoxPVzR/FUbOMmRMREH5CirYbvASUSR+JKrFcdYCPhp2/DTbWk6CDI6RndURLldE5BVgWU2lKgYOgJCqFfYPijTb9NqG6l2vQaMYvYFwvKUjr1riUvEsgH4IfSM+4CjJU1iRIp69548kLEIjG/85t4jQt8lheEsm7dejL1hlph8Aword0NFOs26jmxrIVDfeUQChopyIkvztV7NnVRAQN2u3hCCM/9LC2hWm7408cLK0dJ0GQPYEveB1QKwH6aWV8q0CtOSQzPuXYcX7PSruMSImp2P8DlAb1iuQgeaXy6X0bquN/8tcjPJQ0Y015B8lGkK5bYsHy+dx3AenQaASbCR5nycDnwm3tlchmNLeF67XgxU9bGx7sK8EpVCoO5WssCqvPNY25AU4ryDVO02GUqkQnpVCm664Wk4gnVgQLSUITRAWglTIunR5NPpGJfTQ5MWfaCgl4qXz/pX/Gd6w2I34okyj4/2oEuIjJJNDVFbnlhBJeJG+NttLMXY5rosK7u8tHgVyqhROyybaf7gCjsyGTFXDPvlNbuEIFxOlRMhdXt43nKtRCXtk0B7/1cfWasaM1vAL6ROkniDA930+Ji1f0dY5tHH/+1ob0kkBjuLtuuW96ZLEfzrGA8Wpt1hBX7DSOdlauBcIkUlMpfxU6Q5C4SwXN8WhXNsK4zpnSPVgxcszUSvvrF2DwebgN0mcBXS8aIa2OFOVXXGPDgmKLqXUEz95MKEuvCDRFQPtSF7rNgo/g0jovVN0m8rD+WqBdeJYnATgXmN3XntCTZ5kBWjIZDKiVNUtSJSUaLHp9lsLMJqY0OfHhISyamBbKQCmCJPXpf5OQWGHCop784Jzcz6HPCsmC3dQaOPHdyNkTNfvN8GyaGL8DmVX9n0i0I5VVjML3E4s1AMyxbxkKM+mEkSXLxDhh6g6NbzEbo2u3BnT4joH9y7gqNHXTWfWy10kPXVK8/ywW21nLyK14rlIZbk0EVWDzy3sZGs0OT+H9ZfgMI+vTno1vOgiECj++wDILNk/6eYDOIhEDCONHp9xvO2ppzzFd/HF/0ckdjSEB6qkT8FyQ41/rT8jWzV1JDLRwUEuZO3Sh8doPYG0rli+oh8dES3akQ0eBAKM5zOM8QY+m42VUuFCZhKHMRPYNRZi7B0e2f9kV8Bi5mLo02LyZwr6KJVgnuJKZMTs0P+Qw4hrYOnBTRpsUW4Urdy1QcZtJBut7tmNjfBzlw+HIz0gDVoF5M1ymI8st/tTkhOv/GrZTf0xbpN49xDl+OsRn4bD/dlmn0/ffTGygWWXKpwrc9jYQG66ndgk3DqsgzspPDHvO7twnBBwE1lSAiAK9lWAPzvUaULglDXo27awnmZpS/SjYpRryegox/pDtzwNORx4g+RLHBPQdycv4PzwK/8eqd00kyMtbHlun7Ltq8201/WIlx2vY4L4eR1iBfzHJAvPxszzVsFBKCbJdaXd8d4v5dpfOb/Vgf1OpPmyiyM3ROEIdGBX+rxv3uFFAzUni4dbvdtZGpf7ta99i49DkJbENJpckl9lVABHeMQTreoW6OQQ46M6VKD68B2X70UJPept7gAudZA2on3hL8/6Ihq3pIW1GOsPkwJvpbn6uJmAyuPmiOxdAzxVj3IxVroRwSavjlq5CIQRvz3QnJDe9zT6Sb+6RwFe4ZJHpBtFebl6fNCr+i45V/Qq6himy8MiBNmehREKcWMc72viccLznAuEJXVCr8s/cqywKVjMdqcnOTjDpxkcH6pD5YEM0G9iqAbhNiOwL0n4X0BikwkOF5F8AWXmCw7eLtI/rJ6izvKGnRJN5TaiouEPxfhKe891dgiL7NznWZsLZw1SghNqKZa1U0BkJklJ6PznVJ9uocjxIbrRTYmEujjiCkZnlyzqhFat04bQncndMNaR1VXiV5eBnNjMVk6vNfSezFa8Jj4zco8F2V7Qu3HSCAwn93Y2cMSJAQPqrgWwLBWYzLfDQn2ua/0aEJUh0EdLg4rvxKTOGQErcR3/ATscxUyvUIiG3RtFx9FMl4LT3QgqZrDxdEuMfKotPwz6UYgllKzD2cE/36N9Ii9Bkod/IWdUYEGTfmZIJOoUe4pA8coyoSinDLCsQiUw7HClKsxQVqVAc8BWwXXjFM6arkT3i4wat0slSu0pWYAkWzX6iFa59vADPAU4aM2Dxn9tFLlk906Z7HIsfiyzmm7ebY+VQxmkHUUkCtKumixoVd3HEG/n+t4anJhSj3n+jeIsbbNZFk4SE82hr9eeAoIu6s4or38CHt9XifHbKQnmQpnV41ij9i+kakWIJ9kCKXr149U0jgRh6FiwAFcUuBrpkCW/3XW/xNkZEoM343MeEXSiXeifiASTTIgLphf0kcAYV/bTSsWfJoWOQY3qsy/NzOU1ZPiRF0cZYRN7qgnzRXVOJlBRu7GDoy9rYd+7Lid1xq+oReiHr140AmJf8tZofktpb8hSLAGiQbHblhQeuCn3jtuBqDn6KlmZ8WgUB8LPH9G5FUaOQkP/Z4c7FOP8+68mcOcb/SHsO7CNqCdNRkh8rkNlXdLXQUu2R+fWlZZU+q6jGH2kC6l7TLV2SVs0Fp38HrRo3GgCgXA/oJuXxLq9GB30P51Ctkagufx4oTWPG4BLyylKcRzPitXB+6t4TcCwk2nwhYnKDz/Wpiho3blGaAEH/a6KL1yzbsQsA5JdoPg5PSoe69vnIK9p2sM3XC1CFCZ3MW7DmBjmtKQ4ESM0S0UzRnjPYu8o6Lknt6sxRfzM7uB2b84vRdz3VyYIqeLnOqZdxs5oRk0954tjSPDo+lWKZiqE4fim9yDaW+j/6CgTxykaxR4nYMbBX9kB45o3b2jAHwZttB+UjUpcfDAVLOeRb2HCQBRmH4xAs+j0AzZGoh2gQsZ/SMlwQl5p3ehofjlfPEGFcDwyLh524tNcjRzg8sAZ9u81jNn7ILkQ0QjpRLQkLvYxvEPcv8qBawfK2DO0PEg9AycYDkOWq+eSiGL5GFo1f8hB5GKZq+cX8Lar36CClTtMGDZDfO1rN46lDW/iAYODEaG1GaYSBlgDVstKtSV4PRVp4B9O5z3AlwRzQUkIGiTzgFM1JSqWKX01ECdPfG6NyroJHM5c0rlxyvD79hHjc4cJpIg6/3cWQljkh+hVz1NhZKBenNNh/PAW2CUgaSPYE/fInRlnMnlP3IhO4Qg1ike7SiJASlNUUbMON8XErvlBE2DviY8oWfNS9f6xiceJgGmTd8PUUUobpDwsLXnAAqaVp"}]}]}, {"codeGareDepart": "190", "ordre": 2, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T06:50:00+01:00", "dateTimeArrivee": "2025-05-15T07:22:00+01:00", "durationTrajet": "00:32:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 766, "numeroCommercial": "A8", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:50:00+01:00", "dateHeureArrivee": "2025-05-15T07:22:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:32:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsLc1MvQi/FgAHlceb4CuX2aKjhBQmMTbBz8+6bikgFwDoV33hXV2+WgC6w3+feia6ZMuhSgdGwSh1zczka2l3yLv6yooB8vWtDoXMtAx7FBPu2reKkgqonbyCTkcibYkeqDHUNObUHGoBo18oKWYOfxA8uBqpIqzZDwehCQY5ZLLuPq06wUStNRh7Bk+7K6rEa3/mrghguKPmNzhRldIWUuluy0oiAUCbFAw1FTTOMykhd5s7VGSwIA6lXHTIwkJKrwcmqz1iZ3co9TNFzk41HepkZxCIXb7gZl4QZLco8Ev4EkCW1717GF4pp/LC6h7r8y99nPoXBFc6vl2ZfLL0yePBk4fsurmoCSNjhO0JQ9DCIzvLNOiL69XpHxZzqCRiC6LPYBaLvvSxC63CbgbeS2BLv2BQ/z18zwVhiMPBOD1QwY39dG+qP5Qxkb5vNmq7DQGZqfseYQgwtwwl/4AsCtA5tTl0GDwF2uzRgCXeQuu79uxYu9oTZeuU51CFWfq7CW4/j8nYNCOPSkT4b/eSCk5Za1mV6vDDd7VbJO0x5/nju38CXFf+w64NHEsjH2UIqD0vyJnim1Ojn8bbdL0yyVh1bHlij/fQpvflp7d6djVttkwiNepGoVvmqo36iQJwhEtF0PnStzCdIuTvHw61I7OtAEyTaaB/rN3omsKgCcbzIe9dK3k+Ysbouslq7ItyHjwtfh10vTPq1LoHcLIls+EfMi/fVFSMjIxb8JX0ZJoh2+0+cV6GjQjSezGnhcLTpcMDr3wTLQ6YJQDo++VcE2CMOwD6DMype+VsQ+JZSNWYfPAVW9/oqB/nA15xTQ49CM0gfujb+oQqbKpsho/jGYsGOxdAxZPjBEEgH5/rbPds7dbsZuKBcPeul2wz3bRoZ7Dsa1IS+b8c1KAXAoZVk8nBXFtb8/CLxOkSeDUcr6+pYsaiHZrfXOKJ5TyKcMA81EpBFsSbEH14QBs8mvFl+epRyl3M9xRi2UT/2nC8d3uNRF0b+yC30ug/VZFU9dDFFSxdkwYGK1HjuLMlENzEgAvcxKMKY3JgJvvcgDvwZwNhhZSn0f5JhJiTh0eIal2iDmntt41/R1Gc5KKHLRZL51swmvO6LWqwqTc7VK7SvQ6tf4wUVNX76OwJw4GyDqQyI0zLzVNW9kKtNnIPA/+GzNhcHcMLKxmAuhOXzSv4OP4zbUSZ5EbbSozn6pe82ykC2r2bXqwVQR7ZZnIyNZIPnEtwc4Dfgnh0zdNImY8YvV6NWKFZs1BMfpaVBoPy3tJ+OoPPI+d+O9x9ZnpolPaH1YmkiyoXR6li14xLJPzJ1Q8NpTXYk7u4S7jBgt2ycHabIyNYd9ieMZyXVIrLM/8JpB2xUN5DaXzrw6gItIBs0Lni3uTbJX7M0qFBuS1cS9uyJ9gv5AjsvdZlL8f4kTgscDHTA17andaO0O28l4i9O88BiXgfqD5QMjmQR0ilOXUOITuMXWaCsi5B3NzI9RG/FCqEMMh5RLQx/2ah1JSs+RFLolMPdMC5j97bG9lOhuHNx8riVWdRRrVhp6BlkBQJLtPm4wYXQd0QfzR2KoGdNsIJ7YT5e10MkailxOt8womNs2Wb4ZL8tYmKQCDUeav1SUHJTpDm1+qmaGWQ/27HQTeq1CUY1w17ppegQPH9QbQ1P8y/Ya0tlinRdoKDgS9Wz0uyHvVbOiajj1qig2pihu8Rt+61B7DCWIJb9eVaTpAqGEeEPsw8kQsmxG4rPxOCcMRONzTAKv7rXH0Tz+c7CfQD+lmMAyOhT7ml2wwpjFBOQMQjK6DqOKUaVL48Te3XZHTtKO8GOo6iL4BKPoJr8B7ASMqRLralDYa4bi/Qz1zIsZZAj8WQoMgiD39homFkwCFOvTej4TqXXeT16SnXBa6N+xNMQ98xg0gXiQVM5n0FLIopZkY3Rc2e5TpkTz40nEoJTHho/hDu/30bNTAWkCwY+sDBFATuMebvEgxKlm9++z8LTAf7TSGu1/nXN7B4Z59FEBNWvHpNwfNYNtH+WKn8RFsTQMdAT9nhhB96VTeTOAuJhObgUdpHCc6/FhDWlcAG3Xss42Xb9jtM7nwQNYO50kSUxsg5IS0yRN9dfqg3MfaZhcdbMB2HaSddA9m9FLWV9K62Ircr28X8RMI5gJy1i4v9X+t1DhR5w/xvQSHiWrPXiTqVxzoGDPVdP5ozWBoQlMAVALx8AFxAauGjSLBDB2ZkI0RT8fBc8F4+MgrJlGtrCX2cXSojUjvJGQ2a5m2jdoijpRo69HOSGZ9KZjjUkL9xF0yinvRKjoA3+l3/wQFIiFyAMtaiqq+Dhm9VPiTUeDTfbCXTltFJqKRsXqmQdRe3HTFIOi8yBW8lkId3my7AcKa7zrL7+KbAqFPTifB/nZjtsFmF6Rz07s1xokvIh6p2c8ilGULMdiCt35EvT+cfrdLcTbSuGYTQ0V8tdx6Poa+lUo3MnMGsl22BY4KnvA2Ijyk6oIihMzKkuYj9AjYL6+6jv0ruW2tZjigxvViN6nry0JehkzI2bjlgmh5mWs/sOrgvg2IJa37pUgRdge+O3cjucaS34kQU5jGBe0gqijb3TYZWryn5kviXfDcbDNauL54HyAJJl4tJ9PZFmXRFTd+Uej2QQq90ooIONl+w+NKoG2ac4uSTvGAkywXmNIvlj1Ram18AEaH6uG9IstPO94mtefyzhN4guHHi/ghNjCprVL3FpRMRmdxs6Ixbb1F6YwDblfYhkO7L0YKh2YoNeyfg42UttZ/RZ/l6uTDvWMxrp68D6f+7lzrQwKlwwHztiXpQVGu6jhmND505qC2bsQq1Oqc4hdkb+4o0WsjLaADCuBWN/aC59WbwJ5nktgAnt5JUSBje1m12teJQ5ghfvOyDHnq6nGVcjr9gEEG41z5gk6IRiz/E/HgCdZ0SGk2eLZNAoT0oF9O8IQCw4beW2ygfRjX4MSh+HnV30zgSfOatsgiS/5AjnOAGYyHsJZPjPV4Qayn688Ge+JrdJ8MtaAzKLCELm26hBDNMBuDjR5pZZ0JwzahSprD/H0XUMTYUB8HjhznWWiWeq5rTm7MJRqXCY4UYVR1Qpj9i9KxoNwHVrNG7sptPeT5KHHqcZHHxd9onOTG0FsAd4W2PEUBJ1P9Rl0Qf+92TP/nNIx7ma4uNHB4KFtaoyZtaEAhL1kItmKHHEQzPYqVkR+H7aabw7xjGibEIx/R+p2ctiX1YTcQWuC1iMVi3QdT09GxSgoOR+2xPyyxt4RMLYbHi8SKubfA+IybytecuBtWx9plSG5RN0KSlvAyk0DAFgWse5R4TF7FeL9TuNuBQhnnj4Rp4eJzpG7nA+cuvbg9/O9xwqe6ZAARUmEayscNjhgE+JehFw//79aCZehdf2LolrafRSL1SoBwrMT3qt3IBqGsNZSs+021jO9priZQEDX8a7uP+i+zPRsgq8au3KiW5Cmp0Vt5SI7033JzFUiSbTJlDGDmhKVX9lsN7MvP7ZANHRBVvDmFVuXt0anLaViHzcS04PfXNUiAU3tm6MhPNQZQ0hNSjcaPgpFWZJibnXsIMPLWHwEQ4wvsFxW0FJmSRtuCfub/fIIW/ituCxiEGjJl7mXzawPRekoGb2ZhlfoQ9bJPc84pNoajJdBSNYHIT+poVvCvyeKt22moVvEq1U4TFh4DxjXhCN4Q+wHEW2jlw1ewlvYftT9LOgwzGWhLLfNGQPXYfvqY0CsCWOign/9mxkbosKOcGYX0HQU6kxcfW7OsbfUTlHT8X/nX4gaHJ61O+i8vkNnhZBIczi0JYNgq8qP0LyBQ9/WzVwRwQ0trXsAkvy28rH6Vst/tIvOURWS+2cWzk9h4acZS/B4GZhHCq88HXp8iWNCEwdL3FuUYO0JeuWhHdm4BpaioHTlUWdqQlitDtIXOIyVLgv/o/+JDmBBE4daDTknSqMrpIC8WyY6jnUcwrCkvCjttPSYlc8aZDvbEXd6SxtYROr+rlGsXOTfOwAHnb/lK4rCmDB8syFbv1IQ2OzEy0fFmjdBKbt5v9etIVvIetsbnIeDELxky3pno51MKyPFi2JRuJcd+OviQjhZsCpr+bFagspCvON72o692wXkgsb5q9eulRx/v300Zz7lsAIWGymYiMjrfZT5H1RYHG5GiE7yOCT9iRY07n9b9JJoZPb59BVOq2D/c8KvsrMr/S2Ivjz4xS0Xii8Kt7A7s2NyoJLCRA/eX+CthTBC6fUr+2WkDcB8i5wGOIneGP7/kTK6gK1/ZuVNtafbLNzfb8rTqARhu+SCu5hA3SDzpzh8Te0BmymRExEetFuvnBbAAwqwB4CMzJSb87fupuUBAtigdzzg0hZbs1yvqVmU5YEqKqfeZTINjrqQAshR++KVkOWsjMricxZJHmTjMX/DpZjNWXUXXpPhVheX0GCv45+MYPV2v3b6rvefQvun/FatMRfNN0wcoLbbG/xjtjVnWLfEqq1/CCX6XZN7d2IgnpuSdLVp14ytSlLY9ybhuZ+z1paZ3yNR+zfpx8XlxhoVWPNSVVRnsa5R21nWnLLR/oM81v52XVXRxse87bWklway7bKQjEjoSJBN0euZ30Y76hF324+OGmUiboNMxxc0NGY/c1UKFfdJ+5BGWd5A0ExZJ8VirSJYKSK6Iy3uqeWSQuZcu50jzKTP+eVzz6cgLR0Y6QTWIape5oM91hF4uHfaUtb3ab/kdwpKOAzMPcYpMDPSQWvhGFPXGoM4+z8bGA0OJyDvqFX+UYCVnsSWDret4Fwq59N+hYENLf0UKmZBbyBNYAeEQtImdwOt+VRW1qz1d6BMajzKukUVHaNwl5S5SitrakJ8REkf14BPlE7X8gJHsk3+rgmn6GbClu3Nxt/V0x3DVyPxFf+xLQXAikXAHBvGAuifkpvIdTNhXgsNd6pJru6vrBxQ9zYjZf80FsyJNA5pnKphThOpuS4RRjdcc0GhoVCpE+cmq5EK6AT49D0CzFYHAQ9OCZ8Bq8CqZuuc6ZtKAOyFPOkrsHH9XddAcdTJMFommpDOEPIl1D3IqaMgwEwh9nVZO2mOgDg4v4T2a8CxQEAlLJHncx3/xVArh6FGA6irmyjzEA0vEwbSkSjL56naM3br9eqmt5q+bNn3VwheiGOzdVJO4NI24lfCsj8p8dZtz9dI2qkEg6I8l0C62rhFN3kkm5TQSoK+eIjouaF0Vj/dpSePyxzyTYWJZvLeWciS8JS7XmweqQcOGcFvHnQPLLYHA4BvP2KVmWF27ME/C5kGzpZKth91da7m4a58kezXO59FVJwLLz/04PmvEu4+HYrnsHRIiH7nEm6Zy20Xysjl+2G/sh3X6djkHE7a+Dv5Ln+7huAwsc3bx51GedIwzlXfau9re3qf/Wmj3WYIpCx6q4cIAktEUmie0nDjCPdGPZ7EPY29xGbw7HrGtPZfnnNmbDuFSlXzLM6HQHxMK6geJiizU9aA91vqlmHWwqO47T3rfDbQZJ1BX82oROdB28ETb/B8zNzzSAIObYx2JLftqXxaTOH9JFGz89TsISe39pQQXJR1eFKQMiY9oQi8efO7QYeO5Y5QqaWhrt+2iQ6ZnFBFlVm6rf6uIfeuM6kvehPfuEZLjrwrbITYEIf8kWpU7cLVBBo2BcDRE5NUnpY+Iz4MpjoBz8Gk2RsawSF2yaaLRd52IOM0ExwwEedGdXqRKSd3zOMTKwQIYM5VbiRYf1Lt0vDqO1JltY9vhOVaV7LIVqynB930KxYvfX+IOOjM+YBe6IlrbjZcnvvGZeTRWF5Bh8dbSD5Ga2sowA/s1x7eGP2BOkOGAZIZuJGPSjMAuAGPWIvUCh+tNQkUxt7it0pPkCV2m0+rIzcz+4XM3mQcOgwfASWHmcqYqyPZe+3wMqBhdl32HO0awO14T3opDtO1w4NIkMbQuxco1RvMvq1q2m1K4CxXvy/YX42KWjDxEqokezUkHZ7T6bjQzXDzMe135W0gdZzQnjw2VOxab95svvLxZRd+YEug+bdQ/LovdJHQv4G4sVPOM8batCJB4jLY48h8d13L1ZJd97Qp9ktQpDjXH6/SI99Bw/uvh4uSjamq18pvnQhuS81roIQZcyvvxZJ8cyHbzzF3SK1LVlyIn7FlCJo9rhgiTX9sLTCHRaI80t0dflGis2Q3SH15TijEVquczso/BTMfK1MKqkFPdcT38ms+Eufv+ve0eoCICZyG3I8rZ/ZvJ+F+Lw8OfyX3nyMLfT8jSt7o94n1/AtwYNINjIWcfk9BnFDJVDSaVsGBbzvfHw8Deccf2ntPFZoHEGjhG+OZajBK2s4H2gw6APpVZCBeqw1Vlr238lDloq3uzx/MIklJG+1gE2wy4xA4LU7sLZaDCu6LxrutzvNTkGNfIF98raVbUqFBKMNPMZQCA1wnLzS2M9Ukbxkc/JEuTC0lnH6OOxx6YLCfj+rregj0ykDVwdVc6IoYTy4FDrgBq0N6TQTX7UxPYOmToSSjZlZGcvPEdVZ+FZLsOMT3n08fAFooVotdn7yaMrIumWGjd8dCPXUWReQlz86yjzQSGXE8ev5OgBmtI/bhLBEAvOxNEYqwmaVH+z27ZLLcs4yVZOuIBgA+kjtFhl6/tdIk+xlgRWoRMgzWoU7ynNt7cdZzsj960DE2lmCX3olQeusjGkXlBJ04VsDvtWpXpTxBzwNxKhGfo2gLk39lYLDNbEMtlDcXDKrboinKyjKD0Zp80S684el8qXNp7eOXZNLrGBUlsq/P01CmOT9+LIw3xwcFAzxbVCi8iMU09Iet58l3vqtNf9szwouVq7gXNMCsh2UFjqii1/O06ZWA0E8OYkQiYakJwxLGg63e4tIJaOIq8EKFdmMCIhdHHi5tqfLnJUldhZ4sgAMtoGPYT1OkIDb11LvepeoFPlDmB5yxApCGUVAOPjHfJlV4ukQcNBv1aAuFAgF/YikzKH4lFYfOp/yD9EC1o3iEuLiRHqF7DWL0x2x0JSUxo1DO+iAVB9GP6l+lpA5Ox0vl/NH3uYuDrAVhO5nOkhWrQ6HdjFvCsEn6hHGyDRkfz83ZPbBu/apmKfv5AMOvfePnJK/7wCp8XCl8RYHi/yLRBTyTYozyzpun37sA6sn2jmidRysiwfeq7UyeLuR5ljKwZvPZwAUoqwA1rX/73e7+B671+yHI9odBoGra1yumYWVTAxUhf/yWc+zaM/GyUj6aWaYL1nexGMqjl0MfNAKrc3EswCt4H0qbcxsqNFxoZL57pGd3XWw+jjRpBYSIJqc+he3t4VNSRxK57LAMjVU2ys9PKQ+cqwS4ploBte4e3h7NkZbZtHDc05z/jo45lhsTNVNCJN1tdfReYlYjyMcHXQ2mvEyY2HMlW3YVEO7aBqIyo6JqU2/kHsO7UKXHXu52CPAsAPhWFTtGsPFAwwEwOTyvvd6iF00xOpjTjb9NmFgzILO3ive1XRpIYX95G0jqto9YF1hw4XWHsAaAmWlXdjcApWuOImXsOQ6Oq2w61CrTE8MJGrKYKuPqYyeHXrYcCROQOhMazr8Z++xTQIecGNezFe+g87BYu2kdpSr/l40gBJVFy9i4j8qdFNz/dV90ZG9O5QlbD9ivW+SeQbNUPJDa+GAfCIQlxSxHERVUTr+tIx/YLFXtCkVsn/qziGuZeTdjeseXqLNPUxN4+T9YKgVPWBxBmm1I3ObqeD1LmGOkt4gdL2V80MPnl39Oqm6wkb8w73hT/AyIy6osVKOlr4pEu9tqYm1AyfC1tAwOVdQEJwigU5VhInREmkkhvNYC42Yqc8C8Vwcmuk4AAXH+3iTYR4p68o1VOGadb4kp107RGfV/AaWrgNbRgPeyokLxzryd1xFMXQIuZwGKDCStWyt/L0GzTvpOsm2B4mQTjeg0SFlvAsTF81I5F6Lt3Q2CbOeaf6PfR8Nbjtdc583bdvl+JwMI6vnfgNJ2KCITirfY4cpoiqfFCqeSTyaVl1orOfA+Yv2RCEEMtJlQjBRRI9troet6M75aR3pz5D7q2kgwO4ZPaXRaK7iHHw/IBQRsrJrsTtXXLKq8yV6WH1CZIMg8VPG2r9sRnF33uCeLGWvSmcbPRZ8AQUqe7S6VtRlnJegyCZnFcR0HqtqqPQZDA675WNJFLjhZN+x34rgraU1sTbEb9nLDrdg0ZKT4g+sYsQ/JU3L+WEyrV+BjhNllRTLsSO8FB5prvYuv03Q1NXfi+22bUYWQjlsHlgFGpYulatqUprrk5rHA1QhpIylsY3jHvQiffpSvw4SONS2H2NuzrNW6BcWbxQqAwItrSJCJ8AxEdGOBn8CzJNavLa7+/LxgDhvU4Zrx9pMjV73ua7nnfi5nrwgWoLG4Z1wfe8Eb4VD76TTTD1Jw3E8j4nlHVdcWMDy3DjWaOi9NStZYkCNCgiKrdaxokbZQRUal3n/7ACofLEHinuY00CVZsPEhl1kEV3oczf9fRMymZ3a3NMtgPJ+wBYVubXmiPfD7Jhyg0KAqYK+gAE+gJ2oDuo06NEtJDFXUgDyph0ShxrrJ97aRFHAzwHj7hgckP4HKT0Zs86tLZHpA12LAi/6MZYzu6mm3gOALuxpnBkp8Cms5+JDntuvIMBpXBwyyTPV4emBylszAsaXI9kQGg330bDNOVFJXD0RbqAL13iCzTZa8tu8HVbPCxfqq9QQUMyXEwoElFrbxEnAL8bm3SRlRc7d/WKHJqkb+A2Vo1bPQw7jBJqr9Xk44Yh4hudo5JAxg96z5mBN3B76EHc6p+gDlw0xmtUgTSyx3jeQOUxLhQ8rX8bRDK9SPxBgncQi00lk6EgpIqJDXfxWbbOa13gp4zYDyGB9hX5mNaJDs3BHZt7RQJzFqpPloZ/xkyr6H5gh6yvYu7xL7Dfui2DZ2rec60Ur7dvPoSVt9+0YT0ABU8L1gWjg1RmA6aBtYiZbK4Cs1UFDdzvN2N4+dLPnQOvaVoQ0PwA7NGGcspRjHkurF5z4ql0GL4Y5DsZRuPHhsErvxSq/YNVKEkyM4HBqWL+pP3KZTP5Ap5y1ye1JKCXOoNtVlpc0CGIOys2AuwbhvbuUIgR+CGIoRO3oQHmI/bLKwsd4gZRoJQieuhK6SJ3O5ulenErPkHPCZNEOY7docNI4p9aoxddL+cJDbdQJByeka9niuXmBNR6MafLLK15mLqsIXJVuesqqaM60TsYcwuOxIXYt0v+tFAH08u5Wh1gWTTPz1jnjzD/CKjZtK5cqNB3uG/K1q80eG5hCryHQgUqO/1BEYYh7IO2Z5eb/Zghn9JwG0GpB+R7sqHFpId/UZX2hECetTIuvQjaqhlHhJYFV9oNF2UUet39oMwfg49rek6lcsRB7+3ju7AaxVUchrnZCPaQ0ZDh7PBdO/BB4wRvE8yjatgOdC7pIKyx4l3ZBEoMFiCjbqrQ+RULzq9FmPmyxW/74TdE77khK4EnSMSQ9nsJjlb8K7j7IpPVdDlel+uuOmUlrBZeZGIET9IV+A8QCsB/5DSF+kN+p5ugExZHJfKbFft6ZJmPx5h62t3JhQGid6hLBa+VDjlzbnrcvATNanFueLLhUoHUQqVgSGyAqoi68NLfoJOqD7R2dZ1nVkEXIUD6fw2P+JRkGzDDUxAvFJxYhFrNZv8/MhACiBz/cYiMcVbpunUa5/tgORS71/a7ootuLn1N8o4iy934fiVKEZ8iIY0zT6oAVqS0b6zgA5KKpIYx/KcccH7BshmMrGVsEjebv20xCGo+hh8IXkAuiDxWlIlBBWBnnGeQ38qhhtsg+GAm47ZNluqdnrOxHG6MDiNng0Tw4m9bPnRGPdz7c7uBxaV0dzH2DMV5yGBbqMgdpEuZO1cB5fIg8gJfDPEFxkLRm+l243Dw0QAVjOoa2CTlayf8wlawyMxNQuHgT2RxiCwjFBvVzv1MFIr48dTktZ20J5+EaKWRWKSjypQvq7UR5CWajTB38kmGEFB0BSUesdJTIoHL9AN2YyM+X6RriA27EfMhcT7nZZqlw/WODnB/xmwDnFuL2+upe5z0KxERQdYA3zA70ww="}]}]}, {"codeGareDepart": "190", "ordre": 3, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T07:50:00+01:00", "dateTimeArrivee": "2025-05-15T08:23:00+01:00", "durationTrajet": "00:33:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 723, "numeroCommercial": "A10", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:50:00+01:00", "dateHeureArrivee": "2025-05-15T08:23:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:33:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 4, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T08:50:00+01:00", "dateTimeArrivee": "2025-05-15T09:23:00+01:00", "durationTrajet": "00:33:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 722, "numeroCommercial": "A12", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:50:00+01:00", "dateHeureArrivee": "2025-05-15T09:23:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:33:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 5, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T09:50:00+01:00", "dateTimeArrivee": "2025-05-15T10:23:00+01:00", "durationTrajet": "00:33:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 717, "numeroCommercial": "A14", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:50:00+01:00", "dateHeureArrivee": "2025-05-15T10:23:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:33:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 6, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T10:50:00+01:00", "dateTimeArrivee": "2025-05-15T11:23:00+01:00", "durationTrajet": "00:33:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 713, "numeroCommercial": "A16", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:50:00+01:00", "dateHeureArrivee": "2025-05-15T11:23:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:33:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 7, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T11:50:00+01:00", "dateTimeArrivee": "2025-05-15T12:23:00+01:00", "durationTrajet": "00:33:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 728, "numeroCommercial": "A18", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:50:00+01:00", "dateHeureArrivee": "2025-05-15T12:23:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:33:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 8, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T12:50:00+01:00", "dateTimeArrivee": "2025-05-15T13:23:00+01:00", "durationTrajet": "00:33:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 737, "numeroCommercial": "A20", "hasPlacement": false, "dateHeureDepart": "2025-05-15T12:50:00+01:00", "dateHeureArrivee": "2025-05-15T13:23:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:33:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 9, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T13:50:00+01:00", "dateTimeArrivee": "2025-05-15T14:23:00+01:00", "durationTrajet": "00:33:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 742, "numeroCommercial": "A22", "hasPlacement": false, "dateHeureDepart": "2025-05-15T13:50:00+01:00", "dateHeureArrivee": "2025-05-15T14:23:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:33:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsLc1MvQi/FgAHlceb4CuX2aKjhBQmMTbBz8+6bikgFwDoV33hXV2+WgC6w3+feia6ZMuhSgdGwSh1zczka2l3yLv6yooB8vWtDoXMtAx7FBPu2reKkgqonbyCTkcibYkeqDHUNObUHGoBo18oKWYOfxA8uBqpIqzZDwehCQY5ZLLuPq06wUStNRh7Bk+7K6rEa3/mrghguKPmNzhRldIWUuluy0oiAUCbFAw1FTTOMykhd5s7VGSwIA6lXHTIwkJKrwcmqz1iZ3co9TNFzk41HepkZxCIXb7gZl4QZLco8Ev4EkCW1717GF4pp/LC6h7r8y99nPoXBFc6vl2ZfLL0yePBk4fsurmoCSNjhO0JQ9DCIzvLNOiL69XpHxZzqCRiC6LPYBaLvvSxC63CbgbeS2BLv2BQ/z18zwVhiMPBOD1QwY39dG+qP5Qxkb5vNmq7DQGZqfseYQgwtwwl/4AsCtA5tTl0GDwF2uzRgCXeQuu79uxYu9oTZeuU51CFWfq7CW4/j8nYNCOPSkT4b/eSCk5Za1mV6vDDd7VbJO0x5/nju38CXFf+w64NHEsjH2UIqD0vyJnim1Ojn8bbdL0yyVh1bHlij/fQpvflp7d6djVttkwiNepGoVvmqo36iQJwhEtF0PnStzCdIuTvHw61I7OtAEyTaaB/rN3omsKgCcbzIe9dK3k+Ysbouslq7ItyUCm/On1PzRNZ13ke9/fktu/7i8g6RyslY3icOWC7FSk5UNR4eORucwC4g3QXLm6Rfzzo0CZItxTDCalpSPCe3Ouc+Cwm+Z5jlQpRQgDwN8rIlVoAUMO6mcPiBObtaAaJE1pqwHRLbL5umj0e0fZSYCkYxjBvIZouDBcmVJMO9g1zu920YGk9r48NSNEbNjwESAhtSHLM8WKZBxvyIDqrt0KZKEb285B2T6sdEgCB0vPEh7Gw735D2pTvtWyD7gY5QOopEbj0sCTUDgSzjqJClBJ3QPY5SPgz7PSY0J511dXFerkdcH19XpfiUYkZFzAYfMpEz9UqmLyLCF79zhXcmIU1JHu5zSxL9SiNA7woacMfP1G/S7W+Jd5D9HDYBltPOwBWiqnriKqL3ptiCgy+PTaqlsGO94uYeZilY+F3SY6Pgdvxhrj0gKvbrOItpn6ZWiv4LVesD72Gwxf43GjRuIQFbdIXjdMq/vLMc50z3EmyegZQVZ2PmjYva0kuPwq8YE1CyUP2RUKSiLOFqMsmw6T+JcNXbzSFQMxG4irzRlox3p0RjaY3lPKxNiba30IFxmhqXFAlMPx4iuED6nyFqmesGnbNB4/hRtnGgxJ4fVSfeNiowZixVVlshHBIN8kiXxzNWslqZO3PGVT1BWvhLy6DEeE1C0wQXyM2jjEvS0PMpBwrKDabna/u0nrblUARwlr3MoBzQUqHIFFUQ+KRoteqZ2u92JKJfiBJG/C4AyVYvuiKj8zM6OrTaWPS0ZhjhwT0h+pzNkkfvXG1BXV3TQD5hSb+sa7iZdSkW2rQhW4uwjVJzX3gH0FSwumUcIlqtB8TmqrCBXM5OidJj0TluAD/IFEC9m/IT4e7guSQFzG01cs0YLlXE/qs6L7JYe10+ZSJDYyPesxPS/OiW2IIOCwyT7KV+NbizGyXJKXIc+Erb/1bo6QXTnsbkLi+O2PozfKFvDDZRlltRsJiKbrgoivVWUEnUecUHBOYUxImczvlgN9YEm7aDxA2jFQKI9M51tEM/gwFPkjKFJ09O5Iw/v3ZkQMnfL+7pX5otUM+y5zoHF/li+8+Excoqu+HmPsKy8EvPWsmB6kqgN/VQxZJjH2acYnwbeHMkVMPXgGkB/F1Yrqyw1aKd6IT5gZtk9/9iudbdIo39lct7iV2jBoTrasLTSwONyIDQmj9vPK+uicuK+x6/Q07k+uEsbGpW0HjdrFaBsqeMfrn2ooolIbIR332l/ZITqpNf262y2RHGQ6tb6fYZkvhAds/oebjmFTfU9vRzIFUvuSCLI6gqqn5X0Z6h84bT0+XhEeGgETH+nDsEQjk2GgkUC23vJux05pZkO7PN3HzWJGHTe7wozSg9kTWnm+pyEY3kNMVuqJiHVrKW7+Ydh0hbhjIBREXbpcStlvNfVwyH7OsK28iz2bzQESHH/8Aek9Onroq1OnBuSthN9dzlSIQEO/gdV+QPIKl6vUrRsIGHztpityWBFtEwL+MzJc2/iOwHGTBPVFz0L2yX9RBEmzoewBAOBoRk8IHVjyZkboBUDp00fST1fauqnuZuPegVW+2vr53c5w0bDMKJGXhkyRi3Y/X2tLqq9reRdfMZlt9kVLndTE4Bt1l4WafUZ3Udv8OyHhSKL9YZe1hBXI34p0acVeRz9l8GTF5TJEQLv/r7cHbJeKFTuSKzRsoAsvJ2DvSrycpMejYqeO/YXmH0GLhk2U/dFLA2oo2gCMZytBKmOFfTm5hK/QgXn78YVOd/CMJ87GTjledRsavvPgeSrSahtT+NJhVX63jFyrgtYY/dRs4UsE+CYrZs0Xc2GA4e1DS/ChKBN6e3r39USc/rvVe9CVS3g1zP+IsXpdtdGVWwXtl84i4wHgg+Urx+jWTyvtlLvstB3xBAxgciByfnOql0LqHycVomW+dj/3AEKR8try7K9myEdbuZpYwjexgfR0ce3WcsqrFMUYKgN7D/VIm3IdAkQqIWSpHE4HCT5qbySq1gjq77qxQc9OvdO/YP5g3AiwZVcdRbeuI/FOQm4dPomuamrBP2aIEU+T6X0fdztEArBE8ZQZHXbwRST/WE54xjzolqexfDmqVZRo1bHjQiRhBaoNVhTto0lKSMoys4004/nio4YbBaOlhG9bSvLBUcuV4up+zwxh6cVGtkQDu7WQjQCQvlAEpLvuUmr6HtBdw7zZ8Smjeyq19lXtDmQuHg/34/fF51zbYY8wjYdb1/fajeNn9yuRrJro5NuKL+AjGChzVVO8IvG9BovIU79oHgxa2uoG0NV1TLNbARuM1u6rEQZj+ufFaKu8jfQRy9uDyI+JelzttaSB9SfCCPmV38SpXE5mA3f/m5ZDCIo0whZXm4w7QGMh8krJjl4qK5hgQVMdM/q6hOJrcesqDJqUduWS07xt1/+JYgzZDJv07UiA1zLto5828K/LRto83ID78Oo2UlUVq+aXxVaRA5S2HbDqtP3u07LWJmUckCweb4sN/4JlRZF5EzBpjL3Vbc1Dxwv6OqWiGe3vfPdensTOLRgct047p1Y2M582vn4oYvgoJdqieLR2wkseCWo8a/ZCGODBNlT3ElgvmJScDkhz20NS7zrDd3OQJwiXLPjfzx8e952oB9ZwczesVY7nfybgYC6jbTwOGPeNyQLudFq9eXA6bwag06fsCA0cYqNgkUf2PkegCzT5B6We+TFWT1sHGPLbCgjb5mGmRXwWQGqhKvjEmfGWmF1nJdlriYFUHYt+DlxbBAMcdi/yTXNfnHO2FfYM9+iUvXyWDFc2H8ZEJzr2HG1TzkUpLFTYjrw6K/N83CkhrVmV2zatBOyggAYna1Mh0TO9SwAgIo5FRqywpZDZjPMXnZ2NbrgB4HmhQOhNEFh9G8a57jKb8eNxudnN6DL8QEBfTIv1bEyHtWRIJopik2sT0Sy1yu7xfsORlzBlwSlMuWWYPu913ZQBxfrTYK+IGQcTZ8gAg0b+lchBG0s7ljd2SXsVuproxv7xxSiQIF8H3GObG/L33zK8M+abN//Gor4/H5DkEJ66Xk0RpZrHAzCEARgSJT/HvBhTd3fa5dGxx68RoBWq1/nw9+Cq00cnZICF4ndDVGbHJOsxYM36VcBx93knxXgJfm6Y6gPvJRuxz+oXZAMJvragfSQhAm8eQxDFeOUso9GKBZEWjM4pFykHFhmIBM/Uf4CvxX9x+zTXGba+Nvu38YofuE6aTzgRJZ5MnjqaqZNk4Rg3523osiwo+aehh1iAZpZnjdeDtgJli++JtF+yvODYjdFKmuH0Tc9jvDnpTCBBXDLMJeyBkf6oze89BaQIPfpSanFNDBwhsoVw3j0avcHm1lT//D/JpkhlOqRc1FA2TK8sJVMk8tyewQROZVdKwwYL+vvb+0oSySvDLgTQmsDFRMMTpqgSELtBTbiU9sBi7WSdNH7qJK4OnToiMRkCCxGoQZH662j2JR9RDOOt6R4EBWAXxdx0IeeIxJ49NjFiCFLIjHhz4+DCi7oz4PtZ2V0B/fYkazZ0FoPeQvU1FmS3NAzE9da5cH9rTYwwiQg5ns92XBbzZbYbPO0zj7skPYpNRgSQToWFMuE14C3KeeI5haam2jTrB+Fl5MBc4BPOxt8DJ7NtpTezjBJlI6lIsr26b1DvF9vSFzNxd8N7ds9Ojyc35mAWCZyKVlZmtAzapW6oyqdUgsilRe+TBlkwiMRtYtMLsmVQXOsZet1AWVrmCjzAYOUrHlGHYkbCywk8Bshmd+jc+GyUBfKigWY3y4+wBLp0aq+myKmu5dJOynNUeRHxYBgyMftmBkcYFT5m7NUfkCieshKPTY3gkX88LQFMPstfVxMK8gyRunuf6eGkzyVcF/1trSk7+0Mdu+ga2nFbHwH04tQwY9h0FurDI6bzW6m0m5syc0mXM2qJTShDb1i7nFZZqv6oMvrJSliXzeTd4gzR7l6RJlNxidHMUMS3yzmfz8/e1GInUny3I1Sn4bgetT8RQLOyW9FFNJ59I/6SwGY4lRAk6boWRzYk2En9O8PcDRys4XAMEkVCJpObIcy6G0IX8E8svWuddikfom+Zfm1cKXWjotqoLlbPdp9H8oD6l+OBqHlCB+/4AWl6KpA3sVvdqYCAq9ZQLTSy9+j30uSv85tkUTJbt40B3FLX3bD4EIlv5z2ufYlRYGdTa6ouQfu5YwEYAVmoYzvVDWkxR3OYJDuZTwUqeMng5ixfhS6BS4QsfgSmu/AXjso+7b2nFiZ6GMcK3fd5eUgNy7rTytxG/DZ5bITh5k2N9vzjvpKw9ctYZbzYSblRRWlL5BuOk+VqiO18bIy+lBLmJEXgCTImXgSeV2hLIphbuzbFVAA3KsKvQSp0biUPM1KJ6Rq+NCDYMQjd3oUhF/2ptUgII/h0qeuxQuH2PxF/0qKvQYV8jCmyCvqyYL0u+u1UrPZb1yrI5Adlc3KzTklk9AUlBkul8UY7Nw/xbr3HCyLOanRTFb77A4i3mg4oU9GbDcjkL7VRASRfdpYKfW2j2Ceu7ZVp5BdzlkpN5Dnz/zGYdYsg+j1+A+XFvw41ZnF6+av97vlTQqHOp5SQaaV4b9DF9cqePtgO/N/tayrl99GKzIkq0yPNIloy9e6aKsQXsbgIDVNuS3sqimgKkPcwx94eb00ggYk4caQJmfBP1En/r2oNK32Y7tLJfuL3DCONnthE25ZDfvXER15cGqWDad33SSxgN0q1HEyS7UbLf9LHwG5wDhNTuieegSFWB9+SK9z81KMYtB1X1XHKN2FHLAtYyndNnWb5GAwjuDMeXSUSyD7HGyFolcceFqzpduflNUylN85gtX1Q8vIoBFOIuBLEYReSuyok1+LQHIBGcVLdF5yJwLuNd0vClJ/Ri72jZvB713ylESIDc+zQIcK2+/A5jTuWQX6fuJf8oIZnQGtepoHSVYICMWxW9MNYoD/kxbziPYn9JOjTiABYrPUtMTtfpYtgNbcX+ZiEI4CO9qEkQzkT6FgrilzVWdMmNfOMdXYVEL7ePJ3MmSF+SuRPiweWhzVT0Mqq5/lBPgAIWgX7sWtSPR1vOpZsbZAEUFX1ouxIcVmXF3jiMI9x2myFxmMyafimEIUv2doTSs4vMiTIR6aS71361zTH5lUg05abA1N40OyVR9nWfpMxF+kiOif2bcL3E6BPUdRUjZuLRoaI4opj5eWkVTiNTZ4fbxWh/i0KECDky5lITFk9hl6NO26DS4rSKACVig9PN1ZHKMt+530uKQd4mvtDJgFyfnERmG0V7zvS2/r5I9eYDHNRnO/UEhEqc8nGj0wfX/YofSdQNPCtIA0rxIq57jc0OZO6ECXSXbVA+KyJP/JVANNIbPu0tFRvZCpAWYlmT0PUuDRNSGXBpgvxfVaKcTV1/NOzKoQJpRG618U7DBtRe/jRu1LYWCwJS1cCg1TPWYVBWKLdI69x1QvqTtKdNZtna+53He63popRJ8YFKHQujbwkLj9c3B9u5XjFIINuRYNB8ZnZVwrngTZ8u5rGzbsrG8GaO+YBtYkZJHgrQ1fI55FH3IMleJJn8WM51MJrYFgW1GGbXZjPTPeWJKikgbzANKjtMgZQRHe6C7KvtL/u6VTBt07hntvjMTRw/Nu1A/CJSI4SDBDvyLw39lAqSyyT6MVFVyi1Inviv6cRTVB/zTSP4hhBtcYmqFxYvcmBzbvSXtMSH1aQEkkrCFFISijq4PqKUcmGtj46vzaY9i+NFRAD3IJaUJbDzsAqVNvbPfJGkIcIfcDr6yiXTDcu0gXOH3ZsvzVnedCNbMH9+0nD/KKCYWv5PP8Snt7qjbafnuIe5bhyBlCNQjrzhT0dbYM2w2GfrvEZrPnjbH2C47+EpNEyAZvwTfi6mvMw0UEI4urvRLf+WBQ+xuH6pkbk017qr1VF1W8orpFn9wXFXIvSLhZZmWut8D15F0ykl/zRrVe2/hTy43r69yVsg035+HqrL6hdi6+qFWcovh2nFUm/LdrB+RubMA6+74Dentna2qfNbwlH2CceuQ8nt3sD4+Qb4cDETvsTj14AbRD1oztMg8Uni9gsSs14KV6LcmWE+0nPQ7CNDX9z3JlRL0sE7GQ3WzAFnSK9pRMJfhDzefWIAF9jNAIWyd5iHls5ZX/ZbwBIccrEufXpK3G4D8Yo7NP/l7hIwoo6ANv/aiDJ/k0vFpWbHidfy9kWnry1hrdG/oClBs/1Ud3VFl1fOussC6no3IiyzjLStYpmMhCDuKtD+BjArVP/uvX5r7Xl+4Zi5hHeHG47G2hiPPguMTj5Ms9QasBPC4W5djNPdccxkx3vwJU5bm1UFbKwe37hBJSV6URYIihf28kWGTz+6dtTmGzueqd1wOgPKLkWns8xYIgxbg/zqoqI9NgZlX7fhtEeHtgXg+8z3+K9P8q3Zt3dEWR0GbGTTss3I/pQMHuacEWKPM7mcHw6Pd2jw0lxdoT523MWH4rExcirgL0WpAF2Cjxvr+4uuFyPCdWFIAUG0mw3lAoCeq6lG2kUKJ3T73mAUANWbhcwT3KXH4GQfEEfSGlwXtrtgkm4f/vn/XzwDDfTDuE3n7qgRvkVfVQfcj62qbWJQ7chLORQ1cJzgVUuYuIiFXcrnUkidfNG7e88b7gOO2Dv0cntc4uPmOYkqTC/Js2vm2sIkof1C2g+3IkMcGIZ6AdVhlsil6wnM8EiiFfOlAer3zq0SUvyfY0ZyWoCiXzWbEQ0LQKEFLUmdPJocb7BBGU/lC94jwQJ9dEC2apikRZHEfIUP5d6pFOlNtvrZLg06+EqQU1M9hHREkyD0WoDY2SynNNQP7QsJRbS0g9B0ESAZEDAOAxlH9W6jlsvv87qY9Q2SmpNYqmbUsrvIj969G3oCb+rKvOsfFmELMybUVHx1GSQqLF9Oj9rdhZI9IKS4jtkZkBzVrFFaaVxMUVBp1B/qrJ3kJZc842s7OVHa0vYU21G+0maGToTg08HqeSSvKd01Il/lenC8JfUNyIo1hFGHmbN++HPwE/zcS+AgK8raxFKq1RABK1ak/9Js2ChGwq9abmvY4XAc/MmZffnQOQfoyJm/X78ne5pHuBnP3PRKnNWlzbIYiTx13wqtjAQbj4C29fr3pmv7j4qrqjWJMFzKqJ49Wx0Sg6XYFz+3ruTXP2uwBsHc22tzTcyg5c0yVilsXxm51/GQi7/ZdVf4FN0AT2CJpdGdfJzF/HSGYa+xCiSX9M/ZoxxMbuNON8vimi2JffNxTTLn1a5Hrdgop6IZbm4idyeOykH2fauAigBjhwTo+Qcr1lvQYfYleyh/JIMvmi3X1/XDgNuIS5nVgoVHMJ8WUqYhP08MzCdT9GzIi7BFCO9+2yw6Ol1/W0E5zZauaYf9XUlMCXVSO5JrQbGKZkb6SrpFql/2FLPX5JNpvNhqkV2ypAjOaSVNDxGSX7fxwkt0ZqLnkYnJhcjFzmVP71/7JMYe43GcFx8Rbh37/U6l1PW1Jpwke0sA/tUAd5cEizPb/jAKis2720BgS9HRXfJCIgxpab97TtuF5Rz782KLKSyBN8x17XonqxPbcAyey+8kZeXUMnupRXix5ZKqReHVsYQYWODnC6YqjSx6k1j//HtMwBhLbDG1aLMFpP6+YveZJfBMG2GdF2phKdBhAinTngCxDU5nmruuOFz3akNo4K1OuyjY6zfkk8RCq++9awTb2U1dzkENWBeFcXjVr/X4LFD2gHzog6LA2udL4uGP57VN1sVH3IwGY/fA1yeVJAFD4SXXNu17bQJbtlftwuw8BMZSQ6mdM6+AYphQYaKi1fPfZBKu0yOjcmbyovR+0PnTvFO6Pq33mChY6DJbG4+Mxe7HkxWRsrSdJu6Edlogy/tXIWDM1Mnv1BXr9Q202Oc5o4cKAKzpa4QEkHVLqj0FLwIFaMyAYcIIoIzdpYiGpplMuAA6WfAVL24cRO9lPi8wftAc2P+hrivMLTW1p3lqOKEaKprIm3gyc5b5fxVOS81ug0dasGtmusa+LTKISZHHflnpDm0TWcBgbFL4eW7apZBlmXElHLnypw39aKbdfbJA578HmMO+eZyPFzV6JltJC8a8/mU1BUZapcK2z/BJf7lJ67BSq7MxVfDua2O8K88e/omwk4VbWl4febqOI6pGakeJPkgYfdsIRVcg8YAW8tyLuEdplErbhVc9GoHupZB/d2dFtoHXUaKvTJtASEfGKGPGBzLnzw5IdGOG162EMbDC1xsYtl1WU4/1y5iCuKnyOHo37SpfNPuVnkvz58EcLHD55+ktgemXGFUnpEFz8Hjl615VB9TQRDYLVZ8EAHmFu/+860JqZ7Qpkou4HgQFY3kiJC6QKJZooIrsOn62cAZOy4x9HYaahWkMm9Np88Wb1vZ6Ca/1qN07lF72lgwe0W1dndqbW0TLUGQiKgSj0LQD1p3Yhfva8eT06vKPfeEteRDtNlUCBTzDmTCnoWvj8R5r2r3mLjj2mUAO1VTXU9qbvEPPL/1EX7iMjspdLQOEtoDe/9HsZRxRW12NlLfxWzEdnwf0dGdv1ICi9RGOqYuM5Us95uypEt2kuhkEKXTZ2ipQ7pw3MFMSXHXONIuVFSouKu/wDGFIb9L/ZZb8l/VdXW3Y5+9lCDLqE3rLJMwjIfK4sjkfYXgR2kTZO0JugU0cdqJx2aQmvgfKnzEOejyQAfpGp+2ZLHeLJClow97ExEQ+9iB+7hkL6cfn0ajpaznRjvSg4q2/T3MMdZoHAfjRan2MSLGL1a9nzm/Ut++fMkikOlOor0bJnEJOinhRe8nNMWyaQrYGXsuMaJfjBu4l3yS7BEK5g6R+yJ+07Ni+KrRs/5VICtc6+vmtGN+kEcjetMriiajcQyOymWg4lZumUhO8xrgqyb2QNDeH3WPk/fHIziB5SuLhPl871GBkKqiior9ylnX2tWlSXEuXeBCXOWxRc0PObiFo4tseHuMxs/Ru4eHFtThBplhD/UodSOtYbSYy7qtR3dzxVvS6ZmrVPqHuSLtA1tmCq3XoUdG0/AogRASGsWoISBKviLz7YE4XcRnqhRbRhewW37Up7EwxirC5pWpKS6mWQ/ZEQxaMndV2hE7lPgrrBd2Q+jIE63CspBLbM7XYz94SH5wWKZKmai+Tpb0E/ipKvFCQOfriMe1QWI4OHxDcz+JgzBkccua3xAXNYPM+JDKgJXZ8RtzBOvTTzNR64RqLbi6pMhApbSwZQ2th/DkCBXDIsZTbbZWsnfVhTgKPpY4ZG0mRwCFILVJtW3GRFxYyZFaIFcX9Am7GF4OPFLxmBHQhFcE/dGTxEVcHHZDKTOMpV8A8n8Y0BHFl4ESnhPT5XV90rgZMr5DwVDA="}]}]}, {"codeGareDepart": "190", "ordre": 10, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T14:50:00+01:00", "dateTimeArrivee": "2025-05-15T15:23:00+01:00", "durationTrajet": "00:33:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 745, "numeroCommercial": "A24", "hasPlacement": false, "dateHeureDepart": "2025-05-15T14:50:00+01:00", "dateHeureArrivee": "2025-05-15T15:23:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:33:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 11, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T15:50:00+01:00", "dateTimeArrivee": "2025-05-15T16:23:00+01:00", "durationTrajet": "00:33:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 743, "numeroCommercial": "A26", "hasPlacement": false, "dateHeureDepart": "2025-05-15T15:50:00+01:00", "dateHeureArrivee": "2025-05-15T16:23:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:33:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "AEROPORT Med V", "to": "CASA VOYAGEURS", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}