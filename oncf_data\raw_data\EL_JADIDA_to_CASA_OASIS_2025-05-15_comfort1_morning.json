{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "610", "ordre": 1, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T06:30:00+01:00", "dateTimeArrivee": "2025-05-15T07:44:00+01:00", "durationTrajet": "01:14:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 707, "numeroCommercial": "802", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:30:00+01:00", "dateHeureArrivee": "2025-05-15T07:44:00+01:00", "codeGareDepart": "610", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:14:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "610", "ordre": 2, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T07:30:00+01:00", "dateTimeArrivee": "2025-05-15T08:43:00+01:00", "durationTrajet": "01:13:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 706, "numeroCommercial": "804", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:30:00+01:00", "dateHeureArrivee": "2025-05-15T08:43:00+01:00", "codeGareDepart": "610", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:13:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "610", "ordre": 3, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T08:25:00+01:00", "dateTimeArrivee": "2025-05-15T09:27:00+01:00", "durationTrajet": "01:02:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 6459, "numeroCommercial": "V80020", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:25:00+01:00", "dateHeureArrivee": "2025-05-15T09:27:00+01:00", "codeGareDepart": "610", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:02:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "610", "ordre": 4, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T10:35:00+01:00", "dateTimeArrivee": "2025-05-15T11:51:00+01:00", "durationTrajet": "01:16:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 619, "numeroCommercial": "808", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:35:00+01:00", "dateHeureArrivee": "2025-05-15T11:51:00+01:00", "codeGareDepart": "610", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:16:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "610", "ordre": 5, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T12:35:00+01:00", "dateTimeArrivee": "2025-05-15T13:51:00+01:00", "durationTrajet": "01:16:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 621, "numeroCommercial": "810", "hasPlacement": false, "dateHeureDepart": "2025-05-15T12:35:00+01:00", "dateHeureArrivee": "2025-05-15T13:51:00+01:00", "codeGareDepart": "610", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:16:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsoAoqTLY3Jbu6O2dZlfMAI3PsQjM/d7GlQqPdssKjWYZ/Ijkce6hSUTXQkhJRTU9vzapd3EYXzGsXePYLkd1K4zOpc5XldiYLPwYiYdL2i44mWoi1yCiHok/83voguPq2Oywop9Xj4SUwTBlJ8i2yAgMeu1fy8NewuzOGY34ZFgSJYL1agcwp69K1UCh7aI/yAFunk7c8t84dPht9r8OnzBcBtX09cJeAtc87x4kwwV6nAQKpgo4D+CEbsjXLIivGi9dLAYLcNdoT0Rs3fcvWNo9/xyssc5nkHJrKH57Gx60l1qKqJR/0+CXhQxM3zO20Hv/UeNDPWlbsviZ+idx+eTRyzog5clAVezCJOAmzW2TEtTDCb/wkNmOYvMLPegxLyYVyXAxyoIQ9mklSBncJ5KcK/MnebFpZDwIR06DCBEK2d3w6NoeHOMBAxGZ4y/38FI2q9UHSZFDsO8t2i7n89yUyaylJ916+1ezm84LFe4m2Bj7VqIUEoxWWDD7D2hY0nJD1Lkfx4w81nwVcKHz3gAqQa4zSy74Q5H/4PJ01jVT5dLmbWD6SJEXKpzc7VPffj7sydDvDA1UqdKzEkMz0VPtrwWGWenSkz1jpQYTUr+qQZX9uqyErarKOq9ZDJiuKjtg8yzSV+NW6AxnyX32scJj1CMzw+m7f8bbOoOIljHrEOMqosMj9X61c7DO764Zh8nzZ/DmEGm5ZmI3WmLBYs/yk3NNgpghlb77l3mmlkPJJEz8hN0k8Am6vMGWyVZ0WvUlB1aXLeCA6UzFyhIRbbOF5fqPCmf9Ywd+FFpqC7sisflmkZUXt3gxHbJ0ItrL3sIaE39AK2H8IlN/IH5PG5HuIFa1jlBz5ahQXCazjI6+NcKZcnnjOK1UDvOuaRRWE5z76RiQgb0cp9yuHSzVcvHqT4d7o3XlcOY2suR3ysONpB3waFb5KbE/Qnos29qY+0uCFF5LrmsP6yFnfgrt4+eEXghMiRmsUUYY1S30q/+nmTabJ8h53pAKqZhggFI64xiTajbfBp6BzztlRpUGIYeyCfZhJfLOBTDqlYGB+egqipZZjdpsDm+GOVZ4DajLspiaiK7//kFuQ4WFQzq6DwPr+Mr9+4KIfWVEAUeWz+lwviOUXCDtgNXoZan5HpxeSrK1U18QL/jfAFlQSxZG+UhdG5q0PARSSyNkHAH8pI06zt2P+LuZ6aeDqo+bMcO+/VHYLmgJyFfc/eT1n8gFheSxNyK6mCZce9b3bYW4mBzTae5l9/eprru/wtAWyn092P1JjpfvxwP46K7dJYZG0/++chl5lrFAhJhzGLvfYlPc6OqHKM6fb6h8zMPlIR7OsLL9F3PjfmGXeJF16/x8RoqZZ96wByCDj1Kp88EgpqYIbS2RTRrni622hiL3ztdqwENm+kzS1XHtw5Ewzt/3SmVX8r9yNkLCi8V8HRi0dnzsxayMaa9S3ZRdJKNRbqiFW0LkJEq3b4zVG9SfWvhVj4hJQryPXJdg5lQpj4SMrzbLGdOXlvOw7KmXtpTRfnboBO/5aLoyXDHhT7qScg2KxDJaPwZrw1C1wuLSiBw18hpX+OG2CctQtNZOiVU6NtW4m8h5/YsDQcrGchHsg2pLNZ21qYD8sgvhAtlCLiOQPu+Mw3tPnir2320RTF0LbSv3dtrQX7wn/Jargw9yacSZXKY+xCu4A6KjDEV+dSs7oO+SnOYe5qnhIDArcxARGj58p98HlmpuOXuXEYsZZipzq2GpXaLOcyrFAoM34tk2+kdxXNLDx4i2KJwNGeRlEkxsBULcvL7WkrtP8/Noie+se8LqnpoJKpQjVWSRCjIHoxUwE4wYJndmlX7A7u7ZFjRjREyGCn47J0wlPTD4OKup4gosZtnNNqM42tbd2iHgasCVNwvqLVsm+2+KXkHdAZJMxsDW/W1rX6QdZwjAQ1YoVwRo3m6uzplxQUT9qWI68HDtMY9ag0n40ZXrmka/Ylq+iIQY6LPfzh7qccJdqp1fMs9nZF8DwxyZqeAOYTRawgs7nvDcCgja/lAY4RROUtOHebC/9U4BHAqn6qZkp4hszufWDiPxgE8C4b6dsPiDI1QFdNAcA3sKECBG3nERp/r1ZXKh9tLg5FGBFZjmmR5sucSIhLQgNgPMWz9ZR3W1VZ410yeWo/dCXTrbqpoVNaXrwgPSCa66S9tvWqmfcwMpl6aIz66vXPPCX+KOuYHx5m4qEbqZhQ/Uk86/BJtE8s63dnpHbJdjd8EqNTcmHby4Ry7C4i2yukM4N1cmAFtGkEWP1V+5Ic8LUqm2a97N674T4LiIGF1pgKQqhtlbGW4Rv2w1IhN/TSC3DhMrJikXAKkBbUSeCmLwHgrF59g3QQbMHOHaRQ4e4W67H/IiLIZRzlRbRJt4e7ku23LtpID0JfnCNY82UTA5kcNSvaepOf3oKNQzdYJNZs38pNBycQovXBSYZoRAX76hQOhMyQqljV8bpLWvGDeb11CVwohUWLFiASUOhtBrtp4nUWkgNcM7iWKl32G1Uj86Z/3ztxR/cH92VAXgQswIeSbn3sltYwHRB1Be2S+qjD7bHvwniaNhTzh8799LwM+klWjFWT8sNOy3aXQpP000HjLJKMduA1mSkAHib5jCedCbiDhDzWzd7gwX2OVgf2GLcgC62IoH46PIJ3smyrKwALk9sf6dKQSkXS19ae674yxAOuJLCgnx5k/2JxqjK1ex2N8/8/guaWZUjNouwbuSyw24zAYRK3Egw2lk5RPG9uJ23jMhQyjdzavBMpZxRYMDx/mrh22+7gNJPSE1TEROFoi7t16YI26V1lDo9DHhY1lV1vKNKrKBQxJSgdqfao957IQ/fZBiDw37YYJE1P/mQd8/XdGkcXfQqXwKOuIVOllgOWWXhtGHBR/BDi0WfeowIgiGYzJs+Vh6Pc4hNOT5TizUQUdm5/VhQAI18//s4yzW2FiY120C3f1Ht9MY3CNXhYsQoA9SbIVLDL8oXgx8jN1jhudMOjWZwlmjyhWcnxoI5O5ajmNv/N8tb4JRSYn6IKkciomr5nIWhDM3MUK73YDld2W7YChBXWkAGF3UgntszK3ZflYpHbjGvb1IlQvwWw9+JHsgNbOV+vWJ8yQ9h+xCIvRFVK/TMUWHfQ14ToPXS80L63L/ibwK35GGwno+V7sHSDHemp3WRh6RAoGFFFHrQsg+VMyZsvJ8hs3qFRGr/DqN284l9SVPyeMEgDw09Lc1xbxO6khSUMkHYvRq/WIQW4qEO3LOJkFaliDozNO4/9vCMVEOFSQ0Rx+y0j1CCE2k1/JGWZSCl+R5jTj58H0NvZO9S5ojBgArsKR4fXPFcv9bNR/NHsMevKZ/IK+BpXrReqADRDEQiDWJUTU5wEmJEn6m197s6qLRwMfEXJVVXXPIZuVQl7TnatgzAhDv1nTnlZTr86u2m2nmTazHRWrCmTm0HTZAk8hb8vf4PUkY+HTCBFyiQtKNn8jZNs0rHJ/2MOuuEBegMXsnZ1okyqX629CtFQuppi4uoL9t+amd7/kHpmj5YWbZw56x/9WZCLSh7kph+jvQDLKiplKZjLyDBAKCkifuvpkMHU1WBj0Q3qfah85J+PhUlXa1DlaDj2UQrqTvhxvOk9pofnCGoTehLvo3RfSFdkjcZ5MYgtXX3MwysWTrfMcisoT+2knOWsJzIpPm3bDDIj008gPtRxxV/+JoQc36+Bl+oDhbf5jv3pHS7uJWRJoXMQGyPUZuprGo/hDA3W8H8B868AxY915RX1Azw1CotEQLHyLSaJqFgo4pS+yD/5xg0mBYqGDTZR+4nIPBD4vl8UBSc3px8wAhenSgBhu0DTle6uuYyPgdoNT/4ZwJ7Z6A0ZXZZwFRY3/DwYaMrIcmbGkAuwNoLr8cEXBTZp9w+4wnANcEqszLFyB7UZOeoxmes4hjltITyqGy5nEpGwnkayEZn6WC1es/pStBpG4CZG3X5kMiEu/k7dLmVhbAe3vfrwvOdcgiyaXXoQ0puLU2lTGifHqsEA4ErXxg3BVrwm4j+Smt3gIM5Dmg0h3AMRA7i6K3wcRE3Bql7JYACHGXziXsGEWlKMooBVynw6iizCcvGuJUGf0Oak6Qk9zojQ/Rb2Zm17F98Ydd7ZctsxWOzGXRsRgcaflYW0NdTwJKRqpkb7C3RWrWaXCJZTBIBXhbi3hwllPcMqx3l1Mc7KbuXWVZ4cbNMDglsXua9rsTbwpYyJ7vu3TcOhrJ5EAu+iHPX0XFRkllZLfkFt21yXUSkNtbk+PiWBb3b/5N8MVG9S9/k5uiT35bBiSgUx/9l+o4WLGnKqFFR1F4xUgaHprZVd/zbxTYqmJ4xWuiV2v8jKZOgbbIVlxrAf8ICvIjq4byM8d2uhCL0i6CCd6rLej5vcRvyD1FCbWBgMtobllhjwSpK6ldh13nkf+xZcnUzCPp5fKYXxZp/bAvKVvP77mNEMhBrnJmIXNGZLAEpaAvwkY+lvpVuxPVxLM6c12qfJFjOowwl9bnqTSXsvTsOCWlx3b5fOeJDQU+GZghWc50ce+eySzCfpqGmY21GNsGsefCtxPgvLm4QdsywS70jy5Sn1Cmdfbeiokwl4o2Hv+iXw2hfDNJlP4DefRX51NQnGHgitCcOoiKCVFBpBdnLKLCG9j0oKfRyzb1UfC87ad9imC8dkSrc+C9onB8oXiYTTpw9FoNjNVRxkYXN2hQ6OTDXgrevhCQzgk5ZuMk7gsFe72onVaVcn+HGWKvFDOLTAKd8oh5iSWYlvVxnkN5NUfOGJIaUKrqk/mYVU+KkKqTXzPsQxaaiSjK9l+YlnOlpP8eRMJvU389uk+IveMDInuORr6TNvOKh9DiORzwCQxMwSvN/F24OLXzgyXYeLaR8SGIvGQ9XR75o/3WKEMveSZnwNoYfOkKqEY/jiLBfHrdORQqfJqCenALdvnS3LMyZBfG5jt1TkYDL9JJo4Klq7ogKWrIsycMILXJU+XNZUGz0QPaHWBl0eajOBnf4DPp88hF7mPD2UgRIomlx2RyRJFPMUDY1Eh4yLmEdackFiAKH8z40eBYIPFQNJbBFW/VO+40HDmlBHgBFFMCp4L8WH37hvRzuaENtPbYyS/ypkDBZpLgC/V64o7q/wFYT17F1g9ojyvK036B5JrELQBleU2Xh/eRKG3706zjCchhmbZrp+7wOeI7hUwaocsWNkhOTkpmnTT4AgNW1HHsBqvnkb4GpfAiO3yFJl69U3udIuLC1EUdZhRVGIZUC5bktXlhTsi/CiDAl9jJ5gwUQUfiRvxdm4cXC8ulvJRz80uE3zPu2meKGW5IaZG/IomeHomzaETyMnlvJNpLljlsuhrch9u164ckM4getcMAHC/imWTfYu7F/5jDn1c3eWXBJGa62wS8YVAIcZifqXsRcvHq5at/c759r2hnxzgunBoUQaI8gJdzifWsXYbWJbsjG8ZEnPSGRfgo/aL09IKa2Pf+fWYw0nawbkqpKKbAi3UeSNJ56Bgr/JL+kZ11DecTAKgRE/EZeF+t0I2rb0MnhbY43dyBrSjhFPDDgtFlaThmxdz3fyKy2qL1nzveFmif6cALUdHy9N54tbN1vvM91e8PMxQA6gfH7W/P5qP99Dr5gsnVjIaU71/3Hn1mxSl2wTSzIVZgd0dARWel55+dMOFe86+9k1ceabgnQyO0QBpgGl4A+IzgiMF3PTUDOJ9fc4OA2U6bzBeGTW/hRXQbkcwxDd6/FZMsTvX3ZPbR0hWITXF2u8LHMa/ajpDKgv+QD26xxK5OmMZxpA3uWU6hHNcAi/f2gvlzqJkJPoSV6GYqNLZc8RHzVFEyX5c4GfzdDZ+1jUtD26ASA/PLUCtfvDdM+lrc310MC/tk+hna5myEPeDMdcP75hdCTCXUVBu6GZrSALfjnlpRR29dPvQkzvOf5DllbfcmaV0Z0E34H/Ypf3YxhO+Z3cFU6jUG5ZS7kvUxBUMKL6VS201zSz/PiM/FmRBYrk6m3J4X4r87PyvPwOgzc3IURaWi74eoI/0snJ+fMlCZhA7YPZkq++W7YckX9SICoEK4KXSFN5NlWUdjjsrOEuDWmOQXFp70h6+LgqfE5NwsfmHLWXOGn4i5l06bl8IjvIWXYFqnXm4OJrH8x1bCLMd+nnSDBgvD9pjasikVOBBfIERArE5jrGn8xYZ18Lg00y+00qHBob5Usw4oj3AzUxoacl6y+IlhGNW6QQeyLn/L8i2afK8kcSIVX/kAf9BO3AAjgvtD7WdW1+NGnywilwwpHFfKcJUHYppa+OkHTtWayJglvP9FUR303oxk0HB6y0D4SK500f/HPFWQ004eC3TatupI0JCprDywpovKXdIsciv+9PF0k8/TB6uYnugAA4tl/ZU7WYuO+TJt6LwbomiEUJlZTMQ/m7DOVXGBMr4LuMq4eecpW80wQWTTjYz4sstmaXIEOMJT33iAUu9OhNMCSA+h7/kP5j87YV1c8XByDq3RGhjOLqRcC4yXP8DzXgA7qQ6YkQbcGBESvbDOcVh//5k/IGbuV8fWLUsQSa7e69/36iKeoHeJB0Y5A0Bv1IgM3GcmFtXzVVyMA+7s0wBIMw+Gs1uBH/qZ5E/BO/kch7ybcZ8RIX2G4B8IYCIytPQTVBPLUpHck3xqEcpFVqLxUOxE9GhPgjgyWRxGuZFt1tXqgogXwXZ6TbFXdFB69S2VtBOta90RnwOGgtfFjKSJXcncHreZRoK1d1jC14ZfnbPMs03yd3s2vM+2KaMFrB8TpjD3K2r0wNmX17YVNPjJtLns1mdTMKKhFuQb86YyBj5LQEnNFoi10kX6+4e2wHAmsq7krNBw6m3747sIWjLYaktdgYdH7SJDnp+OQ356uW1X2lUndXuqU8kncms5Ffxe0Upo05TCrqQUlfC583tlzZ3ktFRuQCTC3aNqoXt3Dkm3+anteXjQDEnSYo+2a7N1y49cUxzT+d5bOtCOTNQO/o+sxZ/g3qtCWghPvF8hjBqlWmseaGuRJnYilFDmF/HZrLE4BYhjgKRXDhmw2q8LN1rF4fUaVuEe6gbRn/c47WhvNKWkzOj4oEqn3jX1SD5KEqJy74FCrSeZUueUBCu5c4DpwGDyOT6QWExwWboaiEkDnmhcZ0cLe6p/J0rHwVISUmJb37UOhEudiOtqTJ5AArg2rK1Qn6kJIji2dw5a3VAP3zl3pX590btayLxHSsbsUJDW828OGxKtjqEs/dLOifn6E7wjwt5xJh1nNLrafOP7N/W6OzNffxvpUzPzkEyL0PDBvSJWetivkcMzfYzlPXXKTKfVDqYkjEMXIgKPTGTb0nx7peOkoFWI2rwK2cr2254zCL3aPH8RKjM2eSark+fbd61GkDOY/bOToN9Yk6EXJLdSMwolywTGPV7Gh+Vc3eaBdmYK3cWJily7iLkMwORvwnAsnelp9k16LtuyPfKftIaDwnxUUlOqFKYF2YpTOg8CvoNwvsCvO4E3zrJy7S+A3Uzhd9GbvgKHqRENpS5Azhn4IAc5YwaPmo2ErBobrufmqYJYmw0Rlrnz5NwgpoTEIeDg3WKnrg76/YxtjOPRKQSFTSdxP5Qx3In1Av6fIPpB7Bd5pBiR1EFHMBqE98OC+IV5VBk6pdyBFdqapvQi+4WQcy4XD7HscSJKXhTnwA401KE9dMrWHHsRNtsZlfAFWtZ0pikRsKm+5sOiOpjB8Uu4aYdluEvqFTrTH5dcmQ9pTUfQt6ak1+N+z1znlNE67ceGR88DPwTF5baLa9MvLmziKoRKhivFYWhApsw5izWP4sU2F038ARS4Wv9VYiAvO2/PD834wCR1JkmqbzOq2gNb68SY17vRbP99glQ0hNnYeSQlZjdL7NWv6gJcD/+6usLOmxPZpD169u9hXzH48wrgRJQv5O2NZ8+2wQq74yjw2SvKnh/CBuT8PYl5lHejCzHb8KWMVu/ZErhM+MvvqTzRyK6yfxx29wAItN5V+IFU6QTcF2jHqS/ljy4M5VUbT5J1X2jGgBTU4kZqoabyaXtreJV7AWZWvjmXc3sUV1vj8VS3i3sRS20rbZafbbZxhHgC3iwghPedEOVUJ+iEf0miAkV2wQpHUf5J2TTkYnpBGpuMuIpLhSjQi7N7LCnjQ4ZqCRcDZ5EkrerbBWFhNpBCul5C74bWAO4Je3qOhzaK3+Oap4+lX5mPWOfxnjPTnBRNL0ynf33kupngnwq9lCS/9xVaw6bSs/VcyyTB/vZDI33khe5qGaT6Bcf6XLvGazhNiHxofOzz7QBNH6v4nZvXNm3RbU84X2ysM4pwHY3iCdmJ8srXT4CpPAiOC2Veem3fljEeUa2nl2zfueCC1QOn3Ir1/HMGS0+d++cwbarlBbkHEOq29/c2WOw2avffPKt1YfGwU06meNrnnFkpaDTebd9+fd3PMue/TJqNJDIw5ZMCVyQiJF75dL2x/qOgXq1FYzz7qbvy8Q5Qer9r7EHxGpLE95xvvKouHVin3/tMxdqRwlOefqyopJBLJib3f3r1NyjySOkuVAnVJznGpYu3CNoJ+84n6qf5rPh20wtO8SHoWkflgz2tju2ncCBvC1wDns2QXaUhgxtIr9nlPW2bsZkHzksKtviHMaPfUEo2RUx2zwEn+EDPDhQQmYBYoouRfSJ38TA9u5l92Aycu16Vj09a2SasUWLiwsjLjfhFqsbUsn9xJDL6xKV2uVIk893kXQGD201jj2G3VYttObbaV4rPutI6+NqNYkT6/MM08VuYRa1v/yhgoRfYiTwyfUSBEASe/LMmOSxuafrnrTZ9l2hm6Jd19ejXG6N2ReOQJxs17sjldpJfiM5JIYPctb2rmL8i/nhQay6VU47LCnmrTHghNS+tocLl4FAxihd/5r2LDQjCxxR6c+fQg0zlYh7j8G2+4Xex1gPEhAdZindVcgff4jupopRij78n/QpPR6erdv64mVjYVQy56w8z6ckdvlFi4mkNfc7NCJBxz1vTHT8la1gddwdlZT1eVlU8Cvhbe50+x/xSMv5hvd8b/Q61Ud5ApJDwnN2Vos/lMkw4jMUshU5OK72argD+6NGIKa2fQ+DD3Rrj/yjfiEe/EgEb4yncSN+oDAeOHWjrRn7au3gvyt+c0or6c4AbQNI23lAeKEshRprm3EjQjgc7HBtZ0pxWVBg4QCEfSFWmEEuuM5Gy9dxjEyIi+qUQlVTiEaK7ZXUkuxsSJJ7Q0mk7JINrANzKEdRJjtP2c7zIHblNOVKycwpIea+FIrvVQxcAVCM7V14UcR8KZe8XXZdwjdjJ+JXzi4naCwXvgRSspW91eEuft3hG80Es8if77P4unA/i7f5adlwwPDRXF5ZbzgswoZZ/zsdh1pwChYA6M/Mi7joyMi8tDgd6VS+xdFobAq1Z3pP+JJNyb4vP4jNu74Cf8kzxJVf+gR2zVHXcJFv1y98H4GxmfaXUqIT0DBkftrOHGwF0Dh2a2QDG3AL/kESPsWsdGavnev2kYT20CWEh/OV/0Qcjo7aAguaAoutC7GwJuOzoVJaZjgXLr0GWmkmvO3lkVFXJlWOq0RyFSvDJu5HNxhMD0HWu11jPRfesYb2O8NmUbjGgNXTdDSr0jHXwDv7yT3hWvleQMOuodjjOVUszF6w4MRlg8EA4tT6AEmsOypPJpGnzRNSZmJDr4djZrsrWWRlFRJIvO3EsuoewlgF/MJOlo93H3tWW7mpChbWfMrKRb3ejdtgZhmDE08HvD31KB12xPzeI2aSoF3qg0WcfVbROx6zBaavZALMd1UG4HvFm2qLx9mPT3uv3RzZ0AsfkoRrewNqtF61YdZc6ZYTa5xhqyp6Dfzk/MqScDxks6ZjKp0nS8y6eB4aEc/yLMOmgPUkGI/YudRIMJtPUzBzyz3/VjL9o63leRvQjPi19eh3/fs++XJsRZ0UxDJf3h6gjzbZqypEi+N3nz6HOaaSdjmw7zuJYbl/27SbWxrYYztcCB0CxLdBDaZbZEPBEA6dAmHrxLD75+7yxIKu8VlSbl1kxVm/o="}]}]}, {"codeGareDepart": "610", "ordre": 6, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T14:35:00+01:00", "dateTimeArrivee": "2025-05-15T15:51:00+01:00", "durationTrajet": "01:16:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 624, "numeroCommercial": "812", "hasPlacement": false, "dateHeureDepart": "2025-05-15T14:35:00+01:00", "dateHeureArrivee": "2025-05-15T15:51:00+01:00", "codeGareDepart": "610", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:16:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "610", "ordre": 7, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T16:35:00+01:00", "dateTimeArrivee": "2025-05-15T17:51:00+01:00", "durationTrajet": "01:16:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 623, "numeroCommercial": "814", "hasPlacement": false, "dateHeureDepart": "2025-05-15T16:35:00+01:00", "dateHeureArrivee": "2025-05-15T17:51:00+01:00", "codeGareDepart": "610", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:16:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 3, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsoAoqTLY3Jbu6O2dZlfMAI3PsQjM/d7GlQqPdssKjWYZ/Ijkce6hSUTXQkhJRTU9vzapd3EYXzGsXePYLkd1K4zOpc5XldiYLPwYiYdL2i44mWoi1yCiHok/83voguPq2Oywop9Xj4SUwTBlJ8i2yAgMeu1fy8NewuzOGY34ZFgSJYL1agcwp69K1UCh7aI/yAFunk7c8t84dPht9r8OnzBcBtX09cJeAtc87x4kwwV6nAQKpgo4D+CEbsjXLIivGi9dLAYLcNdoT0Rs3fcvWNo9/xyssc5nkHJrKH57Gx60l1qKqJR/0+CXhQxM3zO20Hv/UeNDPWlbsviZ+idx+eTRyzog5clAVezCJOAmzW2TEtTDCb/wkNmOYvMLPegxLyYVyXAxyoIQ9mklSBncJ5KcK/MnebFpZDwIR06DCBEK2d3w6NoeHOMBAxGZ4y/38FI2q9UHSZFDsO8t2i7n89yUyaylJ916+1ezm84LFe4m2Bj7VqIUEoxWWDD7D2hY0nJD1Lkfx4w81nwVcKHz3gAqQa4zSy74Q5H/4PJ01jVT5dLmbWD6SJEXKpzc7VPffj7sydDvDA1UqdKzEkMz0VPtrwWGWenSkz1jpQYTUr+qQZX9uqyErarKOq9ZDJiuKjtg8yzSV+NW6AxnyX32scJj1CMzw+m7f8bbOoOIljHrEOMqosMj9X61c7DO764ZhK446/1vclCbdsiNfOwCsus3Yj7TOZwo/0Fx0ZM1ArETcRHiDX5fwcmIiTMsDtoxFSG8XQKt1DD4K9rPy7OaZMKAME2z4i5E1OptqAlXlkPG/tbZL6lIHu/d8FqU66j5iF1SVxb/wmvjc6Xa6dOunl9o44mqOcn0VQMFS3KtjEf414LhgrLpb5RsDHKiasfjKWTclV5HdEOI0X91AJe2r6LdpvX1tXrqx3guJG1zYj4sjmBQ5YssgfZ3Zl2MEJFMhP0fWd2J3U1ceYSHIaFVEXJ8v/bMUyWOHNr5ibJ46eW3IKV7ApRlyOzy4Cv3D2UeYYbYRjcTLAGsWyVvHoIrFeMU+7ekMO8CGHJVb9r+zWX+oaFwkBy03tXFftIWwftC4SBefEXOSOHDlPuKMBabUT3ALGW5MYXbhjkPGV1qHMhkokUtkbhyEyYNeiqSSA2V7wZeBg+P3nCe0veuWlnPyhvqXwjn2L+ke/KOIVQ/L/CA/6qWr5p8c+ua63oXYAyrbnKtXoh0WwITJYdLL9mnjXcpLwgx8NmXt92Vt1+iga8R4MaTTmC5L46+ZBj2efkk4SKRlovkK6TayzprfXlST6DjiItT/YoPystoZcZGjHt1R6lXcJVCvXbLoXWm3JlpjKQZOfbPwtcH+9zyBT3eou32JiKkEXZOitXpi26o6DA9sBfvhuFlsNETEzveqtWKnA1Ha3D6LXmOomyKiWlbqVMpLlBPZCvy8z4sly/9dqUTAMinL4IG4UPKwr1pjMzpsTltLTT84bvbbgffQ3WTauwFLOoEYiasXOcMcue99Hb8Dn03OhNZFnYKmgsAcfEXnXxxjlPuLcT5/Wni7RwghgV2iaER9p6z/ELFBOj7qujst2sO2ZlHHr9i/fh/2Fyi5mX2As0orGIjnUOfiU2QhBhfasgfT+FOP/2Z+Vhrx+16vfvp4H+Nf86WDSzxA5+as2TvLfl56M0CQB2sBrK+hI2ZWTCJmKEA2xXnTGp5bI1h/U6pH00eUXD8S3ZHCOiHQ5dNR8F5GDUqEaKV2VwQsySJog7FcxvgFUKnpyT7i39GC0LqQXayEe4cSfVQQkQQADGsPyaRCpmOyladDBG5NwwNRrThorA2SIkxRiWhCGWDrVCvJY3JCQSKZSSRuXoYuGApDRz8WylmhmrnSzZolGy0Eh7EkkK3iLK+jtHOAhjF1vSGXIt8yGXSBn0Jt/tPW3GazD+bFne7F9LhbwkXefEinls1kEHF8u8YfyjRbacZR5/Wei9nNhVle3YFFECAwRaBSkGltKs45F+uf9QYtxaKAVicO4Xc5tBG7MpsH8Gr9E/UnZ1kPCvfY7Rww5shaY/81ZzBwvks1TwapdaNk4tC6gnpIVSChq6r1849XefWxnslGym8FYQbWRIR7R7MZIn2T3nqn9AffyYk3b/R41v3MYfzxgdxjM1UwmlALGfNwuLqSiUnSJNK6cXpzsFzPgm00oSonEVBRnXcV/I417zjaiUfzPuMh9e4faW7mhTmE2drUXrFnV6OKPboX/7v49+0yAaZERII/PPb0wiDZI//Z8KGgdF3yXQP2oJuVvgj9YbLg94wZxnusVJqsPdpZgbXxH02MKIEavW5r9oRl3LlAEY/eC0FOEcpfLqKUAwGhJlxwPyD/v8l4FClkaigSaqcPgf0VpKJU+ukkNS0yy6suUAvWsSyxY8TM0O5jTAgcJL7lGCS1g6rTwsJNxUW2ZPzOXpKH0OoudxKN6pG6RyRJbuwj0UWesm9hSkvcxLw8hoVrdIMdaWKw8KfMtMzotUsV9oI8OXlaM/bcHwQ35lSW3rtDwmfVOC+QXrjj7ur8yJLuO+5mXiKQk9cOUJ9HSNALdilFWubnqCdwZYLyOf42bzR8GQmE0nqBHxMo25+7j1vIOqlG4PSTKwsH/rZrhUNhldSNVSgd4pbg1U9c5/IjzM9jI78Wz/9KNegttxShW9fG1k/LNeTb+zJ2z5siKRi/Xkqmdp71IEsCUCxUrIu2UTkgFKxnG1AowkvQVaYq/6FxCSuXkTihzfhOFzUstp8lxHurV+4AVklrLdlkd7RoAUyImAeIbqMePgJoqntqryUjOtVjYxDtwMPjI4MBDhpEP5yZEPDFFE46OOtAwveG+SCftrWplDG4eheGScLbWgJchlvlGpD0rC6mBNXc6TSrvT0Qruhd6ZmjCIdE4uBQ6wpwkuOCt4FCbj6ei3pKimOzJIvHjk0VRmyijsNF/9BFbnZQQCgPhhu7x4mGUcBWier8EiGOPOjf3qsqBq/pJyKD27p7tNSkdKxylO4+x4JIx++uof2kV12VFjIT0sHDE0o/slml1Dvz3xsCT1QPcJlCUO9xY+b4+cie/z+QooMxhvJUlOr7jDOo+8HkKWGJOroZTNdyAYTJnU+x9Xe7t4NQg13gZ/7C8un8RdYi8I9lAgOMHDRU7FZE3cRV/bRXZuh1C86B12ux90NpfsX0xlT5wn09EjemweYixjsOjewtlIKrmUB2i2F57EvcUX+zpbJua5JvhtLQcBAFKSJOppJcQ8OK8EPqU/Qt9eJ/IrYYBgC1ikXmzlF3qXUqEgyEX73CKQk79LmuSW3YKGzpbHGMqEKSDDzmGGbNFPD0vJqchiFBsFGGcf+vQO2QPp9DI4ug+C9Wadb2mp/sImYd6H9efMdG3StymKowGXjzfYcJn/as88SNpxK2vLy0zGGzt/oVBA4t+whPoKzLtbCJ14dveySqZnz5EF8sL7uaKL5WATho2luM/RHa1sXV4zf0jlLB/0Erk60+LCF/yQ/mXiHSMDJCtbgKLWbUoEhkfjvwTkkOvZhvd16ptLPAWHeNC9zcKsUMyoWxfvzuTDP+bgIOIWZAGOvqr4QDheOvKB2aOpy5PCupTULOB3IYad+dp57S/juB3zlza1maY/kP5iuIJJhW/oeYxXzVqiN1VsS3FxL6ZlDGkSXHqUKo0Kgj7Jl6+0SuRC5P/mncoUIJ4V8SMPopw9OlLBBqcqloqDiHNxOHKnAmzB7q4rgh2csOcM4usn5yqdnpIC7v+bEok9om+Ex3B/G3HliWyI9u0MZX2g2epKGWEhHf+vhifGnY1/XLJANO4Z6fknNeWDwmShL+XU3Wwu8sdWcwqS4OCQ5IBzCRaFaWbZSCxjetcktNNoiKXb4ydpTgQ08US4w3dQzHs73x3g+OgYJKzdijBNF5MQEy9YGDjlqBBEdIml4P3PXMES+DNQv8ntEOGlNvDFI84KjH/av0PUyO2LyuqdRF2w2I74rAxqsTZX9/PCsReQ71o/m7xgeaqMFn/HP6G97tVKKcuQ/iIl6olvvT1lwoUaaAd3elkhOrm0pqibM9PZ1X2yWbTHL4xyITCiXCT+hdeYt0IGundM/qntVM/21s+hCBFlAgaOKkqrd5aAPOe83ylM0NlLyz2S48DU5pzBHoUP7QRTvE42VJlgkKfMpnglJknGLEBtRT9ASqAs8dPxj49SkLvhdrXp84QhsS7o9CrMfM5gQmFK6ltAPhkaYSBIJfCddEWy2hPJkBJvnrNr0k9vzDwB4QQ2POnLm+zN5LTAoJfKjbPtc08DnvmVLpAMr/8s0CYd/qNyRIwt50R4hOeEMNpGWsXSTTJFizlbqbx54SwgZa47vT9+c+vFffk0l5F+xYMBx/CN1A8mDEoRL/HMqUitL9NYjnIim9nDgrJFO1+75KnNfyyijOBxGwvvp2/uzpvI0kvwjywVGuvuJNx5DgevxLYlzpEZ/vNKyfLgrD+pgzb7geqifczHbAiu5/bsWtPiWo4EhST0OeYLyVK60ZK/xZPcubMgTtUm0KaRTwNBQlGeIGPrIVmU/QAP8lhNFhmyu1x1D8BZQBi2xCEr4j3VNLckDlHl2nSyaUDYbxVRRcgCPmirIqE3xcsBvemrjZTK7aSQ49LhN521LkhXz0jNxa0LaU3bdVxHpUhqeq0mHvNmlJW18F6YYvsKVu1YD/yA0cpDNnJu6ZtVr/hhkYoJxP17RIC4uDrU9YZKHnWeIQS6KTmEH6LATGBM9roAWc7PHpPRrkyKNQA8TBdam89xMRKzCFlfS+WCZvvZD4T6wEgzXBu+YylaLDuSTCnPcBMJFWPtvg+/VdK5g2kJhbXQbSubaVJwY57KPNLyFPGIWjf3UNNJ6CwMG/cU1Wa7qaKyQC/x94ja7uggH/rN8uxTK/PtJIDa2WqlK1AyTeq8qSrLuvat44HIBMKQm7tj6LlXhP5IR4lPn81b0edJ1c04zwjltD8hk4MJ/MujKCEiIUY5eNwh9H7c0K/bGvr52gB+KkQ2LV6yNAoQmIw3wzQS/MmzyWnAfQZ8HJxjK+W44huFPLAdLsRQOyECJUoRu/lg55VMPsuGVEVmTt69Ih4w58YvUYgp98Ska0gjxV4uuJx5lTDNLOPvcxv3vCeFTUb+LcGlkyshAuaMO8lbQcTvshHR5dyHcYxuHEyhYNPIDxNZZElh25bitA1EWb9ZdcjnCJ8wEspNxVyw6wbKAJVcvWhn9ijA0KRCHEDt4PikzyGntiy9cHpA8joazkN6GMgVKKMyY+m7GgoyB7qwHEA7B9TUft93pc3C6GL2bJ1G2xo7OhXuqHEOjmK9LZw1vKaKQe0g4qPGhCSmw5B8Q4zo3S+nbNthEZ8AiE6aESe7bICs/g+QIk59r4uXtvq6wZOjwhfbtvqXpv/ML5bvk/NCfueZ1xPnyI+1h9/d8vGs/C0+8ye3RNPBC4ixLfTDyWR0LXymQR7MZgtb2YH9raz97TLCP/UtPcmmmdDj+lyymOrDNcVTaViNf0P8gvjZm4SWRw0BolwvmEaZghti9iqNm3v3lG1bzoZtWGhsgjU6TRAdBQhBxo5gVtUOgV4nfjjmWdcYkkT7LQx+k4Nz3IkKGP77vVOdnRw5T3we8vYj3YRte+vE6CK73+JCE6aFPKhcSIv0chrqdtnFt8ZiE1z4/G38DoT3ks/Ab8WRaZYNKeKIkNPF0jQ3H5lFXOIouWdQzL6SuoffHXV7gjkhZkl9kAvaWDSLkQuk26Zef4bwhWuQku0ztYm4DR8NQ39Rsa0pXdcexzPSN5Foiys0GySrTqbX0PyeYS2hSxFKpxPCJXhvlKc5KK/+B6Bov3i2EvDO4bheYTMlMZxFsRymA8Zc/IBgiegk5VbuN18AUn17nZ3i8Pws77d2YiIYZfEwcp+Je5DedRld4mV+baUVvRJZcjEGLRaaLJxZp7/532kdbIp7/WNU/jjASMN9b4sTfKv3tGEuXAil1XNiYCsaKgGdHyy3WBpwwDUM2jdIgZ5OiDOuqHqZDY4ih7+UH6jp8FykIeIIJSNRMz7fP/cFGG1LKDkRqC0bzWMr+ylpqGfCt6daxswq8jzpZvqLTtZbgG2MMRyQmtq6Gjdp/SgmDL+z/myzep0eOejBPJLcB+YttwGN/4ey2DGAv6KLRcoFN1F0q8hVu7dAkPsbXuobbPf9HSArwa41dJeQSyjxGAi0CRqM88IQiLovfDfDp3rlNABDeBepUaFG3SEPmYdWLLJT70lxJeIQ6p9hVw9DTTlUzcgx5k4Bivadz2JTS1DFXlw6BpvWgskcmrWjghQu8Yyqn4Wg9G5+wxctPeAVFRoPHfxhKrfF450sOj6+9aqpvgiGzBk/28E8SWHIVvfbgPX/S09h41woI2kIWYESO2ikjG6vKIJxPRxwuY2R+Xl2np4h+FtmSkiEJ215Y1NLE/0J23U7i3ep+jY12ZZsL51Axgd9pFEYqiqI5A3hnMqRUWR+ODLAmaNTT3aOnvcSR5UcYUh2BPaY7DHeRf7kWdI0S8vIL0My6lpmSc+fmvNkHB+cW6r/Wy7EiJVyLvW6rNl2sZCjEmwo1RcrgNAozn3RAhzNLXkfvIEvK6dX71WKLhNSZPH/UuyIcXqTUM2tInp0+oDwkDrfTQBLmRhNmBkAscQHkP00X5VMkZtMUih9N9+Sneg3pwv6DNCgtK7mcNCbUXudSW/PqUrBqr1/+rcXC2P2fX1H72IF22SEGYzk8B65zthXn6HNnSsXfSlODPozSr1sh1P2i9LE24C/1XNgXJXO8sCPISSn6aEiamJht6lTVvHZCqSpdiHG4YszU7zBIxRCAfPXNdw2NgjxLzrpduGIA8HjGGBmsne2kGP2WM7FxwlaF+3V2IlFQVog2OTgvX1tq+mlaSDWROn08k4JOp1omRh9exUd+QFXoDtZAnIvmmq2OxwFCwfin4eqtA8jXr61YWkAkgdFrlx9+I+cxAKwVGL50GVdmCqoZUMCACwbMNSVzcBBFi63Z6Oq6enT0yl2Zd1f6Yll/rQvp1lPnPPdd/tb1Fh6QAqxVVgGdjglcmT3XfoC/PmjigaXUwPRUoEUoej5/RIMXHIdayJjWCsD7Fec0sbx43uepqQEYfxqAMupZv6OYHJzQCjKw7RUiAuwnMdJw4TiYfdroXIDtE/D/kkx/LLtTz0GwsnkTScyZdftL6HVnao2WDw+OqZcvbIxqgwTfCSZG3oEb0wPq/l8/eQGOnCCDhM0wm8n+6Ux/80LWAAL2QCPeFWQob5M+oEq8aLHyXhQMmbq0l5tj8hYcmQOOpQIwhlhVgygNYxNlIRoFPZIWWtzascyNPtF5ktYtQCuCxxaFV9ML6nGaFwE8zpeFgeimf87h/EhVFGpKI3rHl8KHZRCSs7zyUTFMzU92psBsti/HSckSXWRdlirq3Ir8GprJfiirJ3hBVjMeJiJdZao3o2pG2vx804PxLyHTh63KDcyZ65BG89CabWvzs2GleNEAYuj+H3ziRpq0NhtQ8Th2XORB6DLwhcNO/mVoDOYr4PFNVOwo5hzItlzY8UHhyVEAxvmo90USRR+hU0PaJ7ZWK3hsYjl8xPSF71y1l39mdziCYNwIwyK3HN4XjZqGSFkKlY3igG7BLNpwowSDnrXN3VLWpMwptMnqmzRfadVIuf97mqZtFesFsbFDEoKko3jsQ3CrDGEneIqKzmvIOcSQmv3tIgztge/6Z2a6y6UCH3Bf2mRYXUqrrklA81xk6d0gGCNRsOYpzvChl/htjTMImYwNvNTXqsG8shB1nz+4AsbR/jcoRfZRoBxsNJW1UtzMKd6ioSuzVgPJoeol24kxBEzZC1ggArZJfVYNQOTSG6YG1l9wQapVp/pC/iEdcGOZxtElIsNVZKuocgCavTZkqpJ8g406DXbcg5TPNKK804FvlnODyaE6HJn8fiJNCHSeDCpvJeGR5wSmNfsWCMWFnBYFivATkQ+SEMYWGkqZpXKdP3FgVJxWJtq5F6FFIq5ePDXa0OiUdCZoBRf8i+eMlBkG3+l5hMnSwONkJi7LBCl0AD+7MQcaP4epbSwIRRQgEtxnFLsS6lnuLSCfm30ZRg8fhNSo4LmQGOCLCa82Y9+tDE9eM4IIv3TpP4i5tOZV5VZoNGPB4G3wcsioA0Pfby2gv2+65y75o+A7o81Jk5Th/ZFVOJO4p1n5dbxveOecVDMyXHOm7+/YGeowKZGG6BF2fdUkPkt1dTJDE3k+Af1FKlj5L/NLmFjkIgUGhldq/TkuFM/YwCOQlkNi2XsicTvIwWJG1rAVDeyO4JzqKOZsA8N8/wvdAJXinrl7sIEuk2tXaVPYAHrM7g/+R9NoB7U7941wkMLYTHv8dz0o30bGuJR/yDSQuBIEoTjcjKdKXyGs0TSe6EfVwEeI5Nh01jt6q0FmwT6vleq1Zv0zdndswZMb4Ukj0zrKiU4vqM0y/TVakG6sar5ord5Y2IWtljbZdb5wcvuNcL42pN8pkjmCY8DQLW7vX+EpF3ydPlua2llM5jfN1j3e58aGYa81Cn942g29FiuvVXxjg7pMy2vxFiemfPHKurfob6UoLADVLNb6UHNmdqx82BbAy0FuiXlCOyDAYQkOo3xFrfGF9sr46Brs9+5LsuzoLb2/k6+a0J5iJ9KTIBCjeDqC2Ske4OTv2IDmxAfvxD7QjCGIh2RWDbf3upHA4po8S/V1c8GuKd9UwDsTgS1GP6je/61Yit+SLaFvDXS9nuoAYydS+kXbwaXoFbzmNuO5ICA2Xtuk54YZcrDwOO041YmeqxS8NYsez4zbyWZ8STWS9u9jrgshZ5ylhRuUcgMy/37bfeLzvOy0SWPgPAKKRnM03lfn9DcI8kwCQ/qFEmft+jlQSyyEB3g+YnI/0d5/oRyyBNUJgFVA5aTEbh2Kj8vy0vPxsh7lcARHzGz48u7SgaDm70G1Auvgw3N9DrjF082UGA6d9WhxE4Nl1/rLQn8EFTu/gJic8fvZGbD1EE8bDfahmH6sMvAaiisvzUHOGCPGSIIgLTbzbWxt5BHgR7JyhoRN6LX/XzBIiTkJJilLLtNsrGr4emtbc3mY9Bpw/XE+u3kN7Zr6J9XbUqB53QlygH3ydjuQDwNp4lKu/n1exn+t0Jua7Fxr0AyLjwxpnMRcUlUD4xMp/PvIcTEB5GFVGDF485A4xGk5WuPI0jzc/YxbLFMjHHgbIRBCZDBvyxu9GC4/oPxHAAq6F3Ao0AXFBd8H45dFvCs9UCKjJPA87rIoHh4CTcGruFp7oeF8dT4TkfIDWQrZN098Xnva7WawtBQ40+nx3Lc1hB8EAl7/cl/4Jnf3UCzRt3QmcR52jdioLHpNO+5GmPPHzNmMikpd0kEJGXSGAsV6VIi8bwcIVvnp9bZBu3nl1YbKruxdIlMot8S5btHn49Eww2MuCJv8OlaGmIsuynCQ83B2wjI4WOAC67iB69E/Qhwa6NlGgfEQmAkyD5imL4ESQ9g0vzGTa6evrh615RRARduF5Gum1nb2NzFUoO7pODiKZvcVKKijmn6Jyyq2/LjwozdO7P0P4ob93Wk0+pBiTigiCV+iWboqtxBKJpkt9fgb/itmz8c6DqDly48nSt/5WLIiBq6oqkqEgmH4nt4YmVVXyWvpOwAuBOwX0ajXXXueiQTqfNul6ieg3XhE7eLM267NQRC794tMqM/wCHJkBIGvToV+EwaE7xotJ/X7drSKO5Ex3CLBdTYd7reZlB6AV/nbedPPme8CzCYiPkX0RiHY1OGCSXT5rXV2Vd4MkS7loFL6+rP/spB7mugHKCzhGb3THBpRkpzcdLPJ5AF9vnH0qopub0/HqH22HJ94iaVuzGJFEUv1AiiHt21f4AuKrnKfAZ5RMbqGfy8yfDl6R2gaqpbKdxaaq8Qgzq8gwh0WFPIt6C0ZOiCu+j5MiJ70d+d76GEzzbzQ4JBJ25N9geQKpbrsXiFiZt/T8qImCt/dDL9/l9zSPDaYaAm1w7aKEDxbTfcwZ457+UELYTw1sHeA3w6f5nNY="}]}]}, {"codeGareDepart": "610", "ordre": 8, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T18:35:00+01:00", "dateTimeArrivee": "2025-05-15T19:51:00+01:00", "durationTrajet": "01:16:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 625, "numeroCommercial": "816", "hasPlacement": false, "dateHeureDepart": "2025-05-15T18:35:00+01:00", "dateHeureArrivee": "2025-05-15T19:51:00+01:00", "codeGareDepart": "610", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:16:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 55, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 55, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "EL JADIDA", "to": "CASA OASIS", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}