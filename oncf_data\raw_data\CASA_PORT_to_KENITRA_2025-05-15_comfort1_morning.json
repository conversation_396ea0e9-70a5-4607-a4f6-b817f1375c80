{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "206", "ordre": 1, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T06:20:00+01:00", "dateTimeArrivee": "2025-05-15T08:01:00+01:00", "durationTrajet": "01:41:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1, "numeroCommercial": "1", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:20:00+01:00", "dateHeureArrivee": "2025-05-15T08:01:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:41:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 2, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T07:10:00+01:00", "dateTimeArrivee": "2025-05-15T08:49:00+01:00", "durationTrajet": "01:39:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 703, "numeroCommercial": "3", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:10:00+01:00", "dateHeureArrivee": "2025-05-15T08:49:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:39:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gs4ux/RA0aCZt2L+SiC3yNIPU2acN/+lgREyxNszWYnjnNjMjhQIezs76nMxAK5OgdTMPn1+IUD6UI89UsR7E/JmWXosMop8J3mXV6Vto4Q9DGGwuv7el+027umfKzYjrzMUaZieNLEmqWiYfs2Um+8mU2To4IDPKfcRYLMFnCevsQ8yHj3By5uZgM8k3JsYsM+z9nwgcXj9PVToWQ6w0/QvCWBUhgWuMufqm0sfLdtcvBav/B4eXptlqNd5VdULH2TYuhHdoPEFPz6x9OHulVOhJgYQj9s1iiVdVErh2aH3i5wuJ8U5o8FXdu8DyK5Os9bd3CULojANyf/AmLBKMEbPuvvc1kYUcgxcLYyRST3JricRW8fqUCQfz5URu/w5MX0OI/A3gvhvl/T6NN1tHfM+uahKzXKtrwspm5BXPOeM9r9Yz2yVUCZe9wSiVHSGveajZ5gGdbwzod7+f8a/rIqco3NbLEmOPRJ2hDgvsO324OiaKoLEN8qo/wf05IYw5xQ7BRX2xWhvv10sP06Q/oQyDV7MQLZjZcFTS0F1uKpXjJwcZmD3WNsfuWA6tClI+dZRuSDM3ShaT2n4aPaNR85UdhxNqOh0x2CA7Y+qhShoA4mqff0aBdTUaRn3fPFWXjfeWduJXL/o2Iow35smHlVE3tVIsZ3/yhTVKYksaOpsPXZkLqhr9FBIIkUWe9hbNYeXZLfahZ/1Xkkuhxx4i4WvUbekLC5TUZILZlwVkmCaKWyqcayATO+g4CUDysJcwAXSe4j/OqCcLNhKFgBUIbiMM1bz/oyMqQY5H/6Xm0h+psHkwVb4gvvvTScjCjwLxNjoiwRWP35GHC55FniV2EmdjyzeUqy/xymLqPUSrBrex6hrNs21127hg6fG0oevQMlvD0P24UFNMNFtJd7mmlI/DGZibzjbjEtZ0iYkkRyTM6EWBiLBkMW34E6ZvDUZOUjVe/xs0I/hP5SCqJ3AH21r7b/C/VmCeenpdO8nn9IdUW6TltPZfvF/12DC8RmNnjN2s1fv1SnoJq7VBY82ms2uoMJ1tMV67g0Dd6UtvHb8T+WkNq9j6+JUog6o/4SnCUzU/dKA3pT7wr9BQI2OOUz+zFfOZFQ1NfAuscCHcEjCiBWxR9xXtsZ5DSsfs+H54/n4CLbY1dha5woxS9FbGrmkm1LmwEbfcoT044qZkqeu/uP0WGjgBN12Zu5YkAXdaUW/UqL1F09qQSYrC38UXwtGDMApkZPmNSIlM/KXQ4YY0JxFlrXjEH2spJsJqRUL+X0XnzglKb9JiUybnw3hMA9zOXoJMDkJqJnhuU2SwolPc6ex/z6kWrEazNug3hS9djdjDwczTV+ZkNDsmGQYRCMki+tDxVlKDfgxrOUMnXJS7ooGTGDbeN7QOIyeurNaNZ4/unurBMCxGo6LKf3eYJ5gQramV/HCHrofLUeRglzJlomf72v2pN0uXkNyUq0i9BmPOtJu8FQnCp7EB4aOV0Zme4AzAKulFV0y/sJFPqpkoEURHjLGoPNGJSlHCFKbVcQJj22FcYsnpOdQZrTeBzE/kQ8aYVl74BtYrBaB6xld0DKEP5u51srYbiRC45C6R7XoVQbbW2s1o1TdrVhUfL51bJr5PvXYWsNoFNP+d8uVQhgz2wbwjXDWaV0cGqLo21+MZ2UKyUk7cj2kB/1hQVdujNuhCQIFVg5XNEyVWzT+nqlvV1uBQyXuZwoU1n+7NTPLD/uz3qUtcr7FDr1Qkj5iCc36V7V3IU8ietBn3a325zMhgBxXXQxyjTFKVOySiVrSA9MG5CtJeqcJ13YKlFhGyt8mKNMdHZn3YUdDOeCQhhXiQ0NHq71lOFIDEfDK0oMBtmT85PvWqlAinvnyAYqpa8gJ9gmYh8e+kXQxxx5uBG/YFMPB4AjxfKs92TMZ3sWAqGdu/3I0nqk7VHMbe0z910Khc6NQ5zLgtggW2Cm500q6XftE9xUNid/4WmFymLlxjAlzfo/qk26H0AuHVTxkrWBCh8lvKQ6Co88EOHJVXDTqjYcVqKdfnHcqxEnVahRB3m4iQa0Rd1HeeLrV2Sllsj4Hk17LX28Ot3OIzQ6/L3s/H1Zu23cAdhNPbJ4Cun5iSnUWrz7lwpwlEP5ticmYM2YcQLbOh2dBDZP5SxmiEP6P5zqFPjjDwtOO+O1B52ggyKdR635bEBYmPoIcqTPYBXD65+2RUccsYUKyeJW++K5HIMXZaK+Z90zqssljZQzO9H//axmQvJ9Df9jZa3E5wFXrXS94adw6DImcwftHxkDldyN0zcx0rMyRKYXMI9T3XkaEhSeiu1lObeXP2db0x290xHxXtP1UqpRQJI2myiCHfw5f/OVGq72tCQlMAODD3XpYM7jzdZvIlfw26AjgESjZOtOK0Rg/FmdEPCts6kdxJlSPFHwbgWcdQty4JK/wF3rPHxisFSvGZ9WVjZVMIAMYFoOsUrU/tFukLir8O83byChHTJD2od65zXALxWyf80e3N06MBJREHgj/iYC/X7HdTYGJqmNjRAQbwbmNr8WH2LUiZppwOWQaYHdEWCh3H+70fFO5behC5UPPPs4im6av4VWoOxbSmwgfqXwg1EZqUl2/TA4WjLrpVfSoHNn+hbgmnMqRn+EdbZX0FJcgdJvtWO9mKGqrxJ5+wYlGu5edgRd+DGnr8qEbqAIwXhGo3UtvMxeZHAUK319fLgDwKS9SkDfChTyZm6bPB1rMyRYxJX5yWqkIpb5KpGhSs+DO0XyGK3JdEpRd4ySHteJvZsMIZ0RItc3qOsQcXh30gnKNM71q7O6VAFdm/U1FRu2EC7sexOXBmb6wS6xwSIUetEpShhHQPFQdMcEX2pRNZ/tu2uQ/RsIBmZRO+xJ9Ebx7I57wYzAM/tJgaHvv5vMqi03Zij6xNaP+UHupq6vQpVkIyzYHZzsS7DPsk8Aoc62WEflKiaRpSNhWdVUbO/r4fmUMctHCB3Jq8Se6Q85z4aR/CzpZImyRJJ086AWnAixuWETXTSwcxKUdAJwrA0xqF6oaam1FSHW2ez+b3W5h+q9unZYhniC0J88mlGxylh63Ux8dsxj8UI6tc7WDaOeFMD3NWTboXjE5vUH7mWQOqAXZ6BmEQJ8oB5nTDj+bBqx7p73vuErDVIQLoWeVeJzGMhN+OwVgJPj/k9MfA1fKEvB+T+gn2uSLSpNNnmJfPd1hmJMhJq1vqC356bz6UWVG3URLedZSKUZvF1fndbBKP/omNZr//qqwPBSSIKOrC78Mx7ZqxdUCPAgZ3yBVOXx6CyFiFoybptshmjyTaTTroiEVSzK0o2lNpItEguXXcBanq7c/3FZvu3CYPutY0W8itHGsbn+tHY9ytN0dgASK3/NDQBC6Ec5YeyTOBDJSdJdbOA2Mffc7zRnZqEk/IHDoIhGuXo4ZLguC9/dggqxIAdT7fjmg+utp4Sy7/LWaZopbmiNwWsP/gYFli1wZ10OJspOAB2sNRDtl9r6VKZWn7v0e7LpuniXJBtSmLC9XR1H/b2VLzg/rUKB6I9tiBSA+P5xLypeWVEtUuikmudCeL3ejw9IJNThUmQf4TiOrJLfHi4Oz6py8goeutcyBQ/GBt84pdXgEt2UvoCbULszdTMnx5EwvZsnRaNcHaU3zCc5GgMXXomBHoZBbi110W4/20tSzSrkRomRaX0BI0If8wEFcoOpNHaXFmQkmRoGk8TZvvBbwodRHjuHrULBaTEwWN5BLXTGsBHZRhVt5bYT4zJDUbROGYw0HAYa40t+UahJuBl4XWUKzgjDprOHnT0hvwXVWTZJAK9KPkWjz19nG5cfbnpM3bzj91H79kPxPz2bl2XlUzA62Yr4dqxs7hwXdoixB1fDKhOBXb+PAGrECMTrf8+cM/TQdD4HKU8Ac/Hx4+tPshUgqZ2mb7FSpyVHydyH0sJrBSdY28v7I95IKM6Y67P7zfI+2HVKqqL/0DxNNM7VKqRchcXZwEbdnl8BgpibNtGYuhJKU2r5gio2W7jBlWcdnmTH89xbWJimyPryNcERIBsRK5R1qTCwIA/LDdYs+/C4CZvBCgcJHCiuU7xjOobsfw+r0f0l9WUWJEu3gxj19LzU32/tc00UU8DX1sFFcGkQTkA6Hn8mj9aLAjfCfO3hVQqGepQ+g8vlpSYV7+c3CHfRG9rQARAhht1r//qRlYcPKtFotyl9MKT3cmvBcPkiN87IrpfY0bWrPsgMlJrjNRFbcvylTKev/U/K4QRoMa6DW6SbrWV6HmYqO4RIGSGR2uUH+DUgb5yx/nkzERNHk3SA8kLGXd+Rsc6WHLIaS4+SpBbXbCnWnQg2mreFB7X3Pm5cIqM5VfmeqvWoVynzSVKhALMllIe6VShxAj8M/RMyinljFwNosLyyFEhusfAbwxDz9Y9DAFKujAR/fZFoxkIF4b0I8o42XxewzPXsfXaK8un8MY2sgOnoqlQ/BcYuT7HfhntXzqYaxmXcTEhVLVNFURXD6CXCfGCmD5FUHhrxoqTjMMOeFDeoyDqyQxBS3x/f0o/foJiH8tDZ+GMM1eQvJ7vlEbl/DVz1q2PmJkEuOE2CxFlyX/3PFrGh4PegxkaSnf+IHVETkQI222WKRKnwwQIvLzOcyp0MGXvQShikcl8JiS/HsV08rYd6GXMj3y4TsDCruGv9D1o2YqCUlGLxWklxupa9FE/y22NvnhnC4MGDcrA37k8/WHVFwuLxREH+cy2PxTcNd0WU7wzgpzTEBYqgUibPTUNK7t2k1Qf1XKmoZkV2k2Xq4O8CfM2t/JuTK5ztBTBnTC7MfhGoYtrQPCOEziYso761MTwRr+T8+jx7CRy4sA1vL3iqQ8c16HYgoBIAe1DEJP9UpQ0VUT78/vh9ZVQ5RabEUo7jgFFD1UVh7pD5cPOGbX3LONY6xLU8CGja1xTIM9/NKLY4wAELIVzrhU2Myg2fVyMmEyQbN3xImfo+yUHf/Xt49+XpCsP9fTVMQUWLIg7q7dCGTfPwHHPrGru2yRCS9gB1RUw+3sKcd/iT4Sjum+a65Q6/5dcM3OMo8mBRp0SJsn0FBnu3TOH/a6iI7aDfBFiHigbwie+K8ANJ3tIQDD1QuWYW7QdAe+mczXVlCvMu9T5nEtDZPHu/v01p7gCrvMEC9jyJSRFT9VtoUqkUQEYNtDGPfdYI9QJXi/yRWn8yz3Du/TnYS7OHk2HRdJkGQZ9fck4rI6L0sbX3j6gsrrEyWR3dhCT3zuI7s0SAIZj5EQq4ONV58snXPHYc26DZC+jAgB8ZUnlXuhmBjJV5M+Bs/4KP8WrLZNELwBrGouzHmqlAcj9i2YzIof+p0KOH+/q91jXJ6GiMoo5HnYiQfbuTd72bgIBni9Cz9viv+TzqhE5bN7J79nUfVFIC2Q6JNtxnQ7qoDDMV285YBKgPT6qKF1zKJAIFTReWtrR1HZyWkDZWczB2LA2b64eB1BsY/UdXJrgoMf/Gk6ttm2dh3JZynY9riTzix/n069zJr0qXFUxn+wFCRSCL8+XnL1XemhJzulY5oKBRDeAb9zzUB9nR0+Rd0eilhVNkunkUSuoizCPMZ2suxVmiwl1r2TjA6n36qONl8y6PC3ElnBf+KvuIhEeqo/gblTaDolj3bGe9LfxNqxag34GgUzgluQFSDjNy49zMQ81x8hWC5JQF/3AQuQDbR9vAhiwdJPdis6lLN6Z5pp4m1phyuVsL2QVhtONNeUrtjhGgEHJ7aQsgc3Pc9xKao8cJ5XJYcNH0jUA/uaCEOIQfh2JieHAYNMIm5+OZzZtSwwt34pPHjyVW9O3Af8ivtxUghnfRyljD1g/BGQsyyJKwgzN5Kh5JxvuXxcJQDu5AjBN22aua/UClYaaVCk87ft1IMJiHrJUoHNgl4rsmLNdK6SFDTw9pBTrgZpwVqejDyzt1YRDM+ybjJqHefGIBW9+tjdcla6z0HKPn6nwYtQwxgyxGnSaeYRROzOASqfcRy0dRpsdiuAs2Ju6j0Ut56LZ2DcYrApJz1/N9h5LFldp/EkZg0vqrGdv5LtALWznRRnw8Yq+viswP1ARKNsLQ2uHzxvGLH3D2eSSb29MPwaksdv+AC9Pj6SGikEgLlle1qB5YqZA+mHjD3FWBDssIiUsaMw/HrTNMWHL3Vrd+/DulJ3Ri9dvOgtv6GdfnCoIW8+YP62M7sSN9TquQfz12l5hBixo0UuNOMYB+kjy/197SIcedOVDOqYcbzWoYs3RGdcpw+gEoJasxS6YlwyN9LuwoLITSxNw2FHZFZNcDwqqa6lN6hwM4+3rkec8IVodUqgGRpwfIrawGvWP4ak+NQ/T4yJrmduXXqtdStY4qMa7fvo0kqG0oXnXc/+QMf8ZZyMzj8g+M2DYtDLcw73Qlp7kmCP797oEFJZJmOT+S4kvP75KAWs+n/kbF9ob4yIEFLhbl1LW2VmEqPve30yf0Gy+yih7xaBZmgTA5WH+XocRDCO8qeKiZnoLb/lGUA//rhcWZovHW2eA8W0EJbJqa6/OL6HXNC0ErEG04hLEiZ2kG3ifQfonQJGII/DaeU4BhVOkkQy9572w1Kx8DKyYP86OO1gwl6X7fcr6krbcTzIZiV+xMudIsDBO62ywZUrbUFm2Zm04+wBNWY+RH7E4ErG4rzrdYPPouEXRgV/5eXpRYtBDMiHokARSlE5qYREMZBriD+e4awG2VRNIBCNSxkRDxX/icZfcbhrEuetoIsF7EYg5VY94i9bvlvI4KxnNowWny8XtCLzUWwXOCRXpNAFd5OLq95PzjCODNqUWoWhf303Iu428MNbIzjzfc82keRhDakDizH8gOr/uFAFVYW0xSv5mhUACyC269Lwd672CLVgB4MgQWomE/OLSWmVE6fZNDhCyARR9X2MX6IIENxstBGxyGnrXb4ibBa3yzEwD0e/hEeYGbajbPy86JRqFhZ8jfYKMDdAnSQN6ilSs5XZxJwFt9fxo626LgD6X45rXzXTMnUQJAEdtR7cWp+uHb2XHWYnHse37obK9VzqZtEy6I4kv5qZLrvVBGzUTBkS4eHAjVJhUL8v4toDjMqA62xJRVUskyZgPqJ79pZpT4e+CESgAC/HCS4Q+MWC7ZVifa1kvpeYb3HTPGXR0nAP3OjEleAZ4OCoLEW1FgP7dQFv7VZIoELgMg3/Jmys9pxx3WS4e9MFplmB+EWw3b0o1doeCQ5LOK/4jizvxgTC7ceEfHJQwalU0Zc4p2SgwXll9BUr69PlauE2t27GRsj814UnisRysPsKg1gnCbx8fE89MEaiQdkzguFKbUHiCQ/yTTsqzeX3yJh0bE+ZECq6g4ghKveN63IIX7Ey5A1hCwIiQFCDwCY3dkl4/wgYYISyNO8KXBnnQeQ35sjtKodivkgb3gcjDtFr9tuplU9hjfhAnS6R1T7o0zZ25SNd8DBjDeCApiDJuZeYTcd5z7s7ds5zDUvu3jZTxSJqC93HimZr9WOJ1apMNdSVm6/UNlH6rapUI8uEAmOg1WN+7cFBEtGe8JBcgl88CHWGD77d7Ca5Bnlyc4K2e5CE8HL1+DFnSy4MZobdKg82LbIAWVeWCKhwyS8jxeMcgNOE4wUmGjJ2jDaZCb8+KuOCzJTOgRveKnC4RrihytDatxpA6uXgiVY8I9Z7ko/Ch2jtFZTdfbFxvXx1HRcynGye0O3QMNfuW8GiDEMJNYdLDrU8Q6r1GVi563S5pF14Regp7s0xKeFQ9htkV1sDn+mbaAK3KNZeuIDMW3MxAG3SSiWbjQcr0pecLbweYrjnXdZGq6QZg4ZzaNOipm5yOwhRGSarxdVWOr55lin423jJBH8bW1aweanhE1Mlos3/S96FV1L7U0VYywgEq+DWCvk1oJS1GwnQ0CHwibXOEIlSWnu31IbKJ6ZwF3ez2uKZaVSyFGaZF+Va+7tkRQp6ktDuMPJ7fwIsvFFwAi9BPM2uVYjhWk8zveK9odvqBbpw43qMs+eZIR/osHr46tg6ZF3mAC4koosNp77h+1WSitZD9cuGYC9IED2NCv1kq/uCudhKtp7HNVEOgsAkhxlINCcjPiKGB3JfngbCwtnpeRpfkL7aD9Xh321HckBiy9k7LKhjH4HwxGzjoFp+HcfSPUbF3W6nCNULIgxtAZeef4OtxwcrDR+otiXo07IaEvhEJ2G+9NYx4yJ4Y7xgvBDPrAu2O6IuAeV6I3jCGdR141oHdYy4Ksa/t1AjsLr56XgyTsPyDlsUhI+mMPbzSOMtGLpbax8VSUnx1Bed8pV7bBpAIZP54vHOi9d8CK4+NeJ956yjqFbRo+M9VHYNEPD+vDPy9gw9VaIsyK5anxt8yPUbpV7DYW04nH8VLyht2FaF1Q8sxDxPQ7X7yPegyE/4t6FYKrn68KFdJH2xBLsodrNdypEkx1tSGJzH5YO5ArlxO3ldvdY3/GqJGkW+r/yoWi24oTdgnbpJ5ArlsrpQAIaD68bOOzQMb1iLfShD3V1tdFf4p0X2yQWOSZduDS8iXMoUN/iu0oiHtS+J3ny8W55uGNtKht2a0Mn3JLLtjvTxCO+TSfqafbmY/3cR1a/9of6iVIWv+eO6EVUWWYIpe5+3zjQ9fB6mMjZqBm/cB+aLFq1K7xiAa2LZgzKC4CZY8RnL3+L3QcrCI6HrVm8Gx8YRgAvhO0BaaV6GOrX74G2ubifAYELDH9Y455gOjtROFk+nrZUeDZLAkXUsh1dWzKn/R0QLcdFD3PRu1ChH5XYqU7kgqab2U7mbRDEUls4sfe5aGeDQLSonluOJU8VSaPLEAJ8OwXjje7aS0bxYtZFk8hmsTIYkNFwIKmpPTKrhW9jRnTc6JwLPB564zAsyFGLGdDwLV0wlrrXQuvS7d4CFW5D5/RuRob+X69sHKeEibaxrelo79ASixvvHBUGs/2hwHLEBro9HBxt/UPnYTGRKCWqOLA2tEVgeR65Jp1gjcS5AdAYquYfgtic+U8jk82GYSRQMKnojrpHzLNLNUk9umWy5lnFaJS8E6h9iF8zl/S5+wu/r7uBw8nCNmRsvcWjpLBPB35AsEvf5EQLACJQY/dXwMYKlLiXu0w+J9O/gGRNzaIfZ/FFs4ZEXzMph7HsF4xf7ylztBQ/DzrrxTNJ8XWq68AjDrWpWxIidEN/iAq9DNJVlhOKvnZdxvHmCliBqidiRc5gCUF3EzfZkcvUa5r42YuESVZ9g/pfVWsQxBKzqp1IAmrwpRiJ86CeE6PZC0OKi9BcUCKGwKfqnXUg6uYH0F/Lfu3XEri7XVg5ao3ysUvk+Egwkb73mfbN/G/4iWB9QfN7a/WcT0S/cMbZgF1Zu0uojjBlMDG6odUVh7+CDmbiFnCfW9wgZdYYDeka861URemxnS9+fPXhobmUCRJs8tjhx44T2pxYzZ/Hkc2jOCXlX0lab+SZOsC9ebRZz59Oxq/fBKHY+GC++H/FWSUKOrAOQIPV/xss3C3j7h1Mm+76pq1CZf4+OND//PbSjZc/8Lj3+5qYdF2zD5F7+7wetxmaQqGSqWDBZ7khhGiskUq0GDokF3oNSPDlRcVM+ThK5MJIpsfhXjOygzu848VRCBMuntvzBZh2dJBKJofGcVmL5cIosNjI7c6WrX3J80zd2rHxLGojGQz027hLU0d+QLX0FcOjGXkhI04UFB5CHKy0XYYFlNjFpTAIEQpyZN8cfVRIYFlgQXFuo06W74PRC6bx4ggZ2ZwQUKJHIqSrOC/bxiBQ83mCVC2BXHxTvjU4xASBim1/lsjmu7mg6A5f0kBCQJ6bCjqSvqktjy2AgZc2ygvc65dBvSppB6cHB7wDW27V81sZNVm7tEGmqa2aR62mVyjcS6vl/sqq4OC6B/WwbYhqrFF8OiFU/Ty7uwTeXQTRNNEIMz/r4si4v/RLK/pQweZydItiSBa8BrTxaroyQOWbQGrTvZSQD/OyQkFZk0D2czOHH9yTmYGhCQ0ugQg7d9LQax2MmZE9otiaEwvv4xhL6J8PmVAGFZmZ6DBpS3YeQjSN61Yv4MT0Kx7VM="}]}]}, {"codeGareDepart": "206", "ordre": 3, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T07:35:00+01:00", "dateTimeArrivee": "2025-05-15T09:09:00+01:00", "durationTrajet": "01:34:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 650, "numeroCommercial": "5", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:35:00+01:00", "dateHeureArrivee": "2025-05-15T09:09:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:34:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 4, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T08:10:00+01:00", "dateTimeArrivee": "2025-05-15T09:51:00+01:00", "durationTrajet": "01:41:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 693, "numeroCommercial": "7", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:10:00+01:00", "dateHeureArrivee": "2025-05-15T09:51:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:41:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 5, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T08:35:00+01:00", "dateTimeArrivee": "2025-05-15T10:07:00+01:00", "durationTrajet": "01:32:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 644, "numeroCommercial": "9", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:35:00+01:00", "dateHeureArrivee": "2025-05-15T10:07:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:32:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 6, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T09:10:00+01:00", "dateTimeArrivee": "2025-05-15T10:51:00+01:00", "durationTrajet": "01:41:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 667, "numeroCommercial": "11", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:10:00+01:00", "dateHeureArrivee": "2025-05-15T10:51:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:41:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 7, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T10:10:00+01:00", "dateTimeArrivee": "2025-05-15T11:51:00+01:00", "durationTrajet": "01:41:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 636, "numeroCommercial": "15", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:10:00+01:00", "dateHeureArrivee": "2025-05-15T11:51:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:41:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 8, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T11:10:00+01:00", "dateTimeArrivee": "2025-05-15T12:48:00+01:00", "durationTrajet": "01:38:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 639, "numeroCommercial": "19", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:10:00+01:00", "dateHeureArrivee": "2025-05-15T12:48:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:38:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 9, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T12:10:00+01:00", "dateTimeArrivee": "2025-05-15T13:51:00+01:00", "durationTrajet": "01:41:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 680, "numeroCommercial": "23", "hasPlacement": false, "dateHeureDepart": "2025-05-15T12:10:00+01:00", "dateHeureArrivee": "2025-05-15T13:51:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:41:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 10, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T13:10:00+01:00", "dateTimeArrivee": "2025-05-15T14:48:00+01:00", "durationTrajet": "01:38:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 700, "numeroCommercial": "27", "hasPlacement": false, "dateHeureDepart": "2025-05-15T13:10:00+01:00", "dateHeureArrivee": "2025-05-15T14:48:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:38:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 11, "codeGareArrivee": "250", "dateTimeDepart": "2025-05-15T14:10:00+01:00", "dateTimeArrivee": "2025-05-15T15:51:00+01:00", "durationTrajet": "01:41:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 647, "numeroCommercial": "31", "hasPlacement": false, "dateHeureDepart": "2025-05-15T14:10:00+01:00", "dateHeureArrivee": "2025-05-15T15:51:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "250", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:41:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 100, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 100, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "CASA PORT", "to": "KENITRA", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}