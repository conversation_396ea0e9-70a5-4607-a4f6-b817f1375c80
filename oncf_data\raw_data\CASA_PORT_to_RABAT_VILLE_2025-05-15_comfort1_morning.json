{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "206", "ordre": 1, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T06:20:00+01:00", "dateTimeArrivee": "2025-05-15T07:25:00+01:00", "durationTrajet": "01:05:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1, "numeroCommercial": "1", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:20:00+01:00", "dateHeureArrivee": "2025-05-15T07:25:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:05:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 2, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T07:10:00+01:00", "dateTimeArrivee": "2025-05-15T08:15:00+01:00", "durationTrajet": "01:05:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 703, "numeroCommercial": "3", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:10:00+01:00", "dateHeureArrivee": "2025-05-15T08:15:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:05:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsKwmfa6tJ2w8Zpi/SOr8NxLIVSM8wdjbRs+38BalJFA6ZoiOdkuN1IY3MMRQnPz1kWgRrf2a/K3PtmrZ37LsvsLVSXMEn7og+Xe1ZdUXQlGzHytVM4dxaihuz80im+hZki4NSu409/7lOhcQ5gorLNbI6otx5HMwtz88h86CZw9taYWvIeChnuamTEQ1co2TYCHIGMNaXCY0qherOLQOlj5dPAOA7qhdxtYaCgoyLw+R4dM7/pZzZlCUStFwrgbi7nRIieD0ez4oeoEuFAA3yDWtpyqO0uELKD3TyAgojnsnqX6NwAFQU0s3YNgf7RFcvp+UVCtx9ONqeLY4QkR4yLv2hP/zufsi32sgTd3FZXuSbisSPy2awrXS/aePpe+EaCcVHXfI7KNx0d0uGREaNe4+s5e28QtKhUYInq+tHrlxGqelanppQBnlF1wO5E8WdEi9LG2ezHARDyMOoflzYIiAy6N8DXhU2DzQ2mPHtGJO5zXjOzxMtjllaAQPfMZtrd4Z3ii5Ok6WW2Gz7AxJoJnKGPXySMa4/qCytDiDVBRZZb4n/8bXLftdmdz80YvQbgd8XgFUNz/iYs3yjFxjNwtzmyStuBgSMF6hEftDFgmkUK+neqFlOWFuwVQh/CiikGs6GQVVsAnrj4TfYb3sMCzY8ZqEyffSLEaqAIq3VcnIdrIETe0+TEgpDghrwOXwPLYZMHulsswSfgwqIeFRyp7WoVtOg1nekBmUYWTqPVezHe7D4U84tFZe+qHOX1teBUkQqkvFkJMnpPoHhkxeFku9+BROXnEswb9zlfHpLAj/KB0MtdKOOSFvathBb6Ak+nwFl+EysGi6DBKxCyACdkv6/PCCitel7jVzMWoj1z8EQMNQ29FqoID+damirFKggTrRnAhc0OSKVM/C4LbaMZJjtTaEekM3UMwyH0BwHLSxV74LSgh8RObcq3t8fTibFeUzhXYFp/YUo3J/7Jpvs6Y8d4hGurvHwExPcuSA/weJAWlfE6+ZSFX0a9AOeIIeGqU9AvQZR5BlDz4KAlz2PGignYOng28YakbUbLnvYMjRwV5kv5ovrQ5Y8WqO3cLiUkDTCDYvHK8fBf5j61CyIubJyvNBquU5t+pBb092VLqVBvg3shSghUIE3K994/JeehSMM3lQLce0iNVrGDrAfh38Yrn7ZJtIOW+/A8AC4RwnQA9bAm85invX+sZkb+gUU/NK9abnNKlgaaDWU14cgNZw1VnLgxFcGARGd++a9nOciIw8QdbRXU+J9w93tkzdVvGDwWYUtkHGYeteHBMI+n7mHtxWrZUPQbK2Ypim08+Bya9yVLSwOyvFP9hmMumOkfrvlkMrz/HURP1sdfj9FgtXiYq0y5FhGCUIhSygR+FXMlPjON6ydjiLun8oxgKeINk4c5pcCHdYjXtN9ro88CRJOR94dILHpaiSyYKtiqXJ1xElNEK4fVVqIJ5YWGbJu2f2pfPijoD1S+RR9P+SoIe+weWP0rd9+qjWccD1ZSopEyj30/DArTXe4AOt8J9q+wSVHsLgGOL2iVywdahssZalqtWyq3HPqVPojGRJ6KnelnjcqRQ0u1y3zPkQCisY3Sr17MCDjE6qm2ceTOERRHyCjttZbSlMQjv7O2CZI4VS2V1Q1WvAQ1LD5MWOPoGEsaUqmgUBRRR+ByRhvUakbOH6OlwPDrfWNcboJs60NuQH4KZVgeFa1ulsJ0fx/bT7Crj+UmMlFuBfa6iu2m450JqbO6AfwNaiog2tuY2fkDKwE/KkZzVAQKosVfc0HSWkkM0sIIEwMIk3hxsFgVuZoBYIw82eknZfx+Qu4j38sLQCkrxJ175irfkV/qskm7G7l3lGo6n1OmTxx9rRQGo5/wLLB0f7JTJmzN1q+Kw1BxCYlFYwqFz+H8yPAoXdGHiAHTb9jWmF0pR6jHm2rw5m+5IyJgt5AC5JCnUJYK1dK0j9wKXK3bqjv+isKJy3gX6igEoTKnl+odZSyn5Qte9RT5qphnn9/ENBHnYOBJtmWpiVSHwUbvlVM4gHS5F8dkD6uv8nXDBnhy5B5xjvZNwrLgbtVTI4K6Clj1NoTnLnS05L+pmFzutavJ7iLGdwaYFvt3yveI7zLA56LI4y86ylPoEpxL4kLd3VBwdCq3Y6Q5tHIE6qgqUGGJthLVDoO0QJbp0zKnn7pMk+lp2v+t66TNiQ9izG7BuLk1equa0E4ZtnHjNrSxC3Lh3+pCqzQpR51pZ07qsU2Bx7Ome9Nbdo2G1iE75lTxxKVLnPUTNaQyejA7rv7aBT7BZbWquciSXfVM+MVxKPS18SRn5z5hdu8c7w8PkOf7C/lzcs2R18ZK7UNcofzXmuvc/sQWBgK4TqraKi8qL7LdgqDNP0Xv2xT7dh7uP+ibpxVVM97IgRrG4cc8oiO3HC8U/T2QBC+YtRyE+OvAllvyg+uu7cfd6p+Tn8f4rCaczf6f8qdHVjO4CaNReGRg84gnBNtLqaCzFgftWcX+AKSaUQevRVtLZElgV9+4QoLzbDDO7XQNNqdGCifqrEtYqxEPHCfXrPebYeGaCAgOOAZvicUaTpoIeDVIetS1b8NJUaZwUZloG+HU29J07XS2AUMfClFuGNCVHMVsjkRNzVjvopQWIuda/GD/9SQm3DYGjhBaUkZ8uu7c2HDhUbVlk/gkUFBemtSGQZdWRyZs88LGlQhBhp15QULr0VsMbMXTqYQs0mec/EMq0fA9tsM3sylo6XUgy/UaqmcSBQ0VgkR7vtzUkR/ISXDsildhHhLLgYyCJJu0SEEpknXkgL/AN4SQv7rmbNujZnykzwuzCoX/rf7+x60wnQJQcYTCxlbH0dup5b2pHHpYeyiFsfgQ/ww+vc3AJibPy2oAB4IiAv56dw8GcS2YujN9IfMX+Sgr4+WHq7oMExoVQp6tO0GQAHQVYR2iI3bwpTNzw7ELRHNWWUQ6dp5fDCi6wMK3Hqs7Fk4sEr5cMz9c4ptGub9oYaSyU1gbmgFSuXB1/tUo6Z1m0iXhXc2xaPVIvh8WmOy16r5kmrcGqT0S0zLRqTF49MKsdFSUMbo5fqVhaRr7WZMpfcBCRr96t2aEMD84j2T4sl/d5gScWdU6J7Fnc74yvsXPuVCCBS3u5w5qKvj6AAzpzbWrQ+45rm91cPUeD5Vm9QMOh9A60YsWJ7zTHErfcE45l1196dA/j5JBVY6HYRuUy/Vra9u7NhHabYz3x5yB/fup055M6fK/IYZe8CLKlDR/MyGFodUP1gOoukEnHvkqKKggjp9ADK8BL7qSkIjwm6oHue7Zdg7pEDQnBWfqzGkLnLQ0Bswl1P3EQNmpNYjlpJbgVgtq/zZfRoIDYqpi/Nb5sZj0PRoI5EOHLlvjCKindIpbkm5//AWCZm/S7YD0BeCQlG86wShaUjV/YiegcLdi26s9B3bbCtKQ9qHY5zjIvsfgAmYR9Ym95tec7pRO0lo0uUQy7p3YM5fKB74sxQQbuAhwvYbXKCiSSXBhyoS76zq9VMn/SqGBbl+mEWVfDDivtsixR+OuiLkuR88BYANtHFRR85nL+eYQI6fbxUr5METsdFCkkdyknTJVCH99bUUvq05yGUbLFNc3h4GOjcvvdRUwlxk7OfmR6AogIitqL8Nfe364R/2EUkqSwotydypMn3izyhdqROjRMmKhAPcKUJUnGzldECjBbIPc1ATujrUqZFuUxqDewGSkytEBWwToobsOs42YmmyG7bk+cs1NRYKdWHdYATQboUT7Xr9kN7J233avopXpzTH/VyW5en9WppETffonrbej2EmUa18bt3Gudl2gEatR7yq7viVex5byK9NoDE2jy2CCjepG76MLeZiXD0ikPakztgRNxO6iLMk+Q9yttiph5c6IShrRuZYslKXe0ghv99F6RWMSB3qvIY9wCJto/4woDwh8kHAtR9jZ7fbFOz84jW8MOEcQ5ZDTMeOAG+6O0ETOTvR4V9LjUIr24JSSTVdkmXCNmcwHq1yNlcrZ/mVnNJbbi+agYhfzpX01fTaThnp3zT0inYH8u1bM7BsBS+tsX4x//OaT8tqxWwtNwevU5tdxPJrKSbjHmIpG26AhD7kZAfF7ayuIX/Jw9SP5bH+ppaYPd0cJ56eh1DhlrZRnJMQHxQQIfjrHuWH7sXH4LtLEvlOvTS7SdTtCcra2cE/K7L1DDI6rfYhxyi5IYA4kCi5KiFShAzQYpC474ESQPJs7IhlCBbhHeL5tUlPS+q5FnhA396+6PWKj7Ccwilpe8ipiA6aEE67A19WNUGwhY+9pmg6NN3jGmaWjYJVRMbjs86OAzhvRwof7+U2P6mAvhXI+MXFvoquCDaTKnP/3iN0yW6GmnAHZ8Tta9VWnCF0OqT+BLFMSi4P1vzYAx0R46iB3pcc28UwcRheQjQJA1u07CV+MHcLSQLk2XSkuPb5CcY86JsDGK0ttYamRKdvG6JNxB+XZSSaQjxtXDXapge3y1HqklNF/ZDVOsDp84QEGBmofA5bcm104MV4rX31xxvrVeCyB/H3j7wVDRL4xfli/KH1nEsnZOkR81rwc7VDZW/gsaQkB+c+oifBwKAWF+q0qkDz3WEPQfPc1MpUMoPUOTyWNv47C41yqyOKjV6JMZWG2GOoUfbRwZmCfd+G4oLn/x6XX4BPrYLMU9+/vYY4M+cFJo7HUgBMeiWXIj7XIisW+iVbP9XTfejDCqOD4dvD6fzJWUI0bpCqM5A5YP4iZvsB5iNC55uimwK2otr8+dEQDYq8mjE7bBSpem+zF2RrWrHhPifeEAqk5YJjhfxd0sa9w53gMpgPceEQmsM2n1Kj6pVhTJk1TZI7sOLHstEYE9Sw06JzWD0/0wKQ3OZ/jzydYE/9MY4VyY0CWSuTh1xJn1Awa/Q7NevwUjD72+7rxt6AlBLBLR9rEYUOXaTykWng/WmuPPPZ0M/5QRWaxGFXymgZvQCOfz2vnsVQkKcOBNP+4I5mReWnLDYZwapaN2I8D9bNp3NvUXyfj04bXxt7/45NejoXEjsdtXDLOo8gHPtj+vW029tVSCS3Yn08uHyH27JS7E2GmhaKI5cCcPypkXwVJsMPerypl1vDNw7qhaYpHWKjmaVUa++qJoqYiTtarjYcuTaxaqEXjsUyeTtYfeSpVZ4tpGGfwxa0tQpkfGn1+WQx3A53pNR6JqZJK/fc54ue1QEVLqju0qQ2fOk2DsuhTuoxo1rC/QvcWC82Gu61s4p0aZzWvjhlXzazJj7Vd/UI1ywQbtVV1SBP2D2dcyMmTXew05QiMFJe39bfcGoRxmo+HmeS1xMiGLKlfRwQIHbJKOiKi8tKae8Kvfkn6wyMlpFvD/MyJo6HXUgHyqfZ3bIOYWIp7TuYWGXftCXS06o2MzKYUiWfBCFJM8mR1GbeqiBxNrZQF5p3V5Epp6kPNLiu/SwlDhWQnJqpTDdAjmSJXc7IQj/c/ZxhOEKIKcwSwyv/bdmt2jjW2YjxsaV1EXGpiMWRcYWNObpfWrTHhPi219AdT47CYuG7fJNtysARjY6m90vh0+OPUkuwU/o3KjmWt9Vyx4dpmu6LHaZyzhtupmtdkZz5k7uT/4WuHLZWdT4RiWw5STBKO4tH7R9r3BgzoYJPpKYi/kcIuD1789ol8IKukPFU8pEsn/rGcrnGvyuigc6X3kZH15LNVoCz0bPLkOyYKNQbdbWUyBb6m6+Bj5X4ld0E0mKGYNpCHQM8h8KboIZVTa/JTN5vJURAqlbOOI7aZEgSrBECgmRfE0btiwOzVmqV6bsYTCSnHTXAnGdrqEkwgnf/UyZ4q6kKy+TMK4mwQO8YmAoax3Hq+C8BzodnaE5Idfm/qfaIuiRY72ELvEPjUG1mMUNK4aeZb9P+LkP/wL1MsEXD57/hh74L0jmF1uECOMtZjZV9+y1MyRxb6skOJX3wJ8XvXnEL96VjckPcwB2jXMWysjCIh8L0dOJz854J7/nSwQHNuzC0qtqoJIQO8ICjz30s+Z3QpJXaSpIZ2hPBLFFJ6T/o6w887ttxj6Xqamb6Q1CuROhxskM9X/vSU8Pm0DbqPNYORIsd+cwvdzEyJb7ZasFbey72fceb5th12nuQmNhLX/mPFHSzIsYc+PtkyzopJBkDViMjbOoFB8Vkj8IJQ4b4+f5yAuy7s59mr+iuLQKyAPm77QYBDqAJefJtHA9j8PCSsHh+SiV67qqHOeD7ziytV8uwrh5FBgl9fn6wx7I5v3LIlc595QHc5c8pQ5pOJu1tVtHqKDFJCRMWcZMmu5w4fmiEjX++o1CtepGkN1h7CVxiMyKLW5VgfMziQgFWS/Mj8sw9qAQjnCWFtflCEJPweTyIpUN9bO09AzGuC1Rs0fx5ExBCr3ih8Quw5gj4knvtiU9St3fwgqZtLJ2eGJhRMu3M4PrxIaYYvLS3reRdubTTNpAMSpoLK6H+LSulZ9vZlmHblrBKRlzSWT205HLKlFshif/X9JLQdrVNaT3AOKP6+zTHuaEcnefGLbOG8IIKV2K8tT40kt/qzt8joAuGLgTINCj6rGea/sylR9e29xXAWnW+eWOpcbN7ijkd2zqkju5NYT4+fgr/9/HOO8uR+Q73mtmLXSRilR8UfzAstspZLRoBLNXlHYlypA/qpFLQr3swVtCHQQ0f/R11AuEAvga2VgN6ZFlOHp6rtdAyId8O1IQvmWYMHAGe5SsiqNMUWHoPjQCjDCdcOG26hafszeV+ZZajwvjutmS2aeO2VNYMyGJkidDDNHZDYqgFNvkt+jNW6BPgQB/XHGH7A5TaTl5QNEgGQQiDV1HCQE74rlRYKD3SeV7lUJqK1LUuAgTOCvKRvgQZp4LXviIg/2MG5KqTcKDotH1UoWpNbFaP9bXHFerU/YS5F0pjQ1YiJCYeN9lN7rH/iGeQuC1YX3BgpIdD2mfnYEim3EE0cVknBa/Mwxx5yPpVk+SSF6gtaZeUAPMhMCictVwri5SKIOoQEu24AdMVIrMj6vnEQcuqeYOZ3e8oCaAT+mOq0r20Rm9awVwQa4fD2D6s9ZxcF+CkbFwkduOt4TkB3VzYMZVCiSAGn9siUrt30K5H1iAMLA+fUVLIPFAN4g65WAvxcHGJ0v3sXvcuv+LDAM7UYkuU9LK04WyFwtCEh8eyf6R4cWM7szk0KgtsvsHFnPvMMUEVGr98M9q3/E6EQfDJ1S5DRAUnOiKA+cLyUzc8F6zMrWOwlHbipSsQVo4vTFjGcu8D182bJ21BrfT2sq9xhF99oEmlmyBpi7yfrXJnqBBJSAcEUzgJvpayMcSoqFujFmYlx8uNahsxZuHrdLzpqR1BetLc1gsqeMY3oRcYXSWNLhVu44xJFV2Rh2AJ/h2eLdQoZdwpZfgHdrSVooAQMrLJEBxWwvHDPqj46DpJ0/4OcDVy2FrhurGBmkV2OxRrkOXTRxht0vVkURimPJ8w1k+PC+USdq+WDE808WkdmTsTHTumZ6uE9SYMM9dkGOtNZzsAdtYN0iyBtzgrXWgeJ4ICo3RJyjoXsgPjNEsYYLBDPZbgFw+rBOHvWfUVhko6oOufNy74CFJG/SS+fGfLlwFgK3qUzlKa6OvE8q71YRkybeuX6gRQR/TbZk3rO6puu4Bg7JD1BN1rLJCrSe/SGczrVvQthLDLobZPFM8lmWua1ifcImVhFgSChJ+iPc4WFgMRq9+dg0V8bNarQXEGXoZojpr0p0XR3PHGL6lOLtsAj67n8iDzfP48ESU4QKrtFV3KNiEpAwIc66f1ovpShF89Wf+82P/o1Dco0YE1YghP7qhEiLZR97SYGgYsuUQjeGomGjX+jxgBIFYIC8FkqTCfj5uHax3de4ZtDiEpVfZ9AjgHbpdG5TiCIueBfxHSkjgQCCYNkxIy//KOGLIkspwVhadfjq6t8wlY3ImO3+8CbtVR4XvV9MJswtjrqungkNUT/o6sw79ciJIxwE75aDGXl0RG9woz2xThttF4+ovM8KVnNW8kbdIoqA03sqMG15UyHTBy3QWdcy4s7rno3RpmVQS9nWd+dAs2C7OJC6S9Lgeqglg8Q0rkL07shA2cIMNstviLyAgz8m6jd8fLAb+v1nLS8kyVfYYTllemyqnJwUBU8XXBENFKfaVGnB1jucjM3U75Tdq4dSTTtHOWkUmBDsok5q9pHAYl9P6nePo52nrIZYlYrbvwfZUc/4dhplttRnwVPV2OHhhn7cs3tn3eD9AnD+Gd8QcghPkJ+BSATRQDoraMHbAqH4YQHc1qspA335dznQmjHGMQK2efpxTI+/Ik8dyfQKLWwRkSx4rITaW9LFJgAenRVZb6bw1X0p5RsDoVcz0iIOrjJwog79mH1vAHoRUj0ZgHVuWxKngF0JgZhi4WOVClZFOX7Ngo56WAeG5ZStRNUK1/kaC/V6t5uguOI/eUq7tUw1US5wPvPcEnOUxZr/VGiuOc8KEcdYNugdqFenV6N27R0P+SkKrnrn5wXgs1z7ljkmpWphjhE+ChH+f0zHjgH896Mlq1/YyDIp5SuSa4/Rc+dOY0tyzniwcC06mqNtk2tm6i08zrpU77sfHW9sOps1ouQU8PyKvzZ87oKalY/B5irlbee4ctBw404bnGcuuGX8xoi4NsfisRO7lrd959FP8CnrKSzWRskf4dOcXNT8WcdP3zVsn+XXEdbnVnEIjz3gMsr4QO7Jkw3mp3FW77vP3lgr9Ugu25zcJEsHUmqvU4deelfPXArykSYt1LacY8k+lex//RMVL4b/21ZhBnMUl9SpyXatnUt0feq+Ea+676nXCS0EjEfzb74lH9J3n/l9cBXQ+hkyjZZ5K8mZRvh3CTBgUzhr3+vDfsDuYE3pe5ffX6frEM4ddCJPLp+d9FxzboZZAA6siTsb5+N/dX4viICCORri+98VGjswHR45nbRvvcdJROJwbkevJiqR3ySvuLHXgSgNSm4XoYVC3XCh9YRw3eykWOa0Pg+Uz1udcQ/kSoi+ly5K4sRhNnkmli4pbSM1ecTSIBnhWntGU6/TnAzPgCwY2AZrG4ksyMjO97gScyUMbNcQXJMJuwny4hU3l1OeAuykTPtouOKCbBWT8+gV90oyXM4dngncOOzh3QBFYYjnhE2VndS8U0mk9uydrSnYRxha+aKqJqdP21b8n8vqNFCwnYfnGehg2qDaDwaAG2zcW+qSWPFp6y0fq74L3VBSa/iHkAdYKtVALXZakRgVWpSVCbYXSlrRUXl2K3jj8ls8ARXzT+XWJpQqwcOyU34Ccot/GM07mK7EUEcx3yHlmDUc3cTBZ4nAYL347B43fQDilIBUPm08nQhXgW6N+mWyCVcP+eaABwIDQYcPm2sK/etJSFI0RUw64c/gq6jPY+3nUqWcM1ZjN4rQ0GzubzogPiJQ4qDEK5otiGbI4cUE+ySKJ9nziCkU/K5s8fA/nWWtMfoHnbU/AHvlsN2TpAOzk7uGFudVeGVO05okiJAcW3u5xPHBLzVNHq2FbFnEki/ycoSzwnFbOjalyIWhuHKdStcKjlxPsuHC/nl9aAVwu0pzJlM9mvZA+fXH+Dki18Lq0A7Q+0tIGjPdYOEFqpCSga66y3yzHyHthQoU7eh+QF7UdWvjgVHLPj4Pcu2BANSff/a1FGJTtsjyKwkeAYrJzqq+xVOOGP48SWd77qcGe2030PzMamLYUQqiZvSZs+qCNgnnrnW8UwOUcr6KIjI0dYNbD9zm1V8cGcFww+T+StzftlXQ0FMIDr4XNGJUOA3iFPygi241duA1RwIAih4hYbU3mn1bvvQnzg0jg3KLfMO7Mlo7q+VrF1ZmyQgfqV1BFq3KftfPRexgdJy3pCN/OajK5CeBYmWTwT4SY92pq4OiNPB+8o4y+3mczdX/KP7wlgVnQSrCn+WwvE454bqRpY9l/UTMid49DJKMB+J76tL2/QJK7OEfaXYk7u/gluDOoKayas7y/GEeOgwJGsQkhSnTDjS1k26BdtIpngKKbWzKkPPPenC7ZalhIkwA=="}]}]}, {"codeGareDepart": "206", "ordre": 3, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T07:35:00+01:00", "dateTimeArrivee": "2025-05-15T08:36:00+01:00", "durationTrajet": "01:01:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 650, "numeroCommercial": "5", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:35:00+01:00", "dateHeureArrivee": "2025-05-15T08:36:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:01:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 4, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T08:10:00+01:00", "dateTimeArrivee": "2025-05-15T09:15:00+01:00", "durationTrajet": "01:05:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 693, "numeroCommercial": "7", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:10:00+01:00", "dateHeureArrivee": "2025-05-15T09:15:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:05:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 5, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T08:35:00+01:00", "dateTimeArrivee": "2025-05-15T09:34:00+01:00", "durationTrajet": "00:59:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 644, "numeroCommercial": "9", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:35:00+01:00", "dateHeureArrivee": "2025-05-15T09:34:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:59:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 6, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T09:10:00+01:00", "dateTimeArrivee": "2025-05-15T10:15:00+01:00", "durationTrajet": "01:05:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 667, "numeroCommercial": "11", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:10:00+01:00", "dateHeureArrivee": "2025-05-15T10:15:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:05:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 7, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T10:10:00+01:00", "dateTimeArrivee": "2025-05-15T11:15:00+01:00", "durationTrajet": "01:05:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 636, "numeroCommercial": "15", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:10:00+01:00", "dateHeureArrivee": "2025-05-15T11:15:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:05:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 8, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T11:10:00+01:00", "dateTimeArrivee": "2025-05-15T12:15:00+01:00", "durationTrajet": "01:05:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 639, "numeroCommercial": "19", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:10:00+01:00", "dateHeureArrivee": "2025-05-15T12:15:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:05:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 9, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T12:10:00+01:00", "dateTimeArrivee": "2025-05-15T13:15:00+01:00", "durationTrajet": "01:05:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 680, "numeroCommercial": "23", "hasPlacement": false, "dateHeureDepart": "2025-05-15T12:10:00+01:00", "dateHeureArrivee": "2025-05-15T13:15:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:05:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 10, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T13:10:00+01:00", "dateTimeArrivee": "2025-05-15T14:15:00+01:00", "durationTrajet": "01:05:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 700, "numeroCommercial": "27", "hasPlacement": false, "dateHeureDepart": "2025-05-15T13:10:00+01:00", "dateHeureArrivee": "2025-05-15T14:15:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:05:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 11, "codeGareArrivee": "231", "dateTimeDepart": "2025-05-15T14:10:00+01:00", "dateTimeArrivee": "2025-05-15T15:15:00+01:00", "durationTrajet": "01:05:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 647, "numeroCommercial": "31", "hasPlacement": false, "dateHeureDepart": "2025-05-15T14:10:00+01:00", "dateHeureArrivee": "2025-05-15T15:15:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "231", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:05:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "CASA PORT", "to": "RABAT VILLE", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}