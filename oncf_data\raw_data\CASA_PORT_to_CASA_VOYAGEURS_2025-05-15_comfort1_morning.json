{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "206", "ordre": 1, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T05:50:00+01:00", "dateTimeArrivee": "2025-05-15T06:02:00+01:00", "durationTrajet": "00:12:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 767, "numeroCommercial": "A5", "hasPlacement": false, "dateHeureDepart": "2025-05-15T05:50:00+01:00", "dateHeureArrivee": "2025-05-15T06:02:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:12:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 2, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T06:40:00+01:00", "dateTimeArrivee": "2025-05-15T06:51:00+01:00", "durationTrajet": "00:11:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 736, "numeroCommercial": "651", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:40:00+01:00", "dateHeureArrivee": "2025-05-15T06:51:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:11:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 3, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T06:50:00+01:00", "dateTimeArrivee": "2025-05-15T07:02:00+01:00", "durationTrajet": "00:12:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 765, "numeroCommercial": "A7", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:50:00+01:00", "dateHeureArrivee": "2025-05-15T07:02:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:12:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 4, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T07:50:00+01:00", "dateTimeArrivee": "2025-05-15T08:02:00+01:00", "durationTrajet": "00:12:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 764, "numeroCommercial": "A9", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:50:00+01:00", "dateHeureArrivee": "2025-05-15T08:02:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:12:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsupROKEWSLQer/FCotRSZIn1vL+zGGrQdpsjRjaZ5EFucUROUAFy+/J8N2UepP8/PWAp0CBSbBO185/6V5qEVoAWQL5bOYWGcNXGo9IJmkpv5Siu/0VFaCtp7FLljLK2l4GtxGADtvXpjtqC1TSQsC99WeV2tqYqYImk+CwpbnP/TGHWakcdvbAsj7Eu4JDY46q59nV9kYuCHo/LwZ+bi5+hYW4Tf2kFfuUprzUPhXWTBzsqONsgPXW5V1bTpxU46PHvo+70xg7gBxrs2pqbD3RAykd+X/UDeI5jwyagNlkqVawQAZnorJXym4UcpWA0KWKw5YBnerzVCUxAFs8jSy32Ua3YklQTmDTJj33NEyNmaHieq27/9z03mgiAcCSIds2aAJ8DwXXMMS6C+d0o1QRfCXsIMc1rFghp6L40crwMfN27ABEevbxfGg4vJ5FnDvenRhGfrAgBvzSzGLs66RF7cOZMuDTdsf/n8Ux8ZXMRt2iZ+lt/4ImY/5Gng9FNbedK76aTvTHkre9IdDx409UcvOgvuXVs+oz+0v6J8EZ8sezd6FpY8IbW7lJPmKpTDjDtgqEHKFbV+qUvDnmZnfVy2K0KOTA+PS553nQ/9b9yYPdficdHbbhDfc6URZdEgYslYVBXxig0JNgGlC48hYvS8++YEFEyQAn/Zx74gFCUEIJO45Mkb0V6LtG2IBbAaLLU19VkG5NOn7bPuetLZDISRM6cU6OTnbIYMLlUDT5nUP3AYhXjcuwFpdclqTmZm0klOlBs7ZhqKu/8k0BSaeT2Co0RpPLf6agpC7VB4CAHTFQyExglR2KbjohrAFAs6eysFt3T7VC3UZv3rft1g9AThJ1fkxIufDCDxTCp+KxGp36aZSS8CRHUUZGFNm8ygToJdlDTqE+SPJjfQruJFWU1c3XyW464w/FGWg4q2sHjkcPjYck6Hq7E83c+NH5wASaVhgD4WhOlN4EF2P7k9MVgT/UIRhIpLhFToT5ljqGDwSpf/f8P9Dp41knRcdKynmgL64lE6WPKg29cLpMO9UPSTCWVCeqUndN07Z2P9SQYiUpn5pCoHH7Jjg/F8vB9suRt33QVafZmXEfVt2b2rhGkkgRCia97Mvqg/e89O+vGgk+pexEeiJPmopWv4fQJ1zpg2HSvA5/oL2hjwuJgbh1i8EyC9MRqvABf/LZ/UiwUln9EpGI3mXf1uT15QIX5eRAOAd9Bhwr/tg6cxDBlVQVO2PJA1lUDhFWkypWwghbr5VHDfXpNZkoEEFnPvleqNMe24419XfjJY4UuwQEhLd1pzVuaWhfxkISi3WwKtkXEUVzPAVREq/Rs9qs0+o8TFNS7kWOnIs4el5zxbeVWMtKWQlecT+/c2oA1t4RSEQgezcuZuz4pkKXNBftiUDij1A3vrwWhawOFLjGpurM6JOIf3UXCnhdQghRFKsdY+w6JOVZbFcxiXRXXvY//McEnLJa39dlAWmXBoIiVj4GZnIhmp6Xs/+Uq8oNo5KPjxK2XU8E1Jco567SEt+6ZiRtA72cD7H+vH6EdtignImsxcwF6qLJTy/VPgm9+R5Jeb1SGCKMVjcCcFcpI1Lzu3wLBcPzbskKdSRBAeOWPr4S2WNnTaAKIckDKDOIrYeAhD8ASfxoRbdZXEb8ngDGhhFCYbQvQ2vKTCjLD6e9mAoq/nc2WexgJMNq5Msp2/KGs0mZ547cKMTRivD9bUojn9Y7PlshBMmuFfaEydtPd+qwDjxSF+9QuYOynrRfPrrByF6i990LU27cq3splzrRUZSyLS1+wJx+0cOXCGn97aYxHvCnBdhUED6a0w2Y17GkC2BWHLfU5itgIKMrhZ2YTqmAwnpjbSvE6XAI04+vFNMCtpa0NCakPiqayvIuEEAbxXPtsY5LUKGnGP1T5Prlx5ABb+Tgw5V4vhHx3zrZfyGvagcZFKf0/hchuO2MD6oOrqGKzUSzf4nrGP6/gl2vvRcTKhFOziI13Vl+9c/eCCim2fpg0Deq6d1WxILxRvovlcJ/XfK00rHlriFS/JcMwI6Qx/BtBqa6MAdZBu6orGgUQXhg7qPMUrBRKXxtfa2Qw+FP5CCH7p2M0Ss4O/hHDBLTHWhidFuKqPcsbXu+n8TSpR2r4Q+5kROOaMRtTaFZVl1TcnWVuso/aLB86HejvnBl03GeDFZF+iOY07YBHCGFm6crVU4wSSrpTF01oSWhj4cBy0+vQSMRc4/8CJ+ufETaQB/XqhAzBN+0GL7yPpxhBXuMicUg83ZzHDwDnk04AuTnDLyr4R+sohWaJupx7AebwTM348BAdkozd6iVgLvFn7ELn8ROE68d67bGuRIYHQ6tupRWX9nOcd7WBpsnW8GPwZe/nUf0GsCFcT7yV2v0o452NuYOWvzNq5mwTboW4cUaNKuZFHV3O1Oz3fsJbaDQd0M1n3ChMhGUPRVEJGLG2oVS+OaEwoKBSEnhXrAvfsozIfUr/yYBQ8S/2/h4pq2riZcqcBj49ANejck7drxsZllWlA/GFynm8hfJtD3hzLrjuOdML4k6lLMoi80jTgwgoVphNwx0E87p6nP6nTT8J3V5qzLdeaHQuwlt4bCePzazJDYhSwPNh+Xy9s7fg/scRDCY5+c1nivMwe9WKo9P4TT8Y8+j4VkwCEUdmkx9flSdB/iD+//HyfftSuDAhiEEuxuSxaMOprfKhUjKfelMMTc3h9mRnAziWYc8mH6dpG1sGDcTLPDLsUn1IWF6io3NKyr6btuXeuJB/Kt9N0IDJNlYVqGciTeErMrygFfKd6/EGRwiJ5bxqjATn3zu2JCzvJzN1R2eci5CthL2E4puINBWImiF/LKKwK1Kx7dK+dEw8Qr7Aop1z7D9sda/v/HoV+TlvDF5gfLdg6/9RQzTM7SHXTdVDTHuPq6MIyyqEtaMRJvL38kN6HKf9Lu/pl8PsJyhGKMeq5lw2FMxOqyX28unN+xwaWTuF9gJly3ByNKJ3qCzC/Pc+EjUFdaHLeNWomI7r8AfHYRrzXKJMlolC6qm99YVeXuDAZJ2aLt6jtxs4CL8SBOkanA0pP2sk6DcYu1e/DZsycUfKhZImARaXmrnY11UYnQAzfiIhPpTZ0HSaNkXhYste/8O1a1SkHM3hhRQPwuxWMyA1S2XOSM7C3YkXAxWDmSELMMkNyAC6fnUaMSGnK9o8P155qmV26I7f+QmVbSVTl0ZskiYEXZ5NXxbrFE36DZbDaa9OJ2Sl2SWhtoH8WPy5nZFqK4haVSaCuK0CvmEVHedFuN/gQmwakfzyrX4terld/BPUi8mcVaoPVsKlztvd7e0zUkN4y+bPWc0cRCVm3MR/5VU7bwBSCSiUx/lNH8VUludviHgphSrWG2FD25PjyfKiGoAV/o3DqJvg1a/wFfYIRKk1Tsrv2rcF/NPEr9TZL6/JBOG2x0KB1VHWzRGHo5+gOkKoC9zNNJzPC6jh5i0CshWcqcDTbKSzySUdK/mzTRUPDkrxd4nVU4XNyOKwo2qhxFQAH1DA302ncgNJYG0B3BGaC4HdsPn0Q6MsRKVzKiECbmlXDaOBt3Sdr0BGgd+KSjUMC4QF5IrjGb0P3gxja7qvypP7jU8OGhU79XyfajvfEOIvY4s/tu7bAm3NpwERmhZWhnYjnRu/olq1SbN25CgUQv7KO9mSHHyfbhQ6nrPdIrfU6q8GyaHc5lt9KvI0B1yFN5kiRn26b7Im2ZN3mUQrBpAluKRFBT/wmqh36uTgHhLfLGSMRscLge3Hh9/puUhGIZ2qWrdPszBSHbpEXVo3aozOT0DoTNM6GsrzkHNhD8nyrQdGP40yQaMe8u+QvKzowZDvtoC9ADQLKnZFmhdnwus3fUoVAQrHJloWMmkK7qiR/ge28xcCESrFouEtONj2baZoOqO9O0pL6x0Zl+eZEAR0kzPqF5yb0xOSdAmrQ5aUQxpcYDLikxo+fYUEMIL2Rtj2EvxJSkH6UPLe9XruKrJAEnX/a8rnbVd6fFZaDKknxQ+lRakDl5BBwSn3dwRD31N7vSvF77v6sYLzy5HcMai+PrvzuDCAwWb+VDuMy2rMoSF/mTcY5btfuYcGK+YPkpATPADZQ0r4VIWPsHZ/Rr3IxYRMtomt/ncR0WJx4xYqOe140fNaE+2KxdZzHIPTPpXRND+Y/dKafNOQRCTGQKsFTTHi6lfUntrbTZQ6FqTVQKAz3l1hGqUpkBUUiYTQs4SKEVXXVptwAJKyrHCJJhVzmvaPjHi0WYjWetH1gyJmoG+gyVeEP5C2Io+C+RhBJHmnT25BCPb+TBlQ8yj4WbAZCohy38i/xo/OY1db0o4tIdjt+hQt0G0RF218uQfwh1SmCsay87wOyHcfdQgPrXVGIIG3gVLhNchjCB7DGQNZaP0W7449l1cOHMTUKKgkKU25LNv/42uOfz6Wt4iVTHahBk/+QhA4cvsFvAKgl8b6kzqOra4bY1Fhp9hWKxX8i83YdeSta1vCXTeh7N56h2/UlmOGZjLe53+E6mN0tqiX72JG59iB7exCPirlFJ3G3VAu58ZG2IsupPdU9HS0hPvDkg3LQOS20bWizcrDUdtGM6ZOkQ39KeL0/AUlwJIXQvS0dFXhJsEQuYcvPMJBG7vyOUV/BeOYs9Y4tmH9fhawvWSyVQTAdgpoixCyvw0Zj7ccnreBTuny3Et7HgCwadW6SB8LgXMfqj87TqMhKrFpgqyLMibTFJbWe39c78ruHZTNoyGzLoHlFtmeb4qWbnJNttaiLuUNUCJViosQb0Aru7wxZR/LpQ60+GvMnV+KIaxhexeYNNHhKJ7V+lfOkk7Bge7eLHY+m0ndx1E/+q1g5ODotETvPGywXC/Vk6Xz7etoOmlstghqVIsuIXQDHK5Uo+iNmESlwY/TslGNChJ5Vwnd4Efimrg6ion/LTKW54HE9s8sanZeVr4w5D+/RPSrdPCs76SFAoAtcUB279M0PI9jZie49fYmu0xnNy0g9iRC1IuD72HFqOIlYVel4sYroZR8luEn3rmGEjHXMXpBat5xAjF/ACW4t+8i5tn1YoyEdoC/iK2+rp01A/GR3RNUI/dgzNNkcrOpN2C26neezQGodQV5E+Hjbibv3vkkyhK/bXF7qW85PtUFDSeCr+U6su0WrGeA3+VO7sNR4gX28F46QDt+7H/MKKMKhAtokzWMmA6KupplNWpXBzNiqWEw1Ke9Iaa62kKyUBVgf1GERHPtEBWUPS6tZs2K20VmmPsdfQg1DCJMyaUQG/Ko3jUg/cpytJr06WWOIPXzmTJD8+ZnXurcv38nAH7xVsEGP0IL9hOLGFB0qKgzmBYegFJsm+Js3foVsB7hYCL+9melSyUmGpSCDaOs1LkTcdoxeqtXR3Kc9BOpNHYtswr12sAKks1I0t0IWWgs3EED5KOpKa/RU8TaVCbAMWUbBReB2FHatwWAoXOccePyWUmlxtrqQn3n8KQFFWFsObCubnuW28VLkCLXXyb+vF1UP0d8cIqVEiOFWxDKJZgq6aWU8nnRWTVcwh2GppICWgFuirWEm7Wghe2T9FmNHYZZoxf4oxZkl/OsjRCYpKsxBEnAAd0LzLKJgsGuHXfNAnPR9DKwxht6qaMgkkCUt/1YrMQViE8mMMNQy4vh5BHHY1e+aETCZe3JYAuyIcg3n+/Lg6oBmP941DLtV46x/fvoCqwP1jUBqdFayJT4unGL7uiCOII+FqKGU7rt2VgM4q0Kyhw5qn7gEvpvx+l28dLen/TunNw/DJMAkGp1WiPyWRjY8vMDdUdoKbIbGhjrley2yv1EMyuVAGOvoaD/ZcSINmdjlIhsoIOXyte0XW1GoWJv0AxxPE9HMXKorsSGTfCMpNgUGBNO5k16kgSXWx3GDdE3FFGMLWS3UVG1zZ2MF1eRTRNANeI52lGA42FurO2uZhFxABuaM5MiEFuQE1bZUChdMwwiwBKGvbVgTXpPC7tn3MJH2nrbFSHnk4sbnA+ZqDkW5g+Vg8Xm6shxzKj5QYRA8lzRdAeDghW9wjsttGr40EJlholbSv0p5oHz7fzbZcLVAq2hLUbuDXHBI9XudtDm5CkuuzCX+1cYaXq9MxD3v+vYiVP5l9CTrs5eTztKxqkUgc1TPeRAg+kOAIlA+OfnLr9vHq41PhB9CjRnBa8NDjoK05RFXHo/2ZCKKvkKkCGMEVFTxmw0LhYLIvUTUVWceR+z4NTJWEkBTKjWmi4YYCOLbeQtS2zODqanlRtaL/C6YxPy3Ccu4i+DmMa4P5qPSDMEYrlXNrgya8HS1PDCj1XqLr8j0/90IEJ3isZZFy58xjbXqAu0gCETSUxe2B2QfCepzlszL/bj3dR1HPNYyB1nAHOVaoQDN9CuO8SkB2aDyYF1CnNDyYTyh1TjODP1cEuS9tsMWg1KZRFic1FibwY6wQaoVlQp/q6ViHRvHbOTxAr2eEkXNrEB1IIiZV5PjPojRlTAd8j3lHba/ZJHvdOoqBoGdukWpOfGvKGFJAh/6wrNhCQoy5mMCHbMfbxNB3QP+cDwyc5psDlNUac5tPJJbAe+p33ZW7Ey2QuxM7x2hIBNcO9zI1eszJ25Lq+ihqvOzdBQmBvxxJLI4LbahkZ96yfFE6kLvy9GZzukAkB1j09MEnaUDCUkck5J3rROQH/CWEzaNqNPQvQjyVCF1Bi4EsK0RbU8tQRyA/bGX6YA17iL7dKmXHRB2zZbD0prkqzQfa0P/1PvPw75FwV9NkEQMaB1YhSP/fp+N41UBdSonb4Ed0yeV2egQ9bAh2Vi4lZcRbVhyqDi4d2v+7/qnEkzqKkeHEIqY3EJy2WoW9NKjHw+6pdYmlgqsG85BTVGaLSEAxqh9cWbR8CLY5mFNwoQupKI6PoMqO3XLZgzpe0GhxAW3oqrcf3kt4T7qQK883k+1oBKuefKo1qhGBKGKwGl3p1Qwo9UPx2+FsV9sjwqAtgP5JHGAcRyXxt14pe2SPyUnFm3nveMPcjZxThqnVc5osa/YaebSPk+t/MkWp2XTZUWE5v7W3Z1mWO/b1MYW/H1pt6rI5+S2sO7YWO72d5uGwLXkacZqQtEmIT4X9VtEvkhoCfHGHrgT3R77+HiGCu/u7nU/odabh9n5+1hhFn4UZ1+3bkAkg5cRoDvx+Qcr5ITjqw05hq9n+OUMFlA6pzgcCU8M/Cnpj7tXhhXRmP//bsFL8dzbgkqEY1Jcz5ziQVd8nfZyVjbfXFGlhrijGBo6JOdrxKfIVoh47wxXPj3jT5aY8FrbM9vf2xgMLyHLtnB391cgVBrLXJSeUwkAKOgZAvXVIF5GBUmIr2n/v+QoRrTsG3X2fux8snNNu0fB0GNlUqEL0IAfVpDSp6zj0CfjROTzuo4AZLstjxLbMdU0XUAKICmR3Dh3jjO5h6tMc5WZ4iUKgZkMu9Y4HF8U16kYaq6ldBZjDrjaiQbMNe+UI2jCOctZT0cMyaDZdKnvDJhmb2Fat0cz3j0vVwwqxs1crQayAbKEhP5iqrjNuxAYdrkeq9LNkNgf9h+t04aR8lMJthbYPRuCr1pI92eoe/L53eKSHtq5MCx6D+U49h+iNWSjUPn+UwCUAjG9SH933SLeBF/UgHiPDpJX+L7sY6O+NafyL6CwOJq4Ny3+RZo60idtsIiLLjzfmDYurdFtR/7jiop0bbX+N3+1qFV/GkUutlXFznrMyN/nlFnbgsBZngE795x/LAGlSfeuQ5ix6DZEl6XJ774H1UeOyn7RY+JM5AIHQkPMnqLdqPKmmS5+JZQ3Pr3vX8BB4w57tuwKPKi5GwHsGSdjJcz07MaXk6NeL7nO5xgZjQ6ME2Ufl+TL90XAoD32hmMY3GEZSbPNgT87yrinC0d/OvQrBCZmkk7E7iqpLkPtVhMWcRnQWVwgDMOGlAYsc7GmOKw3k8lPLyFXYC1YEzoHi4Dgar6HDrfj677guUQNPboxeUuEdnmuDtr4MHIaDOD166BJvjI0akSA3OWIUn9W0QVaYfgmV9c5UBrbper7yZ2mcIL3YryZLOTso6rCR2mea7pLbXWYr3rC4boFkM6dvvOwCFPqVNR4GFvXBbDf9QRXfYb7f8UoVoOqqmUoOrzNCypX3id1kwDV5lZbdMBeCHXLShViegIgjjEhzpWJmX8y8P5n9Qn+6FxzndI91gdRYzfL3vLputuhO/sB8PhG8cA8jXQwQo+5UWadpGDsCHxH/qZJFWYAeP77Qz+z3rbXFt97VZC27sIvnerarsvnXWOPRo1FqZNLXj8ydsICIPydBo3NWzG+/F5MGHiB1dBu1C8dXNZ39pt5YIOIhpxseUshj1ocR1ujIsrIfsxLeDzHqs/Adu0D7uvffeIZlcJ6vinzNiB1V28JFiHH2f56rtaD+cdOBBMZon7rHQ7HSLhZk2QFVGNOxHTN77uQwLUaFnrrKChHanAS+Adp5QM5XaYazqfBFDJ37lirv84KtIR5ytGxWnLi4aP6zW1jxrB0zPrgKgCKXLp+xRy/lTfv9dZXUddeHMZjVI0uGFKczl4WyO620fD+Lq1YTUSbU358b34xm0eWlkFw2EUXSnKhteY+6iyZHTOl354fsRa9olQPb41Nf63syGJGMeW8T/QLPZnsqZgcyD14i1QwbRZOMtpME8FM91HPoWrCDU7NjSrouFYKztIECyzdODoEVST1SDYnaqAsKbiDaowFtRRAFZ5DxdEH9ItqYE20f5A7TvcDQ6gPPJlkh801FtdoOHBRLRdeU3vEERvapfprc/F/snlMI4dq0/mQxIQyjGqdvKQBEG+CCzIhIcS8wJvDHtSFN7HQ+rNGnY4wGJPS2+x5hZjP2snnkhjHH1JDwBD4Q7dSNA3mbLfCOz2Vwpha0mq4NHnpny1kTj8XYvnN6xeObv4Bza5XgLXl1pGLqKHLDVaTtMEnzaT+V/8Ti+dPZKOfTcCCtJUYC4hQrT03KAjpe2WryGUADtQyVUNFz8eKFhCekORPpHWAjEJLQJRCd8DksWU6T9sHvZ3+PzZ2KeKPYqcr4WT6tH89S78pcpmTTozS62htJeu7WGK4jf3UH7v4uspySVhY8cIcixHUN26PEhNCcOva27rG4gnyMlTfV4jWnb/Yt4xWvTg/l4nNI94fJqwJtP/8ofBE24Vyvctjf/Xj8pY33Ww5mrjahDpUGiZZkMAEDcLIM7C0rEucpzIsGvHXFRzT6Spb2KgDR11OwXro/M1hQwEzDg7ZF3eAPc6N8h4H8drMeSm8zfhiQANysXh7KWIHwcTfTuNcnf/vs++kSfECazSOoOPlp/YtBXOcCaKbx3MRYuLvwp8BtZV95D7PZjB95K1QhnoRFO4z4tttr4qDUip+uhlMme+RVnQ7vdXA9aekTVrhy2D/1Gi4Ou2ukawG3dCwSxAvQjC31AmLLClsjwjGq+UUptqlzAyLDKYYjGe4zbpgvusoYhGFBcgvAEppRxIXL2GZtsdy69n0zILD3fR3l8Pe6lMhzY7selXxCTbg8lGu7a1ke8yu16FWIoGdN4+a15lO3GpeGgNpiDdpxuKbVIvjfs19rtg+AjusJ2RpSkR1GFhqnIEYgsRHfhi9pt9/pDzAkBMFCG5e0WlWKdkP2ah0BFzHIQy33KSGzTbX+1E/Slqo6M5i0e2co2y9gqQclgtoRUYEnKnN2d4auyGoCxO88zayYTrf0QI6r5s+Q8iZaeqKAwb5MXvcPKQzoYl9dr5EjyreuDk5kF8E/avwY7BahUK7LUjNWLEk4oUGnOoCmu5pc5uaCsHSaoyiVBGci24ieQi2dQSjdifOZkJdxHM6IjilLMUORonvM0z9YN4G21MLpqBr/xNx5xpA1crFzsZIXU7nNWyixt8zxDGMhZHHuxIIoCH4uZpRlqt+1HDwJbAN2KJc+sRylRjVMww9y2tba4eVkuCdFeCic0mBrMQdf3ro9LPW6wrNxfJU7pEFAciKBiXefJbfxVyg2/f29vyEZK5jxS2VSnKm9mr3TDm+0uLzx96MCvJRjQkW6ABA5dFFQ7jHS37XOxg3uXw="}]}]}, {"codeGareDepart": "206", "ordre": 5, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T08:30:00+01:00", "dateTimeArrivee": "2025-05-15T08:42:00+01:00", "durationTrajet": "00:12:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 4313, "numeroCommercial": "803", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:30:00+01:00", "dateHeureArrivee": "2025-05-15T08:42:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:12:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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**************************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"}]}]}, {"codeGareDepart": "206", "ordre": 6, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T08:40:00+01:00", "dateTimeArrivee": "2025-05-15T08:51:00+01:00", "durationTrajet": "00:11:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 731, "numeroCommercial": "653", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:40:00+01:00", "dateHeureArrivee": "2025-05-15T08:51:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:11:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 7, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T08:50:00+01:00", "dateTimeArrivee": "2025-05-15T09:02:00+01:00", "durationTrajet": "00:12:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 727, "numeroCommercial": "A11", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:50:00+01:00", "dateHeureArrivee": "2025-05-15T09:02:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:12:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsupROKEWSLQer/FCotRSZIn1vL+zGGrQdpsjRjaZ5EFucUROUAFy+/J8N2UepP8/PWAp0CBSbBO185/6V5qEVoAWQL5bOYWGcNXGo9IJmkpv5Siu/0VFaCtp7FLljLK2l4GtxGADtvXpjtqC1TSQsC99WeV2tqYqYImk+CwpbnP/TGHWakcdvbAsj7Eu4JDY46q59nV9kYuCHo/LwZ+bi5+hYW4Tf2kFfuUprzUPhXWTBzsqONsgPXW5V1bTpxU46PHvo+70xg7gBxrs2pqbD3RAykd+X/UDeI5jwyagNlkqVawQAZnorJXym4UcpWA0KWKw5YBnerzVCUxAFs8jSy32Ua3YklQTmDTJj33NEyNmaHieq27/9z03mgiAcCSIds2aAJ8DwXXMMS6C+d0o1QRfCXsIMc1rFghp6L40crwMfN27ABEevbxfGg4vJ5FnDvenRhGfrAgBvzSzGLs66RF7cOZMuDTdsf/n8Ux8ZXMRt2iZ+lt/4ImY/5Gng9FNbedK76aTvTHkre9IdDx409UcvOgvuXVs+oz+0v6J8EZ8sezd6FpY8IbW7lJPmKpTDjDtgqEHKFbV+qUvDnmZnfVy2K0KOTA+PS553nQ/9b9yYPdficdHbbhDfc6URZdEgYslYVBXxig0JNgGlC48hYvS8++YEFEyQAn/Zx74gFCUEIJO45Mkb0V6LtG2IBbAaFxwhKtSWJbUQAso05Yg2iU7qI6xs74XRarIDsoQyd6kW6Shq3niNlCzDjNSy/1dUfXjLPjOvDFQGEqerbOKJV+sraJ03mIA7Y6toCA9RglzaAikDbjEzRj9EIkiJKfAD+t44MEqLu2ZzPcu5gQiUr4VdZY5soKGawoBhK1aqVrR7HOp+wGyhxz4EaKRCtV4X5hqZWSEAv2cV91tX4lffok03/+pmr8un676IbT0XrVuA3Gq+Moj4ET4J/ACrzM+DXBdtl7ZcNeVMTyxsbkrKmwh0dJf48jlH79yS/+1MpMX/CvvLBk7op+tc7e4sf2zlk/n586A2cVRPS/rSRE7btTSUt6TZgL702TFU9tLfLJVUgDKaEwXuzMqquH4iGLaBlkE803+X6t6DuljXrrQ4tLx8ih74vuNX1F9OuFicnHdOZKC1ciB/cBaB8ULmEPbdQwwiE2exxhseRigVxg3CdBpWVc/qWZpSTVyiwL8sJWM/Lmmk0EwPiJs3s/xCg+0d72+ICXxmW6z082vNtmDpZIMx+5BMAClCkkWJL8FVZY1nMfiQ3UDE+APAjAb5trgCRBDjw9xE1WIyuJDNoZLxrWW6j1C6hw5ZKnrf7SUG/tu3j8UO4SXvBgJBCL1L18hewPllLPuMTy8nQ0VWpd69UgdO3cm+vOn3kxRMAsBW6AnlTGo3eKwWjFawhKyWlSJbzxxrw8QdZJzkip0lDsV2emj/cCYG3GyFvaT06d8sLJlxrqgqU+vfYRt6JFZFOlHXAQJVUzRq1mMBQtOnIZCsc+TVKrSORXRvAFzN735zr8vcSNMX7i+XK+pXLQSawZUmw9crVARUMJiBLzrLzJS/EcTtreC4y05j0wpVAf5WpuXNEkW3rgP3YmkMgqUu75U1YdFTQ+dLRFjAMicr8CdAhqUEW8SPp7T71mW0Igsja9tctiXyfen0cOUyL3aMGb2RzMAjhgkKIIIbb48Bnd2d+yXdnYvnxUwL/vAjx6zvRvUdAlZ7DNQX38fE9P3dv2kTEQ9acVUpyo7hYiqEEuXoq6ebcgr7AiSYNByaGHkr3U2O8eU4w/pk67+Al50ZM42WVD06kY949+L/94nK5CMEtG7h6n3SvGmWYZtvPwSCPuIcmCAOsZmi4/zwHaTqqMaAmhVGMFEJ5WClS+SkzcFo8i4LV74xhY4dFn5/tcowMPP7MG4p4upAUQV55nj4BFj4gh8AcaEP1j+lhEEyzPuFMI8Md1TElOIOw3dRSX7IM2TUqFIjL1V3Ibw59hoUd1NcLcIy7ZiJBwnbbHyHkmZWKT9xlEyRg4Rf3SnxMXI8XvEr5iTgU3qfQigdp586zU0UIjzE5l6VqSsbiofsMSLTYVkq4DMWu/xLXhkPwh+hW3cXh7aHjEkMV5RpfYjQUSaD7T+9WGUnMMonbSHO4v51ZkyY9iX2BCI0c7uyyXgn+cZ3WtUv8oh9cn0ye5QbkN7oVh03p/6/6Kcp5kjaylIs/FQL0IGFS1ifWFlC6rUzyXdqgTuD8oF1lgSAOoxerzo2fU9Jl4ZGso6onaiKfeUfMh/VU12vILOg/3tM/Q7VK/gHVobxEQSMPc3Fl6bYGLeGdkxYOAwMQCrE4+5OTxuuNzDTso/5S9jNuNCtkK3XMXnpj0Kx0pYFOTfV6RnL91irOtePCy0xLAnoJ3LDMRBHuWEqorbeWJE7/lV3qvugK07ThSZCjidsqP+L4llrauYjw5aS4sWQ8Iws+56xsQkBug4D2HL82sRFogsrOiiBY5dDp1++jQiObVvk3rIUqccv1/k4zy9dAia0K3mYewFAWeXLfAT0unP/QmUXanANILNV5/MH5sbt3lr5/l1uYeEFBLQoF1BxbAYiCJDUh9nSICkf1YbwPKV0zKPpwhnDieMRJtGXbmLalUFgqf+FhTMkSXlLaatBuwnG/rRMx3LGFKP/A9ojBzULyjd/s83vkFlumc/P3EL/IjZEqErQqgE+8tJvFaHDksStI+SQ4KYBzpLau0FVUrCkJbIlRMCcA2T8z3t97AvNot3AAzBFZ+9RCYTvcpWG/MkUqElK/WFcYAqqZJPckV6ZO847VE3nX1w3jHpptZwCSdgDNLxjs46G6I4Cxhef535Plr5bJD71cv+VYzCAmqNlreSe7ek1kd+62NwZIyNgtphEij9cpXVjNmGt/rwmgp78ieJXarFoYgCsHsMiBMYbMEHrViw3aQJt2te+DtU9Q+aX3bFj7Ux7Tf32+eHAGqAewr/m0nVd7TDTUJHK5WNcGpYQMOjoL0iVXk4WR1fYSTMdaPTbr2xjz/eCU4AbIEREeGMIq1gGdrwpHqvPh6wdAkJvcBeMs5Z3Qr3gob9fH+GueuZSbuVXgzlkyuk/8ZaoCNsLFEYAosc8qXxYEszcZjzEvfgLmE3awJjnvLkvsToNiw0DihxBzGOg02zBO3gckYkoV+l2ncBZpaccPsDCHasv0T8sKrlqmaFd0F0iFVYrNmL8dCzoWCP2QwJiyD6tT4zj8PP8TJ8B+SZFDhZGc3D1kzB7WXAEIUa4J7v0TPounrHOJhVK01SBXVgHaMAMc0rVhle91rB1PTrmQNFr/MTdC4SY0QLb7XBRuUdN4bfYbHDiJzxnqNZtLkBh4KJJc5dGhNjGeuAKawA7Jv5eywtcw2YQwerDfN4bzGSd2Lwk5wrU6NA+TJNW5TtB6pF/viroqXbO2pRSA2lDWk+iVpiM9u2IcaSACTkdoy1ANSg5LwIE9RGm07Iheg5oe7QaIUqTk3i33BEGNYzVljVCF6TvSNVW5Ye1ShbAx2lyHVMWjmIpVLIPmKW6jm2QiBVz6H0WORapZu7Zv9Sn8ykOjMFzKkFVucr4sEYtWSh60t6r2BY+idtHxZartQQdwqyKA7NqDk3Wde8D7pIvVsSYjeUR3mL7f0OxjJ1omo0Z+O1LbDfVS9+92feoT84JaSiuulbT9XyjmNk3cevwVbgEK85hOxpdqIOV1tya1zDL9S/K7cdF5BeR7+W792euJKYYxYzQZ+eta9JuA9m97JrkNTzE9UL9BvkXhWV/W8urc0LIafU123YKfsiWgEgD0KKEAlbZLtsMpippg8SNvBbb9TF2PoXqyBQ9U/ovopOfomviggVHYF3/kAS6PEi2v1Itz82l/WfogBhpihQDNKbbmQ1uV227DKdsXPjNmmTleBUmj97+/rZkW9/+HGkaIQpuDi+zJd8og+bcvc6EpI0iez+EmRvrbUwP76hrD3zpLiuD10SHRQ7Q3qQSM+kJSjew4WRcN4JX0a6sT5M65GUVb+AP2TGrCzykoo8BEQgMLTpbDgGa9+4KcbcQqwYNqsSgSGVSVswSkmM8zw4Zae/abM6UuqRh1TqtXVWIPDBOzGOk70W0XSOwZm3A3MFJy6ule0nHsPkyCYcxM6QJneVrkb0TlQkqKRAfXJ9w7uACpogqp7r5Hap0PMPFF8JI12T4ArgravLBjPpGQHRABPbmlTjUacKHnnWbI8t5EqfqC6n1wawwZmSisDREDA7PZEKLcaEBPV6nyIO+eocKwOk7z5rGUD5hUD05aBsnxBnZuCujK03mGadKGv71chcSQIl/DRe0HZqdPflCrZKrMo7xzRS4eOrhdTDNnM/g+hpbpn1njdtSR+bPqpL+mw2dV75gaS8oGf5WnTfAdMffLkEpczjiKhX4X07d2JEKL2p7Wd7jCuKvbI82NueAABlnkeuL21Xo5USlGuc59aZfOvRLkWGhl5x/jOBzIG2GQCDO2VLLUbg5VFwoEYDK87LRP0JJkLrKPa6Q9ecdQEKwu3JMvfNaBJEdRCIBqn05869I3ed3+XUMBBWnFRdmwWkbmdDMvkOXihbpKo8y16kLXHw0R7BEHue06RLUyBxNcMYb61P0KF92R9/Eos3ILHJ1IAKjGFzSYO5q3bmkicINFjrKA4rFxuTzKPTH/J6ENw1o7bysHs7AC6imJGGpGGYwPBwu0vyFMOvZv/7qkVqIQ2SmT55b47ytcFDlUiW3Qbqt0EBYGN6tlupNl7b2x9RFNN9aWNtyG0EAT+M+asIlN2csBAJChVN7ZhDmzSs0RWzXfau1XRgiDoaEiXorlMJ1QRtARDVDDp0qTLWRavZOn7WFxNk9ch8ef7InUHiHf4Pvkzod7nux2g8DdixYh27zosLOhHUqxPMaArT08SU+Ntn3F3fB9VekcR10xUcDYme46HmfsNggaY2Uz7M/R1bENBkSnuc3atO/22Y6pKpdqx4pF6MuCWZaDUBf1NbEY8t2ndwg0LdjWGT4CS3N+wzroo0d9mZ+myRSrTBzkv5IBCd/MarcKx+Mw943mxMqKnOAOW5/SUSumaeLzbZa8aRvMOM/sFYZYfYIWS46AtRsSeDkuigDgf4rB2jyhLf4DcReznyHH0nuJY02SdKOPORlqUOM8StHd6thYJgG/prG48PEuLLzqcrWAxPXIqTSVBoSnoQF55dLDwyyJ8LlJ2lm/5j696MZsUxtYF8EHQwdkTyq5JXZGvpTNCN+VN+iO/Z72QK9KKV23RMnvK1ltB9qVsZ2viarCyNEsuaIfoe6STHsW0C44w30AxUoycKhukJX5c+GXCOKq4xhDpOMiEUZbKlgYrKxa/nsXosm/qyUbQP87/uPGrYHNJ3WGDgZYRZG5dQbpLurdiPtauxEP7c9Z1Jv8Xohm/fl4MMK5sB75DNaVnzeAB334b2NayvfnOAm0KNGCVgnXIFHW2oSCK7LDVp0VLwCz+DtfG+A7qUFUGCFaO2Eac400n+L/mIlbC9H13mbgTeInREXtewsVkgadrw7MpWdXnzdvG0b7zRLDQfRMoZRV7M8Xhkp4hVb0pGtQxxoP12eyfet4zzSw1AQn+Z3PAHZhMU4eVe6Xzevq5zw8xSM6WLEqK5ajSy/G31mKiGhBx53iUZ6lg9unNagUwIdZ8fX0MOqpYtxJUJlh0IhT1Wi5ahU5dGDx+rK5sPVIQKDGdHeaPjjEJLwkRzjyCt/a2+Prx8YkZ0fNalf1R5HrgTUOaaBvV30khKfn9XTQWYa0khqpjZyPYXH7L9an1snzGq/N3dg/v6oii/5zx+DDm6cIhcBUbXwnUo2RPta8r/d+h05XUZVyYgPgD7cX1/CZ9Jx1K0KToSe0S8CKX94LzRbsYttwgyWZflRdODLG6dMBI8htNzFaLYCy0I3Ffid2uz2IKuLslvbCDwGl9x9ouZjDCOCHNwE8dRZ/sBeqzlZV9ppXQblnZKvgwyOCzyv9kvGT/HuYeHfwwcwB3hemQB+D4Od8x22NUxiNRgf6JPrm1fZBTtMiAoCCqr3jS4p0Wyu8+MiqmILMLIkl2plEJ+JQSENCpGMCtTCoP+qF6bxeDjIKfQ7nphqcQyvNPe/3uR+L58TUHid1rw+wAk+sWerwCnXfy5Ab+VuGF+AjMW2Rh31Jk1skLvZH+0tpg/tYKC7mzA7tA4DARyfpSuGxm7yhfAafeooMoU5X99zaOlwVC5fBlw9HF29P4+gfxH6VAKHiVTso4iNFSY8aaE+jgaxiSBgsgDzMPsS6SygTqmN4cc2eieAevwET6XlpVFgxyoNhoy6PXwB/Ctj+MG9ixuYqWxYKjoG+PH43P+k6pCt+Yb4g8JHkzUBiTOV/dAfPho7RNRYecDkJxbkiIlqYFO8pYjoJE4QIEjJcwPLyu+l98DZg0t60f4pT6wvBG3/sjvWDy9mxJY3nJSNdTt2119whvYUIghu3A7e6wD7yckw2ySkzIumprUoyvKn/FOjWWU31KJIbetDwYm/sFw4I7td8LVUkgnO/1CBzx/HQv/EecBAe6PT4y1vSixVQ+nf6ku/MwaRdDb1GI7IP1v0ylJRwINlQnU9DhrPkc7rK5yNv0oaA3MYccrNSeCnVsxi8v1HeKwsa4wrz96rR1MQYXO/jrHb5ywtcf84WlJvY8kuVm/mJQioQ1u8VxElzG7mbnZUS1Kl42Aig8kVUIPrLYmOyoKMZzkZLI/p8bI7ffMLzAT6F9A885xTvsyx31hzTdggseb2SWoQSRbpPw8S5Rqxj+pFNTEcVSZTZ9gbDUpd4wYGMRJCzYlWIhifa+KtghDnp8Pch+Q3bWa9MhIM5QrBLxXYoHWxjcd5Dhil0ki9bOBktKrsU5jyVaLZE6SMmGpFRPHZqCKW8/eo2P8T+aZ6sfg7uQgHBifBfVx+ZwVwdNaGdr/NeBnCJM4JtKZAudfW96CYsUluMCraytE99saEVehjgUWPxahVPMNELXU82EYIateeO8hjiHxuqZaCiRdFPaVKVUG6VxLf+ODxVIFkV75gNS8WISXV36sHSmtEvNIuWFy/Oce3Vq73loVUrSa+ZFGv7LZgUNToT/cISea3vxiLtlFm5LRZ+ICDzyjDoo9GzoPtdA2ZK0sqcspNiA1rdMkIwTPGh01cv2BMdCi9lqDDipfoZ+8WBQRUcfixr/HxgmPMIFNRYD6sL+ykkYuiSLntuq9CpYNgmJeDIzGFnWT95OAWIErSzhFkxLcj5zMOCgKyBj1w9ib5tNtAk2ezpjfcPwgOHNNBINRyPYgeClC6zApTYQ+js0Td9YydnK8cN+c+RiNOnzZgqkU/HXw0kzvM71tUb+AaSKnrtoJSKMEBCrgpLjFOo9MwvTGXsJfzd3voaAOmjZe1RPdM8YvBZl0UWC/omKymRWwm9tkSv8Lj6RIO9OMt25mZJ6xHQGqC3WboD/XX0ENqcvRWsFcytLl0NHmOKBbm4MHMrQ7hqGcZhjW8uem8fHQsAIT1wkl9b3yL11iBOhkpNEpTVvXzMiOvimMWlekYfIXP4ofpUkWG3fbCqjrKAafAs48FpJ8wwpAmLe8AK3MWyCSfUgVSzrSuOIfRzGDMLxBDm717z6D0u00a4n818n19UjadunQrxY+B1WxEc/grfLSJ62SAbqjeiTrv+SityQDAOQi/TWDuIJ8Gbx7105q1trar6Ax5KXb+E3EQUFs8I4Av3A8h7lPKKxzis738WpjSk8vtkj8OHd+A9BSdaGVzRi5Cou8igpS/NReyo1iD/VyPclmkQMbEU66qcbKELWH9FLeNmGCOt5aGHDlwtdEABzOBOY1unFR+PuTI3JKDUh6/1eRntXLTLUVM/d1KAKouURR9hpW9/gCLZsDG0H2VWcAdB9jMDy2QQv+OpQp0RAE0RhS9EFYopHa2dhLfasSTAD2H7T0lHHYyZcuZMIj+qiyjB+NoT0Mg7O6OortRCG9BfO6nGyiPo69MTtfeIOyaPLQTUBQvDf3ZXfA6TmKsFqFt/8XBOgORZSdBro1y1Cw6ZSiNXvVlMnkkf4sMqj5ljGcNSO9NwVGqEJ3c+KahMLLptoqEKsy/tMI8CT5AvQ4SglD5Whif3LkOMU91xhROMUw8XUYfXSCtzuVLJ+inTZRHE0eVTCmDZNefa73pWyXLRamaDFglaS0OukAEMzHOuoD7pqDPvpZq1+y9VWU6zZ4xO3HD5ZtX1q1ekwfD1e+GQNFqshU9JWNh9OBFNfDMCV8OG7/FJQ+G0Ec0ZBa2WUsECxTig/jIzEb9KzD8PHrNfLxSwhInWEqK8vXoR8Nqmtv5lQLKGjkF6GQgwQMEC22Oa2A77kw8ekVHGiCwAV2D9tGs5j6CNX2f8IMx6PLaDA0lArmXaSwbpukNFLKZWEO2PlkB6BH3vXUwqmH4H7d6m4TE5uYyzgmvqKYomm6Gy5xlkZwdKE3PRw6X2czQ7lbqqVaX0ciD4qqglK0uFozdxPq6qJnK/x0wqaADsX7xchfySt/VEE+rWlUbLVGjqXA6Q32cq5CfW+YZ2Kn11DmEEpRb3vsfalowmGDmJo5ZCdhfM5kzj8IDuPSvVWd5XJxvkoyTy/5Vj464sJma2Uk7FCUzCooCALLKGkGe1f5e9hNBF8vgPRoPt50ryvi0AJ6NXObEweGQiC2pLc+ojS5uAZsXJagSCnI0iI4hF379t4OPIlCfW+67hYf7KKODz4DwPH1smanRihDWu/dwMsMAj0fWecJI65XQHWZAeAq6cYYYGr8lPb102IMOdqA0ef+Ga1fpuSk4PzZbx8H585atbKcTFmzhZCbTMwaUmtH90xX/wiQfBAE2fbGLRBbOhtTWcDtn3mTT8I40F4Qj4PxjmcRBaijOGnE5H2DtvUb/JA2oVFBNM6Xa7HxymhHI3JsVUS1aNKhvHV92NmGwKxiLPmjmH7fQ0OxVDUxdkFJWPcpnxdding55tSjMyvLY9IB9trQ1XoXFKykIPO99ynV07nPAfqtV+v4kh/0UDCp160Gri2t9lkkOwdzS/eZmcyxgJiewtKd1A+OL9eV0pfed4zeFo7tW0Mu0FGMwoVJL/FKaM/NvU6phLnOir7gKkUzFaDCc7SXKgs0rDLxDug2U5chUsMavb50oBi3srCH1isWe6ZzsPbwPCcwsRG1ousQ7AUrDstr6xmG3VCY3PoqhxDiVMGKmR+7eglj65UXA0R/pxPx5iI/21wrcq+PHp+FzLd47KnGwDM2F8iJgf43wxEzdBruX4q7fm+r8/+PKKFg4GshQ7eRD/21PClmPEnHSeONaMCgY78aFz4d+2b5b/BdGN2iCGm6FvGUd+q61T3y9uHKicLZPdTWWwH4d8i9zTHVVsr0OJveWCl16BiYHO+kIy5mldsopoMB8SK8Tn79i4IowTSFYBzb3md64Nq1c/DclcrKxDcza67ED2HNPG0mP+IlmT3ZRMPSJbsR5dZ2oum/bZxZdTSvT8E9/GexZRbx0UTDIz3VtBzWvVq1rZ8KFtIsMgTMTqu8LCaWRrN5qjNzyZcNDATg9ZW5H2MMuZ4av/YiS5dAO5/zxMZVCHh+tKczNNM0Xjhji5xNxVlfpMUSROjkT3FWxpnY2S7PK8HZiYxLejS0OcwZTd2Y9utCO+5TJnIQJsIgtCg3wRpAifum20Sq67Rrix7I7a2AjukkAOliqf6rd6nmTR/Pqtjr75ZXOhhBfWUT0q0b67Ra4mxyMSC8tb8BvvtMtro1ArhPnU9Riekc7RC6xh3IeVMT2TQQmEuMFkRn49mRMHEFS3ynHeDwfSFaM9jjq3iTzBY2YKdJmYV07t6qCtuHzXZklt3iABLmT3hQeUOINe7jvjbjTHDwhfDZVy1NY2SFRbJgpmXbGjj0KtLOtzG2EDl3X2K6o70QMrvyekdQRMThOycLHy+xNpqsWl1igHaBqRdtOvsCu4ZunQy2kENtJZOWJ6QEJTAPR9sH6oqPEyul/M/9JdMkOfCK6OEGcV5i4X4Y5Ao09HGIUxoOvhz3XT7FyitzXOR1GMq6rGq+v3bhfXfcaLwM="}]}]}, {"codeGareDepart": "206", "ordre": 8, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T09:50:00+01:00", "dateTimeArrivee": "2025-05-15T10:02:00+01:00", "durationTrajet": "00:12:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 724, "numeroCommercial": "A13", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:50:00+01:00", "dateHeureArrivee": "2025-05-15T10:02:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:12:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 9, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T10:30:00+01:00", "dateTimeArrivee": "2025-05-15T10:42:00+01:00", "durationTrajet": "00:12:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1556, "numeroCommercial": "805", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:30:00+01:00", "dateHeureArrivee": "2025-05-15T10:42:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:12:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 10, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T10:40:00+01:00", "dateTimeArrivee": "2025-05-15T10:51:00+01:00", "durationTrajet": "00:11:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 733, "numeroCommercial": "655", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:40:00+01:00", "dateHeureArrivee": "2025-05-15T10:51:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:11:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 11, "codeGareArrivee": "200", "dateTimeDepart": "2025-05-15T10:50:00+01:00", "dateTimeArrivee": "2025-05-15T11:02:00+01:00", "durationTrajet": "00:12:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 719, "numeroCommercial": "A15", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:50:00+01:00", "dateHeureArrivee": "2025-05-15T11:02:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "200", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:12:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "CASA PORT", "to": "CASA VOYAGEURS", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}