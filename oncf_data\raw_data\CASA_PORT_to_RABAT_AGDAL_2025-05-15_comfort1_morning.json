{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "206", "ordre": 1, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T06:20:00+01:00", "dateTimeArrivee": "2025-05-15T07:20:00+01:00", "durationTrajet": "01:00:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1, "numeroCommercial": "1", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:20:00+01:00", "dateHeureArrivee": "2025-05-15T07:20:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:00:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 2, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T07:10:00+01:00", "dateTimeArrivee": "2025-05-15T08:10:00+01:00", "durationTrajet": "01:00:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 703, "numeroCommercial": "3", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:10:00+01:00", "dateHeureArrivee": "2025-05-15T08:10:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:00:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 3, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T07:35:00+01:00", "dateTimeArrivee": "2025-05-15T08:31:00+01:00", "durationTrajet": "00:56:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 650, "numeroCommercial": "5", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:35:00+01:00", "dateHeureArrivee": "2025-05-15T08:31:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:56:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 4, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T08:10:00+01:00", "dateTimeArrivee": "2025-05-15T09:10:00+01:00", "durationTrajet": "01:00:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 693, "numeroCommercial": "7", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:10:00+01:00", "dateHeureArrivee": "2025-05-15T09:10:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:00:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 5, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T08:35:00+01:00", "dateTimeArrivee": "2025-05-15T09:29:00+01:00", "durationTrajet": "00:54:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 644, "numeroCommercial": "9", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:35:00+01:00", "dateHeureArrivee": "2025-05-15T09:29:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:54:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 6, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T09:10:00+01:00", "dateTimeArrivee": "2025-05-15T10:10:00+01:00", "durationTrajet": "01:00:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 667, "numeroCommercial": "11", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:10:00+01:00", "dateHeureArrivee": "2025-05-15T10:10:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:00:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsKwmfa6tJ2w8Zpi/SOr8NxLIVSM8wdjbRs+38BalJFA6ZoiOdkuN1IY3MMRQnPz1kWgRrf2a/K3PtmrZ37LsvsLVSXMEn7og+Xe1ZdUXQlGzHytVM4dxaihuz80im+hZki4NSu409/7lOhcQ5gorLNbI6otx5HMwtz88h86CZw9taYWvIeChnuamTEQ1co2TYCHIGMNaXCY0qherOLQOlj5dPAOA7qhdxtYaCgoyLw+R4dM7/pZzZlCUStFwrgbi7nRIieD0ez4oeoEuFAA3yDWtpyqO0uELKD3TyAgojnsnqX6NwAFQU0s3YNgf7RFcvp+UVCtx9ONqeLY4QkR4yLv2hP/zufsi32sgTd3FZXuSbisSPy2awrXS/aePpe+EaCcVHXfI7KNx0d0uGREaNe4+s5e28QtKhUYInq+tHrlxGqelanppQBnlF1wO5E8WdEi9LG2ezHARDyMOoflzYIiAy6N8DXhU2DzQ2mPHtGJO5zXjOzxMtjllaAQPfMZtrd4Z3ii5Ok6WW2Gz7AxJoJnKGPXySMa4/qCytDiDVBRZZb4n/8bXLftdmdz80YvQbgd8XgFUNz/iYs3yjFxjNwtzmyStuBgSMF6hEftDFgmkUK+neqFlOWFuwVQh/CiikGs6GQVVsAnrj4TfYb3sMCzY8ZqEyffSLEaqAIq3VcnIdrIETe0+TEgpDghrwOXwPlzAIxpPwuEDGFN5D734UEEZ8vg90M7Bv2XYS2DmTX76vYwd1JZRR6eT5hfvONoqspvU71L6VaM19JoRav77QSJB2lQKpVPsdbJh107pjCPbhsulKzHcWk6CqmFxXcPdWEObjL5DBeOEpcreCKWkrhQ01OGk7iOQlvCunM90fQop/p0NVJNNbJodx1kN78sgQsc86f7jMimr30Cj9jbJLQlxD+38l92QIYf02LuYlFrsUa2TpROfvcrln3TrvckUYFbxmbAt7F371hLg9d+iFjVREc9x6WxR19ATJsRmSHF63iAySF9It6uL74zdehLf0d0aZ27uOcDAXvTEk+4uwaNDGSeMZmhKZrh0bjM5vKCbRRVuwvVigoDjDS+bzFVRv4wCupliB+srRxiBxERlF3vaFukTRw/tpO9pDbcAZU1FJ8iza11XMHbtSLXQzkGP0895kkc/UCWTXGpSqSeUyEr1EIHnsm0YozBDH+W+pSZQWStlivAn+t+tUFxLQKLoXE1uChvql+McChtlG3VJOHoudAnG32e/F/CFF/+bXWYwopkc8BjBXZXHyzDbjJgAcEu1sW9iEMed2H3LY+1AbyHIhTMpMRi0mH3qF05RKZt1plvkBBHfPNQ5x/M6SxtwkNw2knJbpTmOCWdnis/keNZVPWDhdMbczotGjNWBuvJkfstJozerPhGR1WSj08UyZRJDzY7qtwYLGgE4CjMcIXJuZnxB21Rks+ZAjUwt78NdwsTprQMdL4PUzK7Ocefh/wgPZe4eMag/7RHJDYlCg1/oEmLcXy7yeyqgwlGEX5joBqlPAkknJ2L3nCUq6EFAeDDyUW32gmh4Hyrd+0P1bGc0ke+ufir1ZmTYHyrq5CuZJbMDSfwvPt1389Velv63fFFUq/3XwqA/GgTKNDvBgfNecDu8xMBYOIqVvd0CN3pp1q4nexSjnoR05q5V4N6O/n3j7hmxDHEx7qLBPnXcg/L7LjYDPMroejZZO6paR56nN80iGULfeJwKZW4XWpGeolEijDqgVvFSqgKP+5VqwE7/73mm08WQCdAPmp+JZiKikYowvKgxFyRu555NrI3hjy6zG8hNVkOLuCgGWY7IPIxZ7Jub+hszgid6fPG2vpyng9kwnDP1O/izOhqM/e+FLuGJX95XGUv467gXPZ6zcbfLHumXXQt3PIRmTVcKUnTRegGiePpyNnHAZVNfehzkKda5ViPE99zN31fZmUCkqHFcCgohwt65aHwmPR8+7qClWZkgTlP6APQ2mXd5W7q1O5FDwsnhUtzwqy6EAIhWuXOR68Lvm4b3sct80w7VDe9RAjuSKvZs0r2JzYgzcBkhY2xAS5N/GBs30diuspj9XoWL0bdY+xgQkG3JjHe1DnHA+qsg5gHbmKn2kdjv85aYWSdWqKWZvjHvxytGaXLoqoaVM+WFNMY1WOQLvSsFKjTCaBhmfXX7XMztKeJ0y3RS2HtsNSVANKyRx+I4ditLxc4Blbw7pX3HSq2SXqIKMCQYTnsMh+mzA/AuvAIGPmt91pk6jMsuyJ6Ih6E+3tIDzvkSzJUxZ+fa5DfmSc+XCtoQnKBhFlAQWZkGks4dK/mbbh1fdidaTJXXEuz6JpHWrnObAvg3dX2RdJY3U9jUQpZDNkVsBBN9KoVFbs5oH8WHRU+v0Rz+gvj/DUbbgprXqrnqOhBbwA6s9xPlg0FEqz+tWE4hvg4r2fiA1ZzvS5CQl+7tRUuO6WkL90yZZ7YeVZqlBxCp3bW0kgOcz8/W/GRtcSGd2pjDNT+0FISaXKBcLQE1tZEKES6BTgVdUHG4mFhypvh4pcwks9aItSCujRBLGaw1BoayOH/7NduayGOsByDUdi60Oy78T4XfJCgFUnNFpGnilu3LbztwinqCZZNRnjavH0Wqgf0j96N5FeN+hHwEquPLJVrQypTo3lhSfHx1Xnl0GoIzJ3vpFWdDp4p9lSVOBfNfbTeKdWK6XI1/+kehpFrinL2GCtU85aZM4PeGR1Cl69PzDDTjKLviyXfWraDPcud6f/hHfZsNmql2B7z7OSfD4nJAd4jH63Qysbg+kGL98t1PjDdB0dLvbQwb53psiyAhqOa5aLucyz87xtAkYDFKAuN7oLS1peHkcQ5bbI70emxW9uO84uPstO5juQ5DpK/UvUikGlC919SuuikINbNqjmpgvdpslS05yQzjvVnFyGJR7/PF8bnYZiBdGr6H7bfoctiT6JqGzwvIa3bzD+WOJTQ97FvzGBF6okK2jI9A9RSRwCeW/K54zJV5AQ70srvignQQytmxa3SpJfqftb/8/MKaQPFGgoyVNsHg0xeWyb7r/y5ecWB0ylcuFjO3nP2Mw6mYEotDydISwdsh3gIUV4oLTx/IE6DUUX46atfBnS9Wr7Wl5lqcmhhD4v2ftfRCRPm1+PGByE6SWd3tVa6tyZrOPFmx4utuWNnJVT5x1LcEp1wmm2NaZ/3/E7TFvIlRb+3K1NyKs2I72cvOhYQohzANW6FtzYnwSJPLuKgU/ixcTPRjcbClEJ8H1goiVLetQcVOiwuo68koX/pUUr272j+1YAiJrQVZ5BQntHKZv6Gp1uKI9jYCAi4RGQkq0wgAE/cr6ELDuo5CRaQkGyhbdkG7FgA65GZ16ZfRc46ZfLX6vJAglq41KnOdRltk741gtmdgmnn3lX6SSQTjMZe5OCdJ0kC7Ba0M6ke37jrRDy3qIyAWqie/2eiK+dpqgJNWA4sDn6nyptnCbw3ohDmMiGS6BzaZzRzsOPXI6nKVRymobBR/SG1iYtypjKkwQ9wcIXyDG8lChS/17M4L/Pk35jiabM034RN6nZIJJ481N1o0EYfNq4e6X/pt+GiM6kHu6zjm8+Ank0uwQWg3OyJqGp+XBNhRpidq8ALSvi20gf0eSM7GXPh94blR2FL33xDXYolCOi5dsSk3p3Jpu4P55yjsig36H7uTDZuFXddGVV9+jDYrKhv50OyfGJfHIxbB1+kru7h4sTyj5gW705Kg43BTf0bnaueOMXV9xn5Y1HZ05Ry1i0mNS/8WyNaTfpPSsl6ZywzS+vkG/jteSvpgmVoklqsjVQpZCmtm0QrScturv09P2BybastRIn2/RJySNS2H4HK8Ty9STV3U2uaL4esoUoAzh2JyzytrU8j4jn8+mTvW8/uTsnMgJO21OdrxJvkuCpjRd/2OhFkLsFPcK6i+E8YiMJXRv5mC7+OfYVmugQWl+fOFZKn99ML1GPwL0bEBT7kzsP3fuBRC0dUDhTeKp4dqvULSXQjZdAuOimvzbwU5D+9rhlhGVW9GkQyfOEI3UxgTzoTCljmWSX1dCcw/iJcjYbdtUUo71N5+mtYNViMI7jfykaVqnAbmJhibYwv37ktAR1u7UNjiDmGQtwm2A25Si4yC00/xTjnCEZZldasyN/3g2xkRVtwFpOIfTkHonGQ+tlmxBgKPAZSZhRHFXYJ9SwW4CWW+ptnoJSmyS28DGF3RIXJC8nmIAKOXIXUPb85DqNaGg5J9Xo22SePkJMZaaH1l3F9RCQY2obY3CZ8+kclnc0baebk17mUEKkgOc979xbe8grZC3CFZMBL4rmexbE8r9mZvVK3GjnOzvPfWkHKkryN4L2Xu7vlvre8/Tca3sx1aXqUprb8gSmwuebEuy1qxDJ5/XUnkVpuZEKgz1XYAxFY7GWc9jzK7gpOa54wG5JZfTC3LxTTzUqfHQeq9LYOBVzJqM+rNzqpoTjJuUNTPbhE5DdsyRFTgQ5L+GhnbxDZQ8wQqj3QDYIUgupFQ7rVwMSBuBNr4EAG7L8k6XOFLtDyn5LDOQJm1XUzuQi9wQyX5SUM0CylZxWvX76zW0cZ8ouGdxpIfKWBoViYSFyyspGoo2EtwyCXAhjMj9bfOJlj0CM7Cs77mAQeBA6hNxChm/zYEuAIcpW1Rp5p3mBMqukkj5Tne7g3QOTvkYIfusKB1rRCgjCbnA2IOLz7sjI090CBIiZn16GlX/w6yJYcA4ivlCWvvvnX1OlRqwenAjwk/JJjcchf+64dYOekKu+K0OtRW7lJaT31GbpO56iyETa0WsosQ67/IYvNA2d3ZFyYT0YMtw0aloGR9m+erdTQnTeymxxNdFxSG7pbV06oYaMF5ahHzkI6lMY8QdNnKaCakNJ6UsYZ44xiqodmY4c67Ip7ugkjRF7qF81J7gB9wVPrs3Nsa8JnwKy9g5xzqP7O/pAseyY6pIucNYA2iZYRF2g7LKL/VBQ5v9JgTfJxTASy932Srbd+OydE9Tdi82pOXN5D+KJjY77vOwW/5kVTgd+S4iJZhJEFSfkVg1hsDlNLDXLPkXWC7nSoyzYC3/0QW9LdivxXFfFGOH85dFUWbo4rPt2nWXsUd/Mn/h80096grO13InY1IdyMcfjivJL+dD5DTAUhgNnlp5OFrtCzERlwzN7KQq/w74qrCTgbJL4uMl/TlDNa1ja7eLR/U8BcXGgoey1039sBr/Cq7chITdztCULXFC9JKXEPBHJaJ7AKSl8w2xhBRqaYF0BGR4tqqusbMhprZeKcqF5EolfnK3d9y9+MqA9jOpErgf7PSsX4CShhawCjowXrCKMYTzsurZNbfFJFLM2xn41ZgfLd8XmT1D3LYC80inx+4Ak8oYv5yiSSQetRvmONWlL+aMvkacjXzOpBgumHkjYhBZACnb8SS9PDa60ODAIWHmaYHyVhzrj0iR/0uezJ0l7prK9SXpv4EMGfSUEM95yDu8S8gdu4VjGykdLUE5KJBdM30pZ/UT60PGhHBVkFhn8GIkb6qkmBay9wYarQxojdzauRPOnQqs+FMe6B5llsS41aEaxUTaaB93K9zK5eMg2xeHYUm0XMtZ4q2GeJdmIyxxTACrTinrX9atnx322x8pBUCG8z6Rxy4xMnbOF7Aouh9Bi5TRmBcMBXNHUfBO9QfgI4hlhgWjleu9TVp3TmW0pmktI5aePkrGCB3yO50RWyYWHVgHTB7N1YEjtsy2xAL1cnAu6ysXJQBuOy1vnXirMfJdHZk6yE1L4AFQVRrNcVWCukYvkr0ns35iamCEj1cZ6HPtrnfiT1X8y4qs9XLkHP9Zk5W51RXCuHMrUTDGinm+/1GbB9FNmSoM0v1B4X1aO387pErlApBCIwG9zgXACquc52d+rgzabTzHpC/vgphhqOyfEuyEmF8bqKmCK3U3BN5hzrnT4pOfXfeFx5mnVljHxV2b5zVR8rFRZnEYzBxsQeL+/7iGNw0flfscHiTXvpQj0HNkvp/691QrTRO2W+4O5vsLqx677qmaLiqPWKfG6SWElFSKhUMoFcoKAAbY+tx8GfLZgoK+yBGv/g66PsdletTJYeC6z1K/fefIWDQcmgw/0CHkjInCW1TOpAFNQV/PDtL9EFMLkY3ULVKiQ4RL+DKrsQGyReRg5kzFL3Va054Bwg+Syg4Ctw7H3RZMi6DXsynuerQvz3/kLJ5tO8aV/t1TYL3pX77dwJYbm66fnE/ZaDgpV3ZwbDugQh17nEUhnaUlUFUPzpCorPS+LauHEWXk6PHcwQDcaHeenQj5VawOtkVidUD5KlsUUdmt7tm9y5Fnhya1ksVYfqiMSWS2GM2GrQb3qmeStmgmMyvDXV1i/wOhFY/Y8KrobNCpNMxcAPupc9ZI7wcNF3/0A5ztuaZl+16BALpHleK1T0TriWm8/htUUI2ip8e/pk0SFDjX63oFfJdgPcQ6MMJQkyawj7cIog5GacUMpYsD8/xUmlpvtO9/p1evg1CDsWDkh9F42SFcrMXU+lL+qFIVgURSfazxGpZrNMGDnyEBw54nnAFYFkua2uh1uCoZRDxhq4tyfbq5YjfjH7oL2znDYsLM8ELqMN3isA7XX4pmwi6xjf8IujrUoqW/JWP3pQDr3B7aCp6HmZ/ZSl0cTzsLixgjl+8KAWbwBaLcpgPlf6XN1Ljh/q4TaQI/PRb2BCV3e4iDkvrdqnbVo3LwrLXlral8Pq38522gQpeBBwEL8N1Nqn4gaEgf4n78WFbSUTSAv+UpGdlHERgwU+onw8tm/ra9XiOH9vfBHQmgE068YfsPhRxkgT7UdI+AVuH1rKoHks92AARCINYUk5jtYlxU+cocIu6fMbBWhQxmx8ic/s0Am1efJKnT2mE2KTItsw6RqQgosgOJJ5MMuKq2dz8Ww2OFDHwxsKI8QSPYyYcP35iYGX2G3rqSbWpCbe1uRZggzpepHcH5r9GkScNi871DKPbVmMZX4R4x8RO5lpWERBRBmGL+VrAs8rXk2JhsfvGbDB+vK9PFNNIpgFXKyOG5UiezUOoRc+t2ScPhz2yqrJqaqrIUqr+a9S04O16g8SbgZcIXIHGuETgoNF0YZzH4ftR3UxBvTEM4omSPrFthm3tlLjiGfWu9kfrUbJEDUrjycuS2YBqPzqm7q0IhEd8nCCxRPvGwGIwjgiFJALIxZ5hz9ZQXCcJR/ZjHNrWgEAPoQQ2aAcaGuuGoHBehbxcIh5P4U15xtaK7sB1jPFviCmAA6Go+CBv86wJlFFnQZhy4MUH3jRUVAiDdTeXW3jGNWxfU39MbSjuthbJwYDd+G1o+ieNehjrrlnnxEd0baV06GQQCi+rTXxTrDgnBtAOjEHIfwsNmRURs59KJDJ6TyH+mkzkwmYj4dGU8Xond7pzdGyZQjOvbrOHTvnvLtYQQ91/VcGzcOpByMdqlgF8qIeVIqG0C7jKzVPfyXYPkVKQcRFcnKB9Nsp2oeF22094HA4AThGFNAqr2sJAM6Aa9M+byg25V7tT0s/9xFUEjYZ8Q3rmGoQ2G3UF8pAsgPUOrupv5F/QGlJ6LRfUGq+jUQ2LewSiFWwP3aEpuLolBDDJq5oXp0aMgaaQYGE86HYgI1y4HisLJquh2C0KafcaeqD662HeH6TFeVsu0QYXwVLgiSyTRNym2Q3RpuZmv4I+WWc0eIPlhaJ01/i5u03ml2N1x9xQtqcUf6D49+Kvmtw1Fd02a1kS6PoIDrnveniKU+LlnsSax/QpCUsnnQUeDwzRNd1iA9AzrZ296YJwVNERbjGxnm6NiifuOGdE47HAAP0jhATigwlDLwVmudzB0xrqfrOjv7V3iCD5BcKRkyoyZFYAKHRTwh8b/1NtrOWQdhYybH7VR2tjxB2+5lSclvjIEvszFQozFe4fDgbsJSa6AEDXMHntYh2SAaGJCkQRadAgwF1g3NinOwJUKZdPGTSnqbOE2GMhcg01izsNWmA1EyJyTFDjJ9AWWBMtVDxyowbhUymh+nhVvWgtF9rCB6sSgXv+H+JqbLlPD98Er12a4Uc9GflAAi1tAJigCLKE/Imv9EAPixD8zWx6kCfRmP6mlXg94+Ywlfetvzhmjn1QgL2TMupGzE3bR79BrHov+bIvd3cLskQVB3l7XnJ+XojhXgmcUOa0NhuhHQaRdGfyfLcx3rvf82yFWoZv8BH5qgHop49HG8X3VzyyfR+m3HT3SPDePkIivLAyveA1N/HfYQbpStHholCpbR1xnHz58ch/XWrTjgcN179yUfbkKq7VAJK2ROZfNy/VUQm5MMq5RP/3cflFOXhhOrIoPsYj41DvPyBjfe9fcKkFIHH2OroJdPjnyCx1WNd1GCcKZAoDJlGBr+sRtjgoVK+K4t5KJeljufUiO/SX1EwItak+J1dkiMvKUTDaxGR3ZlLbbaSKQuj0R65TOHhXQzIP9JLA0Srm1/KdJhOo1AYWKsKzSTKvosdoxcKtM7Xg5NlY5M0TkRXwnrNeEAxcZ7iaKzXsmzIkFwtJcK5fuZLxQe+WjpixGNr++B36BOuVxQkzjKwBwVGysWqD7sUgsNpvsQPK5cj6ohTgSdT88gWtGm1Pq+xnRsW4es7UfqOGfDgQjllDFMW88oN/2HrO/Fq84yLu5R+wRc9Kse9ZxUhgFnsLpPsWd/ksb2eB7CLR5cacFk8dDUvGQlH8P8Ej/oYADW3LQIhOiab+kZaNoyfYZY1MC78E7mdzXrNQgjfObRaRYQTfgPcxve9w1XOnGvHthdi4caWq+yABJgExCe2B9pwDn4FK/Z0zen3x3FHjOY6E+5jI6RpQj9Vv+r06zhVFWPkKmAzj+sqhZzuzV88iJH3fiwdZVoGRFQzaRApXsTLiVmUFxqirB8uGd+B5Z9Ao0ql18ilqWjXe35e7K1CW/ywDD+1DF3sFURVARL5j1U8/NkLWdmPweMxmHTTSzSO8iNmrVm5fivypHmMHkB0HFIBY0FmdWGnVJCECnwA3iZ6yxuK3YxmHmRDN2t4I7gz62ZbF6I7/zgAWYfxVw6kmwWoHQXM46sGy9zidUkRT/LrAmnXPnZMc+0tbq+sBoJrPR1r35Kqb7CpPebkGAYLDL5dLfnX3kruE4jg+hyfy460/oUVxNGdbWEC99FFyPu46dSKu4jXqa36WCd5pdtX6uoo4ksR/r+oFnyNkgnP+5Bu0qj63zbr/aG0kBy3ZLBUNQo1kXslyt1xZoxgH53jS/5nszuQ+v5CTeg/V1yX79vO8TyfV3DYHoSXojZtGjXzn9hTwg5LnpG7H1tpSUqY/EIE/OXulazbyF03TCEreJASQpBJk6/fRZbbHiyJ7ma1ZRLshCuQGa9wB9MLg3YkSaqoDQmPveEyferd3CTTt6KPNXYRFwmT0YJw1BIwqsmyOySqW5U3Kr43/Bj2eVr7PbrSKCPCUgebSPALTGFDlCPbrUbLsgWvxFCagIsMjHOwrhD8VLM3pulScLDSaW0VFxZfO/AF7KGIriNNKVHnY4+9IJe5KyCa4Wx6o+vrPFRx9A3hHE5MsCKbzg3mEgSUAjIk32nyf4SFDzQKR3Mco7NnGDT6gSq8pLfMNuMYlmJ+l5+JwKosp+sQkL3YEFtIXUzJOoppwn7VQ+Dqv/AUlVGUQQkVXYizQ9VA5zZhYuT9iZhMMj/y23/VfgMmYlcYzCmoUR1IvVUoJIF822UOZAytnywLEtHWfBP9rbkYVXgCrHAmOEK8KzJWAWm7OmdyGR3di6BozMTBdf9xFqAN8EsBtRUn8h552jM/79XSToVJ7FIxGxqRUyzJ9/L0a6UjBN1plB1bGOnXJcql4h5amxIJf9gsDOlXm9ANiGTjJn+8Hxg6sTpPhre4JjXP1FEOWqR6rTnShZGbqQlYtOyBEIUc/LzW0+4jsu/aPeXG2VbvWcRdyCWLv+IEkd/RfAjopV0i3rMVIuwumIRVU7ze/P7AEjUdbN2wOSaGIJTQlMaY+e4t6ecRWOZCMZ9rpRwNKWwcSoyPT6UzdAfs8HAX2ipTmzQBSd7iwhGP3kYYEQHvESHbesma9P8gY="}]}]}, {"codeGareDepart": "206", "ordre": 7, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T09:40:00+01:00", "dateTimeArrivee": "2025-05-15T10:37:00+01:00", "durationTrajet": "00:57:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 6458, "numeroCommercial": "V10009", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:40:00+01:00", "dateHeureArrivee": "2025-05-15T10:37:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:57:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsKwmfa6tJ2w8Zpi/SOr8NxLIVSM8wdjbRs+38BalJFA6ZoiOdkuN1IY3MMRQnPz1kWgRrf2a/K3PtmrZ37LsvsLVSXMEn7og+Xe1ZdUXQlGzHytVM4dxaihuz80im+hZki4NSu409/7lOhcQ5gorLNbI6otx5HMwtz88h86CZw9taYWvIeChnuamTEQ1co2TYCHIGMNaXCY0qherOLQOlj5dPAOA7qhdxtYaCgoyLw+R4dM7/pZzZlCUStFwrgbi7nRIieD0ez4oeoEuFAA3yDWtpyqO0uELKD3TyAgojnsnqX6NwAFQU0s3YNgf7RFcvp+UVCtx9ONqeLY4QkR4yLv2hP/zufsi32sgTd3FZXuSbisSPy2awrXS/aePpe+EaCcVHXfI7KNx0d0uGREaNe4+s5e28QtKhUYInq+tHrlxGqelanppQBnlF1wO5E8WdEi9LG2ezHARDyMOoflzYIiAy6N8DXhU2DzQ2mPHtGJO5zXjOzxMtjllaAQPfMZtrd4Z3ii5Ok6WW2Gz7AxJoJnKGPXySMa4/qCytDiDVBRZZb4n/8bXLftdmdz80YvQbgd8XgFUNz/iYs3yjFxjNwtzmyStuBgSMF6hEftDFgmkUK+neqFlOWFuwVQh/CiikGs6GQVVsAnrj4TfYb3sMCzY8ZqEyffSLEaqAIq3VcnIdrIETe0+TEgpDghrwOXwPneGdAtxsrZVOEe1HVjd99/OAVtyEG5anl/vr87Kjh0n+CY3V+1wKM80ztkCIZoXyFhPhoBXM1WJU5+tlPRAylJsoiqX86dWSZAUuSp3sc1vSJgT4fVcgU+pweiIHFXqi6BWvojaxc4EG3d0jR5FgHn6tb9/JgVWRjRvVngiu5gIMbSJmRJROlvZvlFqWGJaL1x3udMaO2xMusoz0gEUzsiXonDCUFBRcavN00zTQYIQUDAwQrWWIe8/RRDRe93zOKodAanmMOlDrukMriDPqChyEoQ/LR6r3HN5O6IC0Yd3sX4lVIi89uV6SpQ6UHfbxypqHQRZeNdSPpqntD6U73qpXzJ94ayQP6+kCos1UZNaKb2ILhmfAx6cSAgKai/Di8XeyXRrdPtEkKqGN0dYkdfVoy8XDgjEpfTXheRPsoJVanO5zOVvntEUjCHLpWEoQVLy1zCTraHQFN8khxEYtTBm3RUQWYunU0hYAE6j6P71aJjBecLqVyf76/9BKCwzZ98/jSAHML7mEM2CNJgjHNaAXBROMusxNub9NUsBJJjhs+ZH9sHDN/0hm00JoXvswvzNn406BnNc7zylP1Gk8l+a786x+rXcGcAB3v5SzA5r/yE9LUh6lBbxJ+XLL4h6cOa5jBFLvktwud+pHLzLKLM5vCdeqIK8mrjB0VwuF/tl4Az5dKj/pY8e7dI8rTucro19ndR3h6woIBvnRqEMR1kJC55VASEhYVxmR19Ipq/f06qaU/ljXJ54VTfRWxaJWKO+Yp6RHA31dYlq89eSC6NNYrlyv1F8nnPD6jErGS6u82dllcvi0hILLIC5HgF9ngznKjCIRU1S8IZPzUNr1yqA21jbxTt91evFwM43Pht/AQl+hYFsDTAyv6dSqNvic7xVLUuNxjfl1ur/jEaKQPA4ckXJO0KICPaoKksW3X2KRu7B3sJlFTrCTeLjJKwwoHDAULlsLlKjSHbOGSLmfOmGJOHw+HMhjp97GXVw9peBhqAc5MNuGJHi9E5x40DD6qzpUHlGFgt2CfQHNLYtV/i+VFwXbd1PtCdmr/n7eEQLss8OZ3awy3nO9IBYmyiT6goTA28jTDCuts2odcGsTHC+wZ5m/BrYKxR2nFzJmw7rOWOkrodo2GHeKjETnpm5v5rbdfGl4ejkmCtxz70wN57cCizy6K3FlqBeyeatPLc+TKR2eA2H16OWF+gCfmGQHuMfCXI4sY1EaJF3znLW2mpcfbJa+RuRCvKCDIRU6GW4xhYT07t7dZVGKVz6caK6VFsspHO8tKp43bSB3avN/afbXv9+C3lJuBg/fMIiNnOafuyFpyPVwu01+ulIIAyCHuSqazBrP+Vc4q2phNLCZjJ9ARIkFJx2w/TbXoGquPEom1MppODcJnzoWEs91p4IpaPPHVktoRY56mA8uvONEIgJx7jGxe3dhWmXNYbRDAb8WWITQP0jSIr99sEgjUPARo386CJu3g33hGW73is6bQoVZ5GYV+JsCBTbhkJ96BWPC+DH1HOi3FcTVF5vKCX8V2xSMhel0hVZJfU2hEwNA56KuGlQ31g7BKFWcfrC8p/fsxXjYQWvMZEPTirzBhPbriuXBheji1sEuyeCMfKUbedZsfHDElsxBp0A4oHo8I3jxpUgLJ09HW1TYbqw1L/fVRhf+blRgmHPrAW7HhbM0y7C+0l+/OPR4PAdpMImTEAkGJ3hnqIP7vrwrJHrIEIN3YtJ8KAQ/ZyoJBZoCcDzWjq9pNws+HxpW4wmT1/HARaEPJmXXgm1botm+UhVKDA+XuRPwEQKGgQvF0otv4bJGZnaWvIRwM5WHNGBGhYOdzTjMUUQfb0Dr2kdtYqdmQwK7J6V/KfuJizkUKU8gXea1xfZxhRL9LOBPC4UyQUuHMLCosDD+x3RzSmjxQLrJW2NM1EtSNHrgUrWYwL/ieSEB4txGkIKQ40eNHvCsF4KIZvqfbPXGz/e1w9YcdpqnhFAHbk5K6MD+CAEMv77FiKVmYWrtsge6Rr1pq03VQE/5DGhPkamTTkQz3bbvU++H6gGRofjBlihVTqeEaHz2mfZNqjlPY8mJFyZ7ZUmJgPhd3XeJCf8KJIDP3aL54dYrAxNO69a6vRqPgLcytNCQ29QSh0CWKJxdHBHILt9E2C64Qkw5MZCU+ldjh3JWzptd0H+kKxeVpOc2ADvLx6IpvvRJVhi8sqLh7MxgCGWuWbRx3209AtlukXNLtvyB28MrA9NNsVP2/wDuPX/NxSlH+Y+XfjJYV6TKTErmzOTjLhxdE/eQZPu2Art76KEYcCZkDL07/sPD1F9sIM040uH/4syxPOIIZ326lx4i36fzp7f6kha6lk7skiNURP1QTIzmyFbqKNpL35hj7g+WGx7zXFVP1IcuaXS9KtXiGNntV8g4/pUVi+F33wQr61InVru3GLDWBKSFAMXHYCORp7Zbpo6gE7ulFWNOHhGh3Ed/NPFVCgXjAsB0qt9eyUelbgkQhuwi6ydyo/1cSuPvgONTYZIuSx9iMfwfNrJ9IAFa8TBuZ7EPf5O4k6OmJLuaQogJVbd/RdY33n8DU4GweOKOXKSIANnJNCvjhW/BM7y8FQ6f3yiRJfRzi2jY8ktkpq/g3R1VOwhPI2w/6oc8tnzD9E5RSamfMWxaIBdGsHYXS4+RPkJ62zXwH3Gkw41lo68TC7hDZtLDtqfW2DorW9lqsVoD6XqTcxlZKkmfo/NzeBJ5UK1U5x9vhhYkAlb7fbApotu6UX3IDS77KHFUYW1aIL2mD74UnbcBPnSV+Iqf375/70sn5QSN51s/lm78Tn2BZ+88GsY+jTTB5xI8vGEhgsibhOia/9Koe32K5hRFsbOocJ9ErYiksKWUfugjyGhWtYpCR7BGI3U0rYFS+wsZKq4Yo4TAUqgy/2z0lQhXxJsh8o9Z8NlVx8r0zdvDTKmTu5Yo9JANHarBV8464ReJ3YP220C5GyHHGZkJkbNybqyJxy6XZRo5hUw+K3IcDf5qK901jGgAMTiCakzoLp9D4e1mDExxfld/NNSUUXj4pbJhz/7v6mXg/qO3CAW4S3S03LsHPbdVWBdmg0dl8TamrYyqJur78p8tq9xVysCzHgGcOtxRfyzNd0ardhWLYBsY5amSPLYnstZZhdZ1PdhtUKfZWK/cw+Ls0pfaS+1evYpitMS1sB4QFSwq6+6NCchl26Fj02ak1imY736nZoK03dvUokhSf/3qfsWb40jD8FNXarXV8YrJGncMHmV8Y/fJe9eh3+VedS5AsnXjcwTnR4chGiYXFVNwh23SJ9FCbsySzu+g3Wm21+JAh70LoQ4JRVoOXBPiAziuo/euBVQaYenHhtTi2tIpEEMauverYAAsZqmdp1WHHMGWMcel6Dy8FRA1S0mE2qNLAUryPo7bngk/ZavRMCWPAyUMpR+hekyBYxAcjgUoWppW6dRQGuw+W6NNXEFZibUFdBwBq9tj1AnZA34+y5J56+jK3+VdOmEZAyBAAI/XlGx1z5gYqNrFUXV0wKF5AKn3BpIbtVKYOkUJ0wQZyB8VAtXPqXkM4x9p9QlcyoSr4eQWMJbiAdK2o/xW0zHjeOZGtIc43Geq/XZ6Wgzx3JD7wkbPRF3mIRzEzMbztpVeO3CFhs3AycWuVfZDnC5xlqd+8m/+bD+rP1QOO9tJjEfRtNzxz1De026krhWL48zeTP4SeK41pPLPzIWVI/6gFtW3wuyspVXRltONT/N6u6ATKF3CxvfqNxxrUY80lfgbD4+QKn+SM61Dkr8R2Gv+CrS9Q7K7/qxVRX3sPcRFq+hcc/fWADg4I44lejinXZnI2QWSh8MoJQs0XMnAbhfh9YgE8+cmhCJVdnoIRNMGfiSyhSL2/HYwE3kF8RuT7WQ8Y1gvKfCD0wg5geeTEcFvb1H/whzv0fzuJfxq9k33QE29wlbEXse9G0HLvOSIf5Q2NJu1TU/rjWzz1MyVh/nu1NS+F5SsGIe2QwW1UKykBDr5w1wRY3W+iG9M6S8nwjHNyfHTQ2J70Yn+tvnlsy8fZd44tW6qvb1x+iRdABAJEoymelQcdogIGvwifGh7NSEsIzSABN3a1OWRy2mHQKSE8POTKre026V/uOgqdz5mkq85E6kkMAJYXCtwE1r+4J8gFf/FYKTPUPfEH9cqmMobmro1duCwn7tf12oJne1OUD9U+88xZmJ9bK5ntKO9vM0vG/x0ZUf6h5FXFTlsT4jEAks6O3m0UQ70Nlty2R0iQ8qxMWaWcUG2EgX21BIp4owhZFcxGVs5LMXnAbFvMZQd0kJw0w5/Q8NXUv2LT5E+ukbV9BkkeOke5LeOA2t/Tf2UMNlaJscPSH9RmmnuSTzLuYZ2ikIgUUe2vW7u56BKD1iECE39OqfmUKsRy1JomwQx4CbdcOQIV49nTIg1seXcBhrbdtcdNmWNymlYV3IXUBvATWdvA2Wwx5WwWUgPbBjfC8EYxoAKLnQoT6NXabA13JhlmR6Z2MKId42cc8yw9/RVgfBLOQ6h77+jkmk5qGeWfHnFylFX3A7Bn5/CDbr1Nv/OiSxWP1aoVuAvCwZqqXk9WZgBx3OLBlwlIJIEPl1bGv/FT0pL2kUhPtCtPrVT3TroDFkpBBOIiJosL1csQZ9AzC/nsaxza1/7rW25APq3eibN31k5DouWB4Qm8SsvRMWQWtT+svpxu31CeziyjYZcNvwygeUFCPXcmrqgT4L96dDrfCjgY3Uadvb4p+K8mCxKX5jD+6NbzSJ7wI46NBJNtxGiIW+2mYAP2J8XiaMyrlQDiPgZHinlWAPk6R1/E0KbYs5mV0OlnEwzvKYjNF4pWsi1kSeSPUczxb1wn0FPPvHMXdbxhaCaBoLq4BzGn90YZXTmnLUEzkughc7fHiFkVfYQCBS0u6Soy0SCGTmtIE/f8eTu5DqrcQjg/dcZ6Ob+WBXwtpZ2gxjiVAVcO2E7BJW4nMBQcLa4jHM5QMS83zX1IZQzbohzm7e9emi4ZtqHFJgDlAC2qBoO2d5brPalFUE64VbKzBK0CF45oWSho7Fpi+D+vOKwk0VXM/TKaLIZlkqSJb5POOZS/U6aldncP1MA5j/pbKOmDnLl06tb5nBo4xXzM1zXfdF8tKEq2DFgKiNdu1r4oWYJ8iPS9Xza+eR/jqRHDB+8ejX89X260cGmb8QznEQbFQ1pHPTN+JPpYD8IaTiJUfk3ZcNXt8iIQT1b+zLcl5zDg5CDU0AYx4ZpNrSlG8UuFcLBPXePSoxAVq1//Nzups6O0DpKgQ5mS3915cF664/BEHOMocjOJ0/767QYlSbuJm3Llpdkqh/nComYzbMneIi4ObBxV5A6yPyLpXih/LTn+4PXRHGXGWLaiWTNEYitxSVpaZq02r6+UoRsORWwLHQeT2tEDUX0rTc9XEOBsAARhrA1zy4LH4Or28ClAc2q0QHj20iBxZohlWAyqwFIK3bjqSWK0MUvUUdNp67eVSiHpywr1zuoPoDa10OmRAMBuRymvgLroDgcDyxmcglPjXGnd04Hbt5hI9UVpXwtaUpexcwEl4P3ZAAjUR8K6AYSXIzXtq/PocacFaDpsYgqlDTs2QdwPLjacnLdJsEWpeaSQNwHz3N/pqQcZ3T9Z6tO/mSqHOHcxdo2PpQzB8OWyyVaN0fxc+txHQvTh6mFLLiChpQz2XTOa5Ut2xUfLeRLbC080hcu4/J4nEulgMd4nFWoIKJweZnh+aBAGKXvKZ5Zocb5JzldrozHaHEgOdkjhG3r2TJ4zwBf7FERoPSpMik6ezXBcAl3KkgUnKWMhmoRdJZ9ojnM6y/NC0t+d2KA5SN2LZcdZWW1B85QwosSSQ9AGFYdb2ZxgPEnzx8W3qtdAloviZKaKsQfdxo9OAWFZDD0tblccHaXBj25k3B+CgKQtaAJEojOZ66UIkTv6mdGNZ7/WU5HfH11b5ZcnmHYOu9qswPFTiTHrI+GYyqabSTkAncAlj2TK13XGdq8U3zd+42Bsqh1Cjd/OU7xFgRcc4Yozq1GP98ojOzux2zjLs+F3HFM+F+6yIWYkgU4iXEmHMdWIwphR8O4sgx2GaHNsvUsNuTsE02ssSNf7pfRQ51jx7Mp6Czl1jgtD3sN9g4vXXtmdphZ+XjMepHBaWVlvgb4irz1BD8ph4NT6xWVCZ7ZMBRagTSlTownqNU+IOA3y+mjQHyTQKOsg0QE3GUatR2xnuDSGWhzoLVIr9KbhxaCE8ShoNSIRDZQKnDlkHlSEjFCQqyZHpk9QPrpo9VM0zWwEYHANQEqVFab0dIe/RCu6rFKWy/NUGmiZZGqNN2e+HH3yeOVxxnGdn5CfLvpzbtW7mbfJ/pON+viSFEhr37doCCyOwqe68qrRx3UZ2Ws+liNEub3qQuU+4Jnv8PtQVmrIjAna1m0sfx3wCO73LIN8CQBehaKbdqLUOiSHXxpghu7Y4RAjvmAN/FWuDhAD4CMBKTDFsPa55ovQEVnmRwW1re8UWKVNsMxdoF0pMfIm9sP3CrRMZ036XsaCLV92J4uWDavdhBCP1WtHtSMyIZ7FyAGeW7ulLDEDAt4Eq+Q2itg9EE+QGTrBn7q4vQ85BAUb9FWgaYt0v87O2WwFdkcTlMeUK8/uomlURtqSXL/GfECNVpmrXfe7vC5ctJ76a9npMUOt3ots23qAEKLh0B9NXteYWBWtOuLRYKOLFJJtvHryZjnUmn4ZOEqMePlQxGWPwg6BlMMyoR1/TfYJIVnD6ri2qI2d+/YqzsFIyocaYaXNQnVK0X31Y3AngvssOkdZSxRFlZhbWKB1fjR5c6VnkJujOwTzkOT4tIW4756db+AucjueB4QxWUuNeyPWVNZrDMhAel4zuNA+Dioeg0l0t2YhmzML2Ol1FBgOX8lRUifd9ebnYdVJQCwjAOFyJwSpsmnuhpJV+hj+QzrNMOANt2kITXv8FUalitp4f317dQyZmg07KZTrGagY70xnpb8odN3vVQRyQH6SyYBR193LVKJB5ShOIz52JKPeqL6jZaUwy/EtlxfWNIEN7kP30fr7vFs/yj/Em1fTDp0W5seN/8Jxsjd0SUfrjwz2ZZrvsE1ru8O2QcvUMQnOTlAvd6or2MAkiE3tIZCsWtG1ENgs+2p0rNhkgWiArk2oNA8S8m6gEGR1Sw+VNMyv9R15N0ekRswqRiACN1AMZD2m/cD7TFLM0+hm7q4ODyxbke2/v5+vHWcQBzUfI538dT6eH/udLnv9LzKyNzqnqsNh0ZOeT/+6nJuPhvp0ybCfX2BhXzEn0tJvQkF4JVZJKNcVAV4HB3b+y5Nb7dNX+6br01IO6fNoZ2rYFnFVbolvGVhp7CPpjtbB58j2y9ne2gJx+O2N8QPnI9QjsPTyJs5LTTx1KL3V46JSfv88vwXTRXvhuTgRaeuiYv/4gS/IsD/HQxpy6KAT3iQ0n7674NdSPnyCvOwGJHaBm7frQeFzmJAXiy8CXWlDIUy0j1nAHRl8oUQylzNbKnAzBmcbKyh8y9jVPtFqRo/9Dq4OgawUlMcAjEzUA43tWFKqbUPOqp/dTKE7e5J1uofSBTDZnEH9FGckmiqdGNxsAr5mDDrYtbfPuCT1aAroxE+1grqMnbVG9/jx5KRASQnxT82GeOZbVV+3+jcwhPMaN9bNBjXMt4JOOynLxobYUF3HSIpsV0LN9H6nuMqEYoOCBCgcltuJ/P5eIWvuShioagN77wjfodaJXnvViWrW214IE1h/UlwskZonAKjVyKOodMb8OGPtq6PtB6YDv4E8a+z+Q4vmr3eLOQASAuMMRxFtUXYY/7ufUDtvDLYmNsgaQa2+07BtP3jHfKX/abdTugC+0BFo+mWcCyJy1vX4Al2I7LOcEBY/DRyPKFLtIfM7JDau/Mxk7lS1CRXQqTaNHYsr1VzeVOEsaPzW9v1dR8V3VvCRMUrsBmwIhkthZprGNkcUXA2AuTPaPdh/1kmlsd2ByHkPQCdpAt6RObtLsl9E8tuyL20q0mDGv5og6n5Q5Co1baZDuHCbi7tdq7tl1CGrBedvPK6EZw4sXG2PQHyTGC8h1PUzNppuM800QAbhCFgcv8GFPKDz0rfVO1p3IeXLsDgwAEu0j7588Av2s9zWnecW9dFjBVtqYiafEMAiK+v4xaNru3ble9gBkBeMcdZ9UwIvBzhJ+NOk7oPxCJEYfmxRPLyvTloT5NdKde+1DvaFsjsJdw8Fm5wHlVXQRg0YkYc8621eTePiT1s2m3J7Wyvint109o3FItYms51FbSObRt/76d/Kb25zURw3Q1umjWq7iwMtyIYwmxfuLo3sQ+mcJfgY3cR/cHQ/JbdnwmwAoGp8E3Pjft9httTeJ1XEj7TdvyvCPOG3YvPZbw3RSWATe6vJZAtKrPXmUdfGbZxsN/JwBMknUl2AG/p1NMXucv/WSd0klGW6NIVplklZGkIOA+crBcN5zUSDhESKe7/hiqS3HFck7HhkLqtN3DVQcPWfiidTr8b/rkZF1PRsY8gdA9mK2X6ypssDiuwq7s24tULWLoFluVJYvmXnx97ybePMMlnucMqaKusBEXWj0JNjxhzvj/S/CLzUM+SbsVboqNN29sxKzVsHmGAKj6Njgz6aM3ypvAjmgEZHKhHne0Oy3Lq45nIPcM9VkfZQERGa917Lsrwdl5B3Zcc0OiI+VpfrHf39VTdpBtQO9/o3P8XdL5udekZCOQskYkzHa735112RodzIyeyZZVg4dW50Qhc9nc8+wdGlrSIA7vYzecblmRA7iiGa1x/33/9bGNCgYelAlj6wJXg/BhZHfBgi6XSV/AVy64VKL/jmGzftGKBN4Q1InHwaG2++zaavJtfJQloLiT3fjPb43AiIDLwB5+pngX3W2Zc74CVUt7KWOQ/lApw0DHoyLjeiFEUl0VkljKLmz5+nIvjjhpX8p1fl5KC8DvoZIZq2Zuk8ewY1jPB/CR+ZG/4W/XEOTkidZfzI/dMlrQ/8AqRLNSCjlrhKIo20pjajb/7dYjYkkr/Jx7UoZggD5/ZSzozwjPKeUBJehwqYKNUZ33zFmQAmopxq5CRvX6Q72kLV+CnmaU/38YotDpoVa1hUsyJfR6QESIgnaCZMiTFs2ECZcOt1KYd6mx8F8aCu0QoOOSCJwEU12QxEJxrWX6RFr24ohKljlzRviAkwhOsxDoZx/Zhyx9pmsHeH+9RZmPepOC4dADhfukKh6hO7mZCkIb4IBW24/ztAm5j6cc4b32SpUOBgOCc7GoUEST8UOOlPlItCAVuXQrdzbxRVnloBkUsVy/hJFYcBwfGSuoo2yH7Ctz6iLuC6vLmW9RRc9T7oNQCQRNcB1GO+WAhCqJU"}]}]}, {"codeGareDepart": "206", "ordre": 8, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T10:10:00+01:00", "dateTimeArrivee": "2025-05-15T11:10:00+01:00", "durationTrajet": "01:00:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 636, "numeroCommercial": "15", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:10:00+01:00", "dateHeureArrivee": "2025-05-15T11:10:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:00:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 9, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T10:35:00+01:00", "dateTimeArrivee": "2025-05-15T11:31:00+01:00", "durationTrajet": "00:56:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 638, "numeroCommercial": "17", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:35:00+01:00", "dateHeureArrivee": "2025-05-15T11:31:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:56:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 10, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T11:10:00+01:00", "dateTimeArrivee": "2025-05-15T12:10:00+01:00", "durationTrajet": "01:00:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 639, "numeroCommercial": "19", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:10:00+01:00", "dateHeureArrivee": "2025-05-15T12:10:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:00:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsKwmfa6tJ2w8Zpi/SOr8NxLIVSM8wdjbRs+38BalJFA6ZoiOdkuN1IY3MMRQnPz1kWgRrf2a/K3PtmrZ37LsvsLVSXMEn7og+Xe1ZdUXQlGzHytVM4dxaihuz80im+hZki4NSu409/7lOhcQ5gorLNbI6otx5HMwtz88h86CZw9taYWvIeChnuamTEQ1co2TYCHIGMNaXCY0qherOLQOlj5dPAOA7qhdxtYaCgoyLw+R4dM7/pZzZlCUStFwrgbi7nRIieD0ez4oeoEuFAA3yDWtpyqO0uELKD3TyAgojnsnqX6NwAFQU0s3YNgf7RFcvp+UVCtx9ONqeLY4QkR4yLv2hP/zufsi32sgTd3FZXuSbisSPy2awrXS/aePpe+EaCcVHXfI7KNx0d0uGREaNe4+s5e28QtKhUYInq+tHrlxGqelanppQBnlF1wO5E8WdEi9LG2ezHARDyMOoflzYIiAy6N8DXhU2DzQ2mPHtGJO5zXjOzxMtjllaAQPfMZtrd4Z3ii5Ok6WW2Gz7AxJoJnKGPXySMa4/qCytDiDVBRZZb4n/8bXLftdmdz80YvQbgd8XgFUNz/iYs3yjFxjNwtzmyStuBgSMF6hEftDFgmkUK+neqFlOWFuwVQh/CiikGs6GQVVsAnrj4TfYb3sMCzY8ZqEyffSLEaqAIq3VcnIdrIETe0+TEgpDghrwOXwPMjUJSG/zPjsA114kZoZPV0fzaVbD7YE+ajTIxbYwf/qniUrlVxkyAqIvTM3BBXgyhmaDzZ2pjtgwktGh7qOgS1J4QKptagWAYcCjIpq9NP99iLyQ6M3zaaNTFLBWIV5IWhq/ac0PNJr2XBpngrP9hwUs/9+czp4+PwAnR9U6gD9QTcUfnZAXLnRmew0tNM1WcGbNq4d3RqrlDx9JISihhYBpncayJxCXbTFWV52BgdkyjgtwwRtBcFOQOAqLv0A4yXqd2sZFU5G5cmYx5fYcbk8MIdJbozGCfZACoWzJOpOVsMYPtCFLCWGtur3GponjfS2cKn5nuj89U/Nz2RBOOqo00x0s9R02t9ITHWh4/8GZPrOIu8tISrSxgLml58eikAzPRfenF1d9WGhNnP1dhgCMU88s3CW5PORWofQFOqmDK88Hok0L+qpQ9LA0ht9TTgaKoEaYTf/hYEM0T4kK/ZvOsrc7GCz7ocOVCL6HnEHXb15Rj/0FwyUmShiwyy6oYQ0Fr13+3IumUNXGRxxOP0BNI0EqjrJUDNVmgS/2euGCvjaXrBIYrEjmRca+0Ro/n78e+yzGAQJm1egTugRsEDvHkHGRKwMo6WfqttZCH62NN6TxKM8egybFkB+gtqFtpFpxf4RAC8lhsUM3n74TACJGvOjFMkjKJwAj9uPKrGu30Nk1rpx9/ybp2uNr3TyLmaA/fcCDh2f7w8fyTnzAHA3WkiOJQxmHvvX/bu6ONCJM2/ntEW2xImiug1cGb26tY4qtMtFFeY9E6a+uzvZ7pCMU1zYmwrUF75+lyX6CfIpQEvnszWU7miyJnH0KeAawAsrY/JdgPqHRGRENyvlxzcEu5XIMuiIrzyevVWTxBjrhjiUILiBrpk4TUiPd4BuXGRFFq2V5nyXprzAHw7toABNiWfg54iHGbJ3VJVkGFljAcnLBMmhvOl5ZJAie5Ogm8vkdwsgSHI30MB1JIytpiuB7I/RKAM6h9VCap8uQtrn1Xy+GP9lF8n/Mt96gHwo5a93sjfYqPNDfX1fUpGqm8zi33YKzepjfBQQ2ZoDhx5zWVszZ+ZTWSDlYZ1iRkVsrws8E5md5Jl6OE0hZcNavE0NtiIEduC6nDBw0gqOghBSd7RNTAvomHc33m+A3LMhnx3s9gGYsjpTSAiqPfQiSKRLWGZgPkpvO+TAcU0f6Pv8pDNBhlGeIgXqNZueRPuZQ3Fvh/wbshVX3wMLKzfnjpevPqPppCqxlnoiR/UVdNQpjktXD0gpewc45CcEm7xbCTrdEjIJKmSKXBguqhQbePjJFNDlr5yy3s02TQClaqoP4G0zbLS0DqrJSV8aaXnq0JTgAU0uKPW6bOCopvvwoNUAMvfnfSR1AJSK0mQqRwcLF1M9FYNUFx0QterepEFgMglopS422/okgfSJKjcsT5AyEBMKZY5hdp6+SzAZld/CnqGy7kV5ta/f7ugKqYRijjDwZgfiLWn2dwBGE22X+emT0x5gPn0cs0Fvv5XW80ZmPbjHXGaQI0GS5TEsZLKn9lJpJHqdsyBoRSxN53JmLUwktoxkfPLxfz+RUJSmAcjDg5NZgUBpq12Fl8PGxfXtR6/jTV4RU9eNVxyDwESzDQ3CiEVUMD4anzX8MOnOf+EF6BljH74TjZ2ss8p+xh5QJjkbI7PS662y+633w/7ZNHWZwCddN/BvC1LBwQgInlZxqKCVl/kYwpxBPemXL6qe05t0d3dkT3ddHf4kBB9ad0EKcER09VTksQm2l0A/1Ibs4o08m2Du0H2T2D1fWZr1xG5eIgaYSM/EKTCxvcCxEARNNFOV6WM5LuA33FjNp4ooud18JmgXL66qbiPVp9Q5T25Y8oalm/eD04HLa0780qS8DJt7+2BRjO081x5jX6En81w+ECRsvHti9H679KbdGLHNGdWMD5MO5VpSR9Kr57ac2Rdmwb1rv+uf45tEkbx+Tavjv5hlMHWY3OE7SwnQjuTKlHU10MeCqDxXSnZt1U3qb++fcl4qVRUOPDvkDF8kNnXrryEEuFEJeb+viS+9Lghwx8zpbrURq053wGg9t1RtxSKHXUNepOB4XCD3ls5V8Sud3cScTS98EYEoQCk2NR1EB16a5mvSkO/LHpX+gNlL9bpfVuAcqmK+ZjEWcVDp9AyqP+ji553qLDV6VDjHtWuecwrLa3wQJYlPNAlFFDdMu6QXRzDK16r462DL3zsOazgnI/CqDzthY61JXa6llh+SjU4HICcw6lvWwQldqQ4ZIZ3iz2YWuhUJdrZrAn8dwtM3aAo82byGLCVPhcUMW0/Vzo6eReDmenoloXM8w+Ia/sFMqxvw7djO5UJtDKCPnziOSO3mopKa0NPYiJRQSVMQAzWgyovJKM+y7cBo9ahuRr5cjmnY3TjI5GXebNjjgjUrLaP5qSq7KPBrF6xfIVcDqWyAYW03av9GLGfdQFlOgNrjfH/plCXre+IQ/WZ8eIuaUkb3V3LLBLfjuuRqwwmEjVf9TgOy1vEfUK0iqDbzOigmsd9iROaTJ48DFBSVwrw3iF8TyjyyC5eKhIm3Pfp50H4oV+1TZrZJ8fWu+fUTbqn1bHDS1QbuEoc25oSr74kacbQBh+iMlSgj09L9LU8iuYXKbTBVf4No6ibvha1gaGiJEspxT9KtZQuWOnjXiWdWAax6uoN7OvMyV/npvgVlTRTswTbO8Yk4w1sc9oenDpoHLM9FuzXO1O2oVRVuGMLU26HTWNEEsgQ5/P0+8J9EJdGyg+SDBXRrxRNseeqmAlZGSsbme7wKH7WUM9bghsp8pe8X/Gpd6DP6xFMNMuh6V723aLCq42AMOWvWjwJgSzm/18O5n/SIbf4G7JomjnRST5+jxNlLMfMppFtUWxPHpGaehFfGwlRfzgSx+M8PksncSEb1jJyHTfwmtD0p6gAy2jO1MzaAlJa233AV/S4HWs/6iTwUUZuk1jDW0BLfjNl2x/IJ8mV9Hs/gHjg1F1Z358yW0YBIgnCFdEJNzbhsMqsZ+fe7vg+cYSVR6EP1D8EWiY2wTlOmB0iVdHCKwZTk1dZjg0wFvJPWAg6aTj0g8EHHdzEiS1jDWT/fYV4M7WSk7482hTifQfLIKWSNJx5E9y50/9aN+IIWGiJow8XihzMMNjiynnnfbinM4IVMWmqiylAu5lYxP02itmsAjjULrDejkBXOyJz/7PthrOzc1jvMS0yd+RSSiEZJ0NmDWU1WbNNLYPp9t3RB/J+Tp90YLNs2jXLP84M1Fu/vT9/orSuvg07ztWwv0ANcNNPriJwqVYm7I4TuB6r1CDDxVJZzaiCUXS53FcKXRN229qCk5reN7Z54tXMMgOs2l5wcT+ERUnP9flpJ+GGdEKchFQFOKfNHMrbMrLNxf2LMZehmjC5/o50ZVo3y5uC0A7NDJYoEGPMGebtThdjIug/G44xFOcAASCyCL8UtmsC4sqkilP0rS/dRlpCkS42jdhH5vgY3doYtyj67Z0p0fewjnbDVXyRI09SypVXP/9AGEzk0U1/5fcYi6wGB6fYE7RRSkJvGu2Qae1+E0D7buMbsy95qiynm7+opqxLTORjvQAy7F42IRRQ6OTdW7XdR4JCp6jARtmBeRKBIosY2O56gY7MLgqRwEmFlkW1iovd+jqZz44RDQ3dJC1fKOlMFSkN+7KjyxzrjT6CvNo/HWd1kLGnPZpvYLA+HzikAjTVwL1dnYBaDjSDj3odsH+IxaAfmZmRMYcsc6ImhwN2Cya55rn1khqg1U4CSjCIlJ0scwiGX8wg2IP/gpaNBzJ0zuEFq9iLniAoejEaRDdoMetfwNomn3MI4LnLmKY7GTDY6d41dQh0AXTZNHCk6hrprqITu8E0qZDHs4Vw3NzuwpyC8H0djgbBxL2PYYOAUwJNg+Fjt22owEdJUbZ85uiJL3wU3/S8i1kSOBQoRUgdvYLBHZ0GhtPupwAO/vOn8K+odV6znmbq/T8JhvivJELlqC8/QfUGA+vTVd9ilGIkQ70GA9h52qCo7zPzahsONvd/MqDWGOQ8pyhY9oqN12PcvHM8Ou/cv9E/o+WC4i+Yery0TfJWKjNFRr5m+ENln5hqbR4LHAsmQyEPu18lWMgx4km0zktbtHo3fubrSlQrhz1TpODMKFK8b1u5pbC4/kSwSwEXMRiZwfw7tC4Sp0EopQeHF30Hbgm6AJ+nFtSNHW9zeV9MH3iqC/yJvUXRN21LC2pythEPBf+GlLshnJnvLW+WBIh8ELEnklI5cwfdzZv1CYNmSu6Nw7FRXP9QX8M+y6sL5ru5M8Fh05ohG9yI0T1b2k/Nc0rf3AIkY0/R0n0KPEjGT4FdcvkIoyHxqNTTlDmnaaDBeT+00gpa1LoLnG1RLTa6aphRsdwg8fMZO1fhRHEWBq3drRQf0V8xyOOqoaM31AR/RTu0m2YElosl44jDeYfUaIClNHKuA9rEQ2I518IF+nw4Kyua/8dmQAQlr2LXY3hoYHvt+uHAmocz2/Rm++iJ6aROlZlvSHeWfMVCZkvglBjMSaH0xTFHq2Y14S6EKY20Xjde9TNoAEwFFXM7nHnUSv+8HQsR3HvxGxBHoDK4Kf6iz27w+9xx9C2bpa5C5nwvdtOkwcrcf3i0/YlZS+4IMjN0QVdJPlQ4T687ruLj3Cq1poddsnnc3SzSt5ydc52aNJcplX8CiaGq6gDJdhQKQOQ9UBPUtiDO5TIbbpHh629vz0UGmB/MWMAenAu0jEzAyqZo+h2fGGDy0RcpyKcJ6Ect8Wad8YNgte1kMMar+c2/gUSNfcksLZxUYGA9xUxWIusO8s1XxZUGn7gaQPiE2gzJSsrTfTgXUITW3/Q38ihkLAxkLkPtawrk/pdOn6Zx4Rpoym5JjM0JRpuOPodjAFoD6gZWgmKlV/2kQYlapr87cJY0pQRF3WcXOctSoVqeVuw3lniImTyw0Zzht2QkSV5UlOhae8Gfwd9N+O93izHm1IGl9xe0GSBNMT1lW+MdHGg35QIazjdhC3v33ZsfE/R9MjcJDMEBhhWsg/hXUkdh/WCoQH1tKzbgT/LqYyyh310QxXcM5My478wW2xXNX+NOoE3MWmcliDFx8zpLP14TIeN4YB86UL4/R5xEP+cR8VvvURMr6Q32HGkFX0Xm9M6JWVrqyw9IP/Frb08A1aEDhwGNgkJJzk0SHJsKKJfAfECMzJstT6gqmR+Jy6Oci8cxzMTWqX4cibKFk9eFKL/HCj9nEm+MNTrtxZEi1brzCnl5x8gjTT+aSgekS5ijyrIJbLbzPUbRQu7hizVIroWBdGU2uD/80iizhU9BoVfiKa83Hs/NOTi0xifX/YrK321/6rzgAzkuXYg2e6MXF1hgnSkkJfSDdEXoWjb9yUOR+3MeZDNKDv2HMzGVcwe7oqstOZtwOe2YuSeZDj66lhRUza/wQLX/DOpMkjjplVuCOZQBtVKMYXpFuK5LdSwQa7AdiBQU7tW7QWjaKY1INLE0JTcwUwkztAETaB++1UCFqqkAnjypPagyTFsTOxyHamKawh6ikYMLKogPX+JOK4vM38IoNOfdyqMF3zwPg6YiScAvbK5IXbabBl6ZFnIFsMmVSr0VkS5MhB0S793mdvkyItPGwcMS48NStQn8rOKFBFAe0906XO7cGGv5rmSvVaC0ZqPOWSHwKYmZUXHIWERKGZFKcOWIjQR+8TZsbRCxpd4ZEpavwYNROyQzGMVPN/X5dezzpCmI2XOyNvJg8xRV8lY28q8MMHY0ovGwpeZ+Wipq1/ToNvUOVvLbvjEP1wKkAQxKAGhdsJ9srK9JJYbnCl55Evx6WNXEujIBqvhxGNwJOAgolvETkyPZdkac8uL3Ab+Y6w+AaDmzl3OuoHhgj/lDptzyIUJi7nXEWgakvAl1eiyqaAt/rAuZowEtBCXNuPIGfc6NQekJ8KiKMWbPFx5MKOE+26w8kOHFpGFUX36jXNhvWIMGTsLmKX1smgByVERcoZc92hNqn4pF59C9be9kIa6deQtH594w6Dp3QQXgC1XZucXuT7S3/5moNUe/hmuu3zxAE6whR+WxpXDKS//Kxflr7tHRvPrbVjY+gx80EN6jny3+hCgZ2gZXlmL3ZRsrRCWa0TW0FDY6kJJpWUUq3MM4HidlYcG+IpxjBRwo1IkK2os0e3laIw52nOvmrV1qM4toOaEht1+qAg7D5XTriQW7E+y83c0Bl31fhY+fx84ySMxSKwgTdZuEYyPSEQV7pImeTA8aZM3Fgc8SEJ0yCIoUaqWQRmPV0DlcEzrNf4R9IPJH+lWr8zOwGgzqVZMp4uSD/PyzyYvd7MIaZt9gY1hFKcmSwSezmCKGKPKWwbtjkR98OcklwN9l/6Cmh0z/bkQ/8GVfaBNnnrOIK9YsLJikHdv/v/kOOteMKHGdP1WxHP+rQJWdlFjm8/il4E/PCLh5phIcspPfeylD5853qhJoelHoScr9GiHUduf8QXl/MicX3uWqtkAOb8JK9f+YyESWxgV2DmLc1OxflaErV687bQLsTt85cUCSnYWUYDtQc/vOMv0UMhlDVkrlVGrO9NhExALFwPwy0xDkGbE+FfQ6CYYqzpmoXSZNraXSE4Hihhhk7XmKLtBKPI2uOxJreaZnz5/2waLxRoOAi6095BJ8FNz6NGmuZ3Qx5JSETxRINgJ/3TlqjGChgCvvBnkJZuKM7RUpzNY7qWA4eYEGlquS5ozxD3nr1caDEE37U49DhkXNDw4m36kFx0lR2Wdp/UgH9lqG6E5PxCA+6gDEI6Hh2sxv8ijRI5SF4yRWMKLFoQ617BebD5lOjK17qPQkhTjThaJE4mlB+8wm6jaI5mxiKQMqLJnOT9n5LZUwkVged/OW+nd+P4bZPrSQwfQ0h8H7wyfjRB4PZW+w9a8Xn8BV5UYTy65NxhQy8zi+vJm2fYOKuuMYEhSTnxzVU5KYXs4u6ppGvIEOmeDRZ8g7QVe7Uw21PIIbnOw1NSDrGM/CNyiDYv+bF9JgAC/TETriPGx1FCPS3SyZQ2neL3CGNcMQoHswbM306DAylJ+0EM+4m6ftTd93kfL9Rondzfemv22uwB959l2ZuN4imGLAZlsgQ8TXhuYGZ6j2cj+8U7Ublxv0FiM0q/wx18UdWTW8lyeqQiHGItjpkCt/cTD5P9VsuM3YEY5BiwZU+J6Zr8XoPNkPTAQSQbs02bvz/rXVDXcqBXz6CdkZpy6vTXsqFFTH/ZvyDW8zBnV2wAlTt2ifM9dbIv0aw0HWoAG/gGTGONVeGWgCRC+LUA3y3cEENdbWWt7Y2z6//1eLQV497zbHIEjGcw2Tqa8N/tH+obSQQF59OaaKqTlMGiByEfX5dbQb5fROV3Pzuni0QijUNf0t0Ie162+ETWNPF3hOkZRmQOQLKwBsnVCnlg9oUCbPCyPCYxyeLtyZEAf7BRU+hRh9uT5P5x4l7EoUwCJAZFTVg3GeYePIcrrhYCcc9zKblMQu8RnQzfKgaUyRpsz9RjHciCrxqGxSPBGQRLNNFHulIrHrfMrA6V0akz4oJBdwPVVrzZyBHVHEtX82zNS2P8OjOWJCS7DEq5yxKatK3t2eL1Fpx9yHvPsuIdrMvYOuBkCE4LxpIt0HlJoVTIi4o7eHKh1tH5Xv4XWTeYVX7iPV7+nrqcZ9gXsuk/0FDI4qA1UZ2NqTO+GS0DKej8mVjvHjkWxeOkSCpntCXIYZxxC4NV6hAOvwXZ4jyoeV/4TRrMq7mpDhnka5bsNcEmR4w+0SM2wGErTp5VjBNNnTmcmQIH9TIBNdQws5eSN5QY2fCTKl4iyj4KHZD+mFZpbouTO4BmHnG4AfQrEeJR/7sBzpQs6E4vY0/FANjl8b8TF9BaMGDEM4gS3okWpHd6ptkOwLOyoPs97f7ho+R+zXgoGSIpX75m7sGLCb7qjEXqb0JowsyydcZ+xLQd8GVrXKtI6l4FqJaUSOqWooBpJwfuh90KhlO3IVHOcVyNLDO1p/KoWEbo4txc4DNeNVwgsxxwARYA1eCgj6mlOIyxWOwFqTVxPSwoUuYoUhTeDyBJKfGMvxDnBCiNFdGYHFydOX3Ybavx7i6HqVmV5nquWbYb6hzqyHZuXQBUEyNiKLtCkDvVTr8u3GfdU5MaSGXtW5orI/XlVYJduGX7vWQeVh6zGbeXgiownj+LCKCVg8fNBI5AYSyN93I16tSdgc7+jrpSWg7GpY3UXob0FaXWwbzxjB2F2Syfm920fGDvs0K7UcT8XAoyX8w/nDxl6ga/z91lWVPOFdH4pEc2OO3VVLi0ccQEQ1xfOcVsyBi7bzjiY+zxrWs2N/IANsdtZpWnDFQHYkzkPnTOf95MUbCbVv7UpDOsoFG5A7nh0BUUbtsiwBHg0Q+YzGnLcj5AENDApJ7rUXE1iFjYBaptjN1wQSyc3Z4hTaze/ZEk3j8bv6e2s3YnSYTWOoq/q+9+tMmpIFYk/pmdoogj966u8aB48QiJt+OCh2F8rabljjB0GiGyrb+QQ2vhETFB6OqGxB4NjYShk1W09s61v//ukFkLV1D1645buWkju3jNJXCZOkGZHUA+AWStJr5/FCNIYL8Rmlu7MSo2ujWyO6tFL2HJRRr6GWvKqNBx/RmI4CioBykkGTMcWp1jJ0bFRwFoclZjyYMRQQHANjxM5GlZ2c/yWEZ+8gQY1Qb6xMt15Xnh/TRjo0DMemBguqjEzKqCi9QN4lnX6NxWXvGEt4Ftz34RTx2OqGXGqm2Wn/4Cmpvk5kCmrs0dHyE2w3SUg4P+Ct0ndMsOzYX2pa9EM85HUM2/VNrEB4I+ot8TW0fVCpn00cO2Bk3qMnhYDzhMY46mUDCpSSBGb7hlsynoOKAYp2nCT8NQuIfAhIKyvD3ojaYgFRiFtOn3wvTpfCg8g2OWQGATTQqznaOk8zHPp38gBvdB2rhxKrBinkz1p1ssTIpCGsZ2di5ye6B5Awax0N9dGgebewoyP3eaSrI9c5eDmTHQ0Y1BLWK7TA7FYOa1fUA8xCTYIFXoydfLyoM/FfxLGxT/7rEHIKHAlkUPlJ5K4kY5SHj1v+1gzkhlQbxM2YbkpeSHQYsbWToUZa9fMml6JelA+/0c2Dzu+X+YdOCTEuvYUSZ3jMsRF7ozq/SSdG1U8qBC57M7zmr3nGx8VOEMxL0RSlzEiCQS5LmYWfVQ5Blm2qI8dcpLisF/uE0OeGCexDtwQyYR1Z43OOGt9qa+4At68MhhyRINjLpdsD/3URjOQZzJ8wVJiDjNWmbUKcwyG5A+4lAREmc4vxqt5VWF6ZXFN1ko274lMAp0dfvDJxD2icV/xyCEZ/Tt71A="}]}]}, {"codeGareDepart": "206", "ordre": 11, "codeGareArrivee": "229", "dateTimeDepart": "2025-05-15T11:35:00+01:00", "dateTimeArrivee": "2025-05-15T12:36:00+01:00", "durationTrajet": "01:01:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 666, "numeroCommercial": "21", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:35:00+01:00", "dateHeureArrivee": "2025-05-15T12:36:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "229", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "01:01:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 70, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 70, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "CASA PORT", "to": "RABAT AGDAL", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}