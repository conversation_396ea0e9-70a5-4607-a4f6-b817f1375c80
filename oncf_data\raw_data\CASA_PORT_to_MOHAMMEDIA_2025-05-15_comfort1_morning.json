{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "206", "ordre": 1, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T06:20:00+01:00", "dateTimeArrivee": "2025-05-15T06:40:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1, "numeroCommercial": "1", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:20:00+01:00", "dateHeureArrivee": "2025-05-15T06:40:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsupROKEWSLQer/FCotRSZIn1vL+zGGrQdpsjRjaZ5EFucUROUAFy+/J8N2UepP8/PWAp0CBSbBO185/6V5qEVoAWQL5bOYWGcNXGo9IJmkpv5Siu/0VFaCtp7FLljLK2l4GtxGADtvXpjtqC1TSQsC99WeV2tqYqYImk+CwpbnP/TGHWakcdvbAsj7Eu4JDY46q59nV9kYuCHo/LwZ+bi5+hYW4Tf2kFfuUprzUPhXWTBzsqONsgPXW5V1bTpxU46PHvo+70xg7gBxrs2pqbD3RAykd+X/UDeI5jwyagNlkqVawQAZnorJXym4UcpWA0KWKw5YBnerzVCUxAFs8jSy32Ua3YklQTmDTJj33NEyNmaHieq27/9z03mgiAcCSIds2aAJ8DwXXMMS6C+d0o1QRfCXsIMc1rFghp6L40crwMfN27ABEevbxfGg4vJ5FnDvenRhGfrAgBvzSzGLs66RF7cOZMuDTdsf/n8Ux8ZXMRt2iZ+lt/4ImY/5Gng9FNbedK76aTvTHkre9IdDx409UcvOgvuXVs+oz+0v6J8EZ8sezd6FpY8IbW7lJPmKpTDjDtgqEHKFbV+qUvDnmZnfVy2K0KOTA+PS553nQ/9b9yYPdficdHbbhDfc6URZdEgYslYVBXxig0JNgGlC48hYvS8++YEFEyQAn/Zx74gFCUEIJO45Mkb0V6LtG2IBbAaRQzREUEUrJE8HGAtS2Eu3UYD2mEQzms4JThzzM+kz6W7x0I+KA3CD3XFD+B94EuKKv0BNVM7kS5i+AcIBgcBnNyUI1oTWlGPhpKYbiKcco+lFpnjsq4H5ye0spQj3qXI1OYjA3T0A9wErYkgJY5Kz4R1GjELZxRdmrz7DsXnk8LmNbe8r3BhtLlkvdsVok4PSzZ+G6940NNf9rRkPT2QrYsBXJGwgn4sIAbf0IkCpbHInOjNkX/oUQaCnEGit85/husHfHAgQC6BavbV+iR3FhuwACZGXSXgcheDEY8Zmys14lkEp7FMwGaggLz0cKVZdL5jXK6RUkKkBRqHFI+OUAZbXiCkZ6TDCVBt9MQk5PVJFt2oz9gpZ+LsqUPnDNuPpg3CRKeC280ndhvYwEdApwoe1nmyO2QVlXIVRNrMiZoh0Fej6g/cuK5K6zX+W5XaBu3ozcesUwNkwj3NYYvy4piGVDvbyjQYnbUhCLkxJidaVs2iRW8uI/lNudzy1o9ekEBUDMPsGeDlrFJmO114Mrvtiuj3mnvkn4h/mNETpqPX02GxuEb8TnJ8dDc6fuqP2W0nrmXw6dwc6M8ho6sBK9zwFww/PedMD6R1meaXPGV/H3+sjseMIud6ACG0/wHE6guaf0jrK71P1UtXGYroWv2R4bqMFGxP4qw4OZVnMu7bXjcHrU8+OCP04lbe9hBkwUYEkpNK30g785c09N3VRbvFr3NKfPJeiFnJQLhwFZFqSw8HvBIHhKphWA8a3Qg9lkevJv8NbAzTvLhWqf6j7EV5OdWe5Qedd39JfvPZR0ZH8n4jyoiGWOCjX17g5aidKkPNdHYKaoNmx1esK6QWj9cAMsUZgRaFzO9MYMuWodtOEshTdshG6qEhSG8ymfmkBqK5SN60s7wI54gp1erLTZ9QhqFxli9U76wioWH2gY3Ib0ESz2ul9U5w/VOIPhup6XaY6mrP+jiYkTYbtw/BndwY5Ev4JKtyF39i9XfpdNog79HCdHftuaWTRzhfQz7ikD8T3Rv40i4dkqA4/O7VeqQXQ7AHsNWSczUCJ9d6xyfNvyWb7Wz8O1mtOXbrwGVs5TYtzdhJ46wUSzNAqH85ah4Myp3cr4wDnqVvz0+gBNXA2QBhU28O91s8H1Nz7tshzBys8RN3pXcoxq8l6ZKWuj704NwlHOp46FFwAI6IFcgBSt4yT5/DbzYT8thJnmuy8vJ+h5LWGdE5gqVEo3kvCd0RbCQJg0t4GPu53jXYyrN6DUOA2RbEfkVvTPpTPagM+ov70f/GHVBezRub+hOGq6m5S/eezRY0cfER5rJdxjgJqxYwyaaAkLbhsrOtgfCC5jGc1AlClZ4zT/g/Anx9j0a/yYGbMzSP8dmQ/lDoeNj7s6Xu2rcuaU6HJbv6KtKJ57Vju38+7HET2fMcMgn22VdZYQ0r+M/g6Czx6eyszvW9Uo0WiOG/M4n8h6482/OaS769YZqv5hNp0EbyBNeZDP2jvUpdCohm/jPzcEdwujKH91DwIqY/FV1XHcPYo1yo9XtmXcg8O4YSU7YCNmnU8nhZpDpru5rEcSACrVPnEG5yE6dJDyNZaV4QBwnbn2+vtI9yGijc3e8+0TP6VTEppLYr10vvP41IGXvUDi0HMVPEAzIQJ6uETUV4TIBle3Su8sXTaPFFSITdcpzWODzE6Fq2MZzBu/Jmhy8FlmhOXX9NBaY3yxvK/WviFTgBCxj10Fe8/GzVSempGWNCc0MRLakS/omd3g+WVk5qWYWUSyIevW77n1qGARuFXKzsE8zk8Ia9vhFFTC1ifkqd2fn15bP/F4pCtdjdsqGJzTDY63RmscE0efS84uCY8q5KkybbxH5vtyXo18Y0XA/qgE+OiGihlTVK0C3PbRuk28Ukf8Ve51KR0X6KK57QlmFbBru3q0iXZwQfTUfLEW/j57pVwEael0vwjpmEjEJt7bM2P0nOPfFaTSeAVRRwY+OXRz6BnDE+9R+c7Xvrm1ah7AFAxyp8epihE/g6wuEIomDEH5kdsnDg7fhJyqxvjbvWxn/E4NM6EJNrnFjzpDCQX7PaNOL9rax5CkzaYsNw7IJQ5tl8HYx4+3RuYNmaEzVvu6VMXg+9z4L/JIomKgL/Sr0zUzgLy2+3g6aIb66xwg74MyJ1pLj3A6qxNX5dROi7jumrTw48jOhUsuOxwZsMglwg39vlZnqvPcnMZ6JygDTJcP8zZb4k1FFUHpkt6wqdl9VbT61sN6JGVHENdtzT3A6WMbNgxf354yF4mpE7UZGEG6EZEvk39Mq54/Lwopia+91yXuks77w5ZhyQw7WbBtWh1F+CcdA+9/k34p0Z+h2OLhvdu63oZGgGPeIzIq1r6xghB0k1Mhpkid+rRXDqNehqgISM6RYtlC8ChXOrvEOk9qy7sQ8vQFEQ+dXjcwVCivp3aDyGtR+neY5DEB7Kvwy6FFHDWTFpCRS0WP2GvpjjP+PRaW7jYp6EL2Atr42lnCGAHhd1HkVvcIGGBhdrooFvOIs7qjf4rUE9H/UaOQOGyDD5FniK+5en1ZZ6b8TodaFlMSO6Y0bSDiG4gU7SyFl1rvWyxwBw2cKv4j9iEtgM50pfyeNff9jkjK60f0Ap8ki8PhpA2IhFAs47KulidhEfr3m+ZZ9uRUrgM98LmzcJ3vwYyB7UaD1wSGSppybg5JnPhKcB9UA+GiMaLhrX9YX89chqsyjyIMvpHnEAie6USvf/jedAxcVJMoyCOP9elUSqQyJWA1PbH8YagP3AN8lQ013wOCCBpaXtjtRJ8q6kgIXVsI4TNDpjHSPmJsOCZhg5wGVGX6iIW2BMgb1WqjkzSNrgaXXeW0vWaVhqMPCSvPvIJHm1DWjdDC5H4xAvQgrUUY9a8V/Wkq4UlEm3WRTpkFuHoSxI6dgXD1MiUHyKVDkWGmO+Prb8mhaBW4ojJVugPyfeotEG/EA6mHDKlTBjmLyE9Oo4DVOBYiQwmxGw8pbzAUM2kHBtwC5TrqxghpLz0Zb7/v+o9mbz19WstxuE15Y/vywb6SmFL7VplT4bwM+AD5x8udeXoPp9u1xI72lIUu6FDaYpPX1ZpG+KL2R+AgEq5UYYOoXUTtGEvO9tmURCVKSzb/NchTShTvG6vmBE58Jp9HaFwtXPWofcQxemPTkAXdYCKbK29xqS5BFnmXnYf8FR4YInVKFS44cmVlXXw6DwNdVilLbOvUhy7kbYCsk5Lbrxtb6qA65VWQRG+2Yst0ku084lqY3lVfFvVv0+Z6eQcRg4T4lDUepiZfjuNF5YQiV4fDuvb3/vrPamuu83T1CjQEaILBcjoW9lJA+s2seldTxMCgw4j/6OYJrdvxvc4Q9rZ2TAeWIsx6bsijlzrfkgthFKke831j/mH3+9qCUtT3lT415lA+VnxeZYZnFMNtpbOGZn63mFjVRW7Msjg/ncMIR7WeNZ5ArKyFe0japCVFAPrGgMXqJKIaGWITipb9iTxvezZOPaEMQGqnLEESoXOt5UleITLBPv7G6HClP25TS6Nu/arAItN8l57uXTSdYfr6MaH/IC12Wbd8cV5X7kSF9Tsi2V8YDdNh80K49iGDkKGp+RoyexljjeeOMLXEBx4PyjX5z7cDb1QIjQhyebBU9ej5frX7GD7P4g7uRITZnWfIsN+p4Ye3tZeK1FtR8z0TuM3ae/hQ0nbNBKHUeU+QkSLAXL9oMe+xWJLdNECyhuRTjQCAHXing4kYV4PemRkWWSjW5zEkPgHl2VDZCLXFK0fM9KGeHHe/3u2dMsMFRth8bXCk/7YYYUWZIdjsUqzRXuH4usiq8mneEaUgiD+4ZJgpImngojPbOo0d/1tLWMK46+nAf4gHp6mDRFmoTfnqmooOR1zGzvzi4Ua56+SzlYwQSxMHE6ky/Qoid9dJ2LK0d62v0DH7pOeg2KavX51TZRyPrbdExWhdj9KUDJbHZzTVQxA6Sc2naj722zYDWcR+MqhutrJn2YtI6GZdHIKtgwQJFJfUhF00U9aKZuTaR5e4pAdUQazgfdAgaCb4QQxUiym4ZyvbSkprvLQmKlTUYdtfVkduS0+JGa+sSPH8swcq9zBY0ZeSaZB17eiRHfcHVsANmarbVXoNSWrauYPGOJ4GsWK1mIXcp29QaUaeTXVKM8LKB34F2sdq5TK2BOSAOFQc0mXy1jV8ZTrDFJeFk+/J4RzunTt6QqPm7p/vBNjqqeDl1kmMw2brff+6z97GB91X3j6FBodB6KyJ/KtGffbiCfN/rp+n0Dzo7haOrh/rBeUNquIs9cW5YIEcqpR7O2ZAoB1mg/l4lewAijcCiiUb/7RjeVWtdYo+j4FqxU85IzFeFrk2VwW9fN5NHP3WfD+rJJkM2nAqk5XAJuLOdaqxTZ3TIpJSOs2lGQDVB410ixPj0JJ1/R8xzXdPzy60y8+xnpYArXF5MGsw8xbaixLnXvrtTWw5jj09T9RHULMBdFMpTYFgA1Sdlj79bLId2BVMrLhZwKluB2bcbSlyNloxXkSJV3NXsnXEZN0T9y1bON3W4UBOG5II8VdWHp+UBbpqq2nU0QiRto+6hn48N9llRVtAdDkyT0BLLdNzstxLOoLO5IC+a71hbh/uKSV9UrX4EwZrEYCMIBVb+qgS6PEkS1P8wFx+J8ZYUAXwtMqdqiekAxl592HM7NYNiJaUGcDiEbZy8yZhuuaTQYWeIrm5qpMuoWwpsH7NZxZOgAe56Shffg17MJUxB9zSfJisDdI9G5GXTuj83hKcrh3BxaHJWj+RKAlQi9VROd78YIi7MBg/gBUXR5WDUHMubBLsxvXcQPTcQheLCH22RWVJYhNC9KamgMH9RyE35ga0pnrOt7eWTvS2aOCT2l2Y2RXA1YebPuctqc2UnpRIra2ZXW7XstacP2gyRRxRlL/nH3W4WGoELtyFXO2jfwLxTfW87e8smi178k7qxNVaKZ8TT0Dsu5SEVWGgJ3UIU/oUxv551DDuYoACT/U6bdpMCnZrhc+1pD0LBf2vUBokzL9W9/dsXiofE6mK1o7+pHx5HnWWQ0cpdlU146FxocJ4fpdkXYMP+BLu5ot+GofITa/b3WNUcWjVns2tuGRJ8j7vTDTyBBLLvzfPQlnI26Regql7RWQ7vNhH7XAD0GzDgG476BWLOYCS/4Qmo4chxZP7RmeEpsHvaLqxaoooSlGHpFUxulODD67Sp+gA9ahHSIFbN38fwLVLAxyRLZ2cSFmmfTQOKe8OTwWzM6D0KrhJp5/T9UPu+5Fp7hCOV34nk9uaOLeY+PDowZUm2joqOfuKJztVd/a4Wy7w+1YIreGzVABabiU4r+AY54612W9/P7eZLBLjETopM1z5umT3DyCZ2qyXBtni6OxPGNoda1xrisl4rh+h4fGqRqZn8Thn5Sevrpky9QU9LZ9aCuokEMthoDlqddJWhDnSpYKHyj97AHjK69RO0+AI1D36xbTiyt6WiXmn25T4bh6N7vBIv4KSsOYaRS3bDgQUZJMkTg9GlL3xv02YWOn/K7cZ0K6l5Uy+ZdYwKzNNssaOo5EBtT/wj0MUX3LWLwWdzGEr/DN6VTr7kU9VO83qtm3RXYHrN2zBhFtdU1DdEngJ1te4j+LpDSpa1I1ZpYpTjuqv878dvcT4fKFIbUvt7bNnAIp5LO+vFfDhm87N2mGK7VxRg6UNT+N5q7L5qscOti5VDZvDx9/odvG6sc6KV4reSQPAABg/nrEpb8u4JcQNypcvVruuVjrDvP5B6d9zcHJOlhU7ds69KqbMqLp+ob8+YASQpzmzzQ2npF6mJ/LtzHaSkoJCOi5vL8kJ4lf3espq9CKKooc7DwHE+/hx/moc2/saeq4X0dV13lahQfCeGTt6S4o6CW+p4tvY6zA7PprqNyGQGoc0EIHyZXuTt+nq619MPSilgjsrqdnhDi2wOpp9a2w1r6GIFSWedTRz/RxH5wWbHxtdkL9aunVneitQPNcfENG28LMRhe79x+8Y3JEEwWxZwmV+HoHCU4to40NPWfYZqZUeyMjnWnMh1tQAUreYOxRV5xydQGfwL/U6UagP/sZmtIG6fDlCTdQiB9q06NkRPkBqeKEd8dXWyCHZeD/6bF8+Ekj/0X7OKWcBc3iSdK3G8eWRxBscecZN/kw7Dea5W5Qm8VxPLLyZiBKfV45FIUwLWKxt9DkX3ho7C7vr1Nj6gcvKX2ohV4ZgM5FOjrPfk6Z6+EpUYBF87B1FCGkH9NAwJFhigeDiIeFl9VkgaEArqtD5xCBA6qCTfNL5hKeYpMRzJe2BuDVd+vaTMF3rer9mozISy42Ns6H/Vl8OSNvd2e6m42ZLQkXfoYe+36B4aCt4Uw7UUADpSBm4/milkrlcYzYu3JPSSxGO0XKmOsw7JvSV33DeiRdfp0YrcDy9H+wqzZvLWQbeQSm8bpVblnfxWKXGJmxTrdtAXbibJ2Ygb/Ddb8pcqafBYmsuqyESDTKtcVyjgDsZf0Fdieg6YTaWPqtt/4fOE7t7FAOaab06YwfjGflcZ7syxJTviAKN4JV7NYRAjl6zMn9KgHLtgdpLudqxFIXd4yYzuGFd/dDS52CW0oqu1UcuhXhOQu7G8qrsncF7yI+SQ447byxYUvQ+9zz0i6ujsZXXST2m+nnraZ+zog2d9ozAIzJMaM/na7uXaYizhdCmZYNLe8vdC3B7dSM8Eow+iihCAAK9LL+fPqgkENuNGnnZOHo9QmkluXk5ZtFhk7zEq20cQvhJ2yXzwUNOREiZy9cfU+Ip/En6LyyNyL0Te6sZYv/D/4CqmE3xnsfJDK5zcz4DmB3UL2ShdJcLA2nhkmmBnDX8lUVAWs1PI53mB3FACJdOTX3x4lMut3L5+atFxlGujxJBUPtSyPwBjDLYdwHZcm4CadL7mQqNPCBqZTbyWU6EonOludomroe4K4VXUhnYaq8p/+TlOFznHNZ4r/fXO+6iwbQH/LW2xcQEnDC1AheQQ/zFswGu2nmEPnAGwlUbSD1LoXT0s3GAM3gA+S5epM1iXvNWaZN7a57iBXkCJFU/UVjsqRLWPPnHdb0mPgQHE/EtlCKAOc0V/9PzvW6AugUv0SUNdtT1jzBkhTVqGgK90uAccZHmM73xC+vi7DvHDHEz4vRA+9P29y4TrOzuToTHflLbcVsyDsobo2I2ljOghIWZ9fZ0YhH2cLXKcoGeqrGCkj9LtJwQ8nNe9juxIASiz7p/asTzOH/7A9YTIZEpmXSN0klqhxx34sB+tSdHNcwcarZKkTYLmsYsGfg1lKM6DI/RP9sb/lttX5qw/Pqe73Vc7P4HIffBAH3vHLDLr9oIzih6P7aEx9fMlCDcjQmlLzpAi00UZbDgQaJpBnZ0cij4LPvUZkbS1sMpcJma70u9vZY+KuKIXC89Jee2gx52XpkfJ0DmKZmA5F1/qMxsYDJgYaFuUtKOdXV2Yoy38iwq+06cdfU34RcXqEV0aEpyqFE9GRrtkPTsADkyEZ5vfTf/NurMh7NA8UT5TzaMj4b6a7fRUomBv7LcJQl43Vje2SsHe/fL2+wrAM/DrETXSmj4zuTVflCAmqHEbZNk5QqDgRFBb6lAT0bBNu4E/W3Yk7bb2viACwhs8aoTyrW985tgwPip5gbAVpO1GGUfiOGbF9D+mq0uCcWfvT1Zl0qUtt/QvSZIuVrZGDDurNf6FanznJERgfzgVddJr+Au9ulicq/qUndSQALoWybnyKBIx4oeruIJDerzy5lURHSwxGQ/+D6QNf37VizaoAo4ks6nqp4UN8ccL2ZF43DH/7tllyST5HvP5ekWlQo6PW7ydGxyludslxfpekm2yKUEYE2qOtjEggLq6REQtLutToEMJlaCmX6GteeVi3noN63eIWmsqZjdLoM2a2AD5WTCm9IJgmNmPn3hxv6y2sMBX7NJ8Smlz/V1e8XM/ngO1fOZhBS0hnPLymD4pb4e3t4e2GrBMHxI2peZhUE+oqeWK0CSP714Hq8XcRlLj9oYVX5fNxq05itPFDh0qUkZ94CByJ+CT3j4X1fXv5oCC7VSjppn7itfH3FmzmPwaN/22ZRsQretM9yEHe+Xts4Gal6OH3l0oRUhPx3iR9kKAldVqWWlf9xV4D7DWCFKnYHlqigb/x/UZRRDvLpAXZWcImxl19PQf38AmsH9LmT5TFeTCozac0rVqChe2+F/w7o2jhQiTWCpRUc6ALrxCA/KxGSmkmJe5vSEvrJBrLyWX2JJcwF/ViSE0xGD6+ybgj8nucmjKoOB0AIBYNI9Rl4PWeVMmuPtL6JhaTRR0uA+T/Ojw6pKnA8AMhlvotY99lAuJYV5v6jjgGlfzGWczmsCcomiuU30+8BcN57KR1p1iHUXhmTO32AvFFgEc9Y92fPxRmwUrMHZHq5xpYwQYDdastXhdhjkJ1bLYrgALTzpmhu6UxvECxQnXmkCp98uOwusufMuJm9qJ5jUD6NlR088LL7T/Fcrr2vQIyD9+cHqFFtN3jF3S4g1/d8V5M6je4Nrdy9dMRL9sq3UbcZp/Dz43618whRw/ZhjWfzbyiKqeCm4Fz/GjgD1sE2XNzo9tq9cJ6ZwYR0Un4wYU+o6dakRgFU60UUYukCSLQ9tAf/pjl14IIRD2uc/V8jbH5boznL4Qv2jXrHgAaxBXtfdeAkvZl2sVFrbQsf9i/vzdnzveL2Rf4c4qbkLXrBAxT8gPF5Flk7H+rTkZKh6enoKvQJ56h/6nE7Uj5mP3Ypr96+1R4XIM6bXNG8ICrv/v/++XTQLJB8xXNK9jdRI6NxOSglv7Hm2vcna80X/GRwtUv+l7V6CeO07faJ7M1xSrFlRmI0VZ0KW3ExIeXnMStKL6L+fasRAvS9d9dMWMJpy1fbt2frulsGZms7EnioTxy+mnFwcEnSiYJhg07nBTRzGABDrZFfzoLgrVwishGigU4pwCTvYPgLpyNqjlZ62/UBVqiIb+Jo26bb3usJHxUTEyVNc1nPBJRLrEh8UevogXSwt+a2ORbQMt+5wV+LDKVAGmJJtTyLASryn99lTr4w83uL1J0kDC+P9897Pi3Ujdqcp4vSgUU3fB7YagNTc/jkgN/DuZYqpKsya7ZoZNYj/tnEqTZR/48X3/q0MH7XSL0NOTj4eeEh5FLsjKxkXlo8v486fDBHQO6HM0bDz1rKP/FwvCGBLrlqa03U2UbbEzKg+LXgTc1I95ohTNpfzl/IZ6edciJdwh0ExajMplLVx5jltl8tRsQATd0ayqCbgA3iZg19+ngpbBSg8d0e+mEbaQDLd+pQICX/ocVqdM/ch+dUyLp3A=="}]}]}, {"codeGareDepart": "206", "ordre": 2, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T07:10:00+01:00", "dateTimeArrivee": "2025-05-15T07:30:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 703, "numeroCommercial": "3", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:10:00+01:00", "dateHeureArrivee": "2025-05-15T07:30:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 3, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T07:35:00+01:00", "dateTimeArrivee": "2025-05-15T07:55:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 650, "numeroCommercial": "5", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:35:00+01:00", "dateHeureArrivee": "2025-05-15T07:55:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "imoGEc9ftj2IJdOIQ5OecucUT1f7lwdkhggb+ZGbG4XqXX1ctY1LZzxMFy8c4JZ0BVaEFvYwH7gM0a76j+sI90xhGCIoRz3CWJ7LkZUz8nArWjKx8PZEBmBIPFORA+P7M5EjD0czJXgMnlIJYU0lHe/hHahLBfaG03xQNlzuS1OSrYYrSfWp/ins+nTzH/gsupROKEWSLQer/FCotRSZIn1vL+zGGrQdpsjRjaZ5EFucUROUAFy+/J8N2UepP8/PWAp0CBSbBO185/6V5qEVoAWQL5bOYWGcNXGo9IJmkpv5Siu/0VFaCtp7FLljLK2l4GtxGADtvXpjtqC1TSQsC99WeV2tqYqYImk+CwpbnP/TGHWakcdvbAsj7Eu4JDY46q59nV9kYuCHo/LwZ+bi5+hYW4Tf2kFfuUprzUPhXWTBzsqONsgPXW5V1bTpxU46PHvo+70xg7gBxrs2pqbD3RAykd+X/UDeI5jwyagNlkqVawQAZnorJXym4UcpWA0KWKw5YBnerzVCUxAFs8jSy32Ua3YklQTmDTJj33NEyNmaHieq27/9z03mgiAcCSIds2aAJ8DwXXMMS6C+d0o1QRfCXsIMc1rFghp6L40crwMfN27ABEevbxfGg4vJ5FnDvenRhGfrAgBvzSzGLs66RF7cOZMuDTdsf/n8Ux8ZXMRt2iZ+lt/4ImY/5Gng9FNbedK76aTvTHkre9IdDx409UcvOgvuXVs+oz+0v6J8EZ8sezd6FpY8IbW7lJPmKpTDjDtgqEHKFbV+qUvDnmZnfVy2K0KOTA+PS553nQ/9b9yYPdficdHbbhDfc6URZdEgYslYVBXxig0JNgGlC48hYvS8++YEFEyQAn/Zx74gFCUEIJO45Mkb0V6LtG2IBbAaCUqh5xBnb2GcDEx6Ed+8MZISN8lCf+6Qyc8htmP7fetiKcTa0okttszeErYDtpXBDwJQnZJXG19xDku92dfIov7tfYqeLKxb2i12WUhOXUVXkP4FKPhOcD6cAe8CP1aKmUeO+xCrc7IJt2ogaAcJk4WgZxk6HJCJ/FbXE4e5yHORg7Ml5pEGJRcuyAmTbVQv4+kbe4W1odfX8GGJITyz2llaU982GLXm2OjGHdcWMt2XddGaiKlsVYIrT+TUs6RiFEIzY5GJNsd3RvIs0f/aZnf8L3IIuOG848oFvGkCbzVYvgNcXnBDo4lxNiTgt3URo/dNPTgUs58oKQHfB5NgV4A2BZpfIhOjuWM3pCGKSSKThF1uU8htmnLUYLMTde2InQKKWcmg9aXN2LFCOhoEun6B7/YO3ENtPzEjB3V5ueNp3UJL1MI8VVR67ElRy1KhAxFWI9GRS8tGUneTsW48uoI7vOGuuyHyuoLAgjhKYLp85g3m5y7/VXOg2YBn2g1KytCz8R5iDrIRig2oMITsd3dtdzggV4ptpXzX+SjT+AIqYvVgeWnOUO96BthGGkrpUf98Ckx72vAsiSXYjk8uY7ogNiCg1A10E9wU0WBHmSV07JO9nDNnfFTf0yeaLqsTC5hHPzHC/L+Kkr8r+JzPYYj6U1qeC14dBxQGkdqgkcgIr9VuMY/lvW2NiO+n2zej4zoS3F+jm/g98EWCZmD6KA3TyKBNesQ07rBrDhcBqYEgMZz+TsCLzHoD9djfDQXxpqXE8FGauQinZeGrbIruOga5rzUUnmznzpSn9xDqnG0JSNDN9ipuvVDlsP7Cz5ruaFgGS0jT2brczAp+iQukWKkYMWJ5wXEF+PWF4/R1qFsPYiHQ9sxejah78bQIMjKuYEq2ppPa8NMdP1WDYu088F6+FgKXssOUM8vHuix97/0KT0ZutZFBPsFAW8zzViE+jV3WYyLpo5eWROeGBc3EUo2xRj+a6ebe8R1Ggv2xYsMG2MY1CKJ8Bnnh45gsSQojDdlCL8MwdSBf65UvoMjFuS9UYPVnEFoR9iX/u+X+ZtqSHeYD3QsDWpoiYpQdvdOfLkl2Va+SUFT6bwoywQdgTopSQo24eA0uVmjPzNv4Xvf9nb4y+etHZw0uktAidyXfTrxgy1abSg0v4tBvQYAKQAWtURwu9JyTYhSyVZJ6wQIAhW7lyLY2grKDnRUzbWzROYg1WHaN+m6BlcgkDqi3aQcECOETjJ8rpUBoW+/fdydGFsbMH8DbVs598btrislyYbVL93pWC+YIvn8VEnIPBCBDV+2LhhD9OcT82uZdCUuDSmqM7TDvLhKj7IJsmZfzhhl/sqr2UT1q4OMFMKnpSwa1rNKcZob4lEWqMPHPUiJDOHLLjgMOtrZjbsIUku8GxasLETfXHYb21YKjhXWJVWinUQJJQf2gqJ+qZnuNBjO2lPeasIt8cE95jwZNIVQsutkTyqnWG1s9F3Y+OY/ePbPTygJhgrVOoPUOWHIE5VMuN3ixgWqU6ROYXLNtTcsacwe7O2d2bFO6I2JTTMe23XTzAzkgele9CwLGDJX8JevrUU8GarAKHApQgjNIdY75r+LsJeU1Eu/iIy5diFtJYWQY3PziXR5vrmywyd+pI6/b7/dAPReCYIMbptTIIN7SucO3fWrDtU0xLp5MgyDCFvgLY44R+YpRzAVPX5i4o+c24txHZvAerc16ke7AoUddGCPs78b0ldBiRoWtpNpwcCgp/3+ZPMs7kSiGGfxTJkNi4wFd9O3t1BMY0lNRE64Qq546FlN4dx1kwwPVAC5+8RUF9183L2HFZTjhDkD+GbmxZm7c14uQw3URePTD52S7/iNSbGINqPfhy0xUbGlJPb04n/QPsxIDnIKMRCOW8JxlhAKVb16/rPqMSC3lwmYUlSDT+gBR4PJvj8+vrndd8MyO/7Hlr6IYJc/5Ium0S4T06GGZvg82pDJ3rdguoBDBAhjnp/IO0u11SuteG/Yk/DLd+3yP6DXrUkkVeIdadmXcFQwAWfJ8I3JDoEcvfyzcGV51FsyPpFRHcmgabt/MDMhOY/60/qtdF8nYMmHtA37WAWlypUQotGWn8Ei8dROs4XyTKuTX8Vkg7fZnHXju9nyKxwb0peVs4doYK+gv0zNheLID0WEhEtttV+GndNL/4CREE55pseCV1JIdBsReDgdCALy65qE6e+Od2TVjG3VUlxTO8k/zMLDYBc6/pGF7bCGoHT8hogDJ7/mybA8Shh5PEwssHvlrYHK0e6vi+ZC8k3xSDj1DOqYS6gmSWDzC04FYzji1xtNqoRqMGj4AOaueeN+leo/kps8HqcUZRsjXF0ZK+oDjezc4UPf0wCjh0MNqE29XRGPMd6lMBwxDP/WDDEAb/B3RqAUtFNgZkHkXsry0vH36bVf5jmv5jZO2PyyaRABXz1LQD8OGF5Xu5CI6ge1TdIPoeky6k37xWDuwIdYu3wT2yVOvpo36TV4Hd/w0mkt4VAQ31gSYXCYPYxQTd5+ai50mJuGJvWmdv03Tu/2DgAVFbSqqmDfdpAaBQUU0Hs3DNQObBxWzpLHm2fLIDj/ZSyf8N+63uCjxjZ3SXBDObiH1bpTQ8G1dlkRVQE5OqhuKWyQT9DlzcIRaNgTM+xYvERKlnUO8KSYqd4kVg/w9TfQma1qFDcERvbTHQ8fw3Z/9f/bBQ4i0ur0LoLKtrR+MFL3/1eBpq3ot0HEERUhjN8lEWmO+nBnxzJVrGhaihvOL9hc55RCqigcdXuEbdrWDKZ6N+g81K0lvRhkWVsC/L9W6g2cXB35M9gfOpMPORDnNVV8ZUrUuhNbElHLDWPqYWsSekAwALfpi6yKQIbLMrT+jQfQyLjpewa5+a+9wXDz4eAOw/gW00jv2nXi6VNi98tj0QVAvfQvjta8W1Q6vTEWzPazSQVcEgtp0spyRIG0VhvQ1WqPhT46AOjHWheG5ljBfTrKgbtdGgzOIRWgZV7P6Y1KvWPkij7KPnRL9ypaQH16DiY+nJC+sxYPOBDJ03YuE4zd2RDAYN8Y1p0njAAOGGDlEZrDkVlvBYaP3HYu0FJmXesDU3xsDEsNWASi4vPg/gsGWkwlH4Nt4N5KIrduQacdUpOQa8ApM9DOvCR9tcPrnRGK9NK5dzC+KZwL/h46PNe2cLBJEeJoqMq3xYxV+rwEkkvRDGRF5EPNBFd81I0lKl8iMmVBmzjoe1b48Nu/3E6RSRotvfM9T0Yly2e1GuQl4Ox8s6204apG7PkaVfoVY7uHTwoy8+hzTXQlz+1mJe4giZpWcMC+cZRL6uKruBiDPGWGZs/BfCYz1P3nBTNoPGg6VeifgMmRJRMjeR0NNpAwrEebUV6Ff0+8ayNC1lk/wpk0pyZ+1p61HNv1OPcwHs6k9O+heE3fhsdn9VqZ16oZon8kWJ57w8cqGT1IaHvd8Zh0AcUfh6mDrtt9zjihIcR/P0N/aLj8bBxpFfWbmZ3L38KBucAVdmGCksIB7Wyxwz/2iPQh/NrNua4TTvb0PJo8EaP6Y+ewR5kmI77qepbk2vF+KH6cBGSBGcvIhtz5ECES0QoW5BMZKD3v6D1kr1Y904hOoH6K0v5PVfQNn5+85qbkMCnSdTOYhhFac+RjpWEoBjbLVZAiDNhAGu/CG++Qsh8vzhccaCIc6+g98/7/2B3KxzLA+uDTImzkb7PgJmwvVrwmKgcmmCKzKV39YerAQHFQRHBE0HvdttzeJ3srV+pbMD4HyQc9zbe1u+iiXrleX/6DXCS4B8WoYwh8yi51uWfCGY5BzDEcvrz9tpSVRFI09aThW1y46ZFN/Ex5d01rNbjYabvC3fs0ScFGrO3eQJwG0NT5DeXCQ2eilnLgeFK0e3vvrz99YinKizfkBaBzPxMCajl02gOTmuHmsxYXYuvEODn9INEgB/QYCPhowY7qRc+44AnpTAgziQ+lKANAqvvFRHQPkhkSes9WBToSg8BwBcWYoYM+rY7UC96W9wpv7K9zTz7YpUSTtOAohpB5E0HE10lkCX41w0f2lmFFW3lMcyLcUCLqe3rZ2nZDeAfXlH6ZydIUs//GLkoVuQnUZzQTRKVzLrU73DJ893YEY1MgtXM3YUJgoBRCdXtMFppqbndpNvGA3NbEkIC3EfPpWKykr15TerJimVC/WB/lK/IPqt9qeUxI+Z+O+H0RxZvaGXtJeEbGCnGpOHOmZ1/74EOzGxjsdNQ6Lu6UHg/cU582/NKGQzbA9EZZqBBzssNFZQoanmITgH2yNZyuV5GhDNGfkLcZABG7DwUgkBN8XdTyzqvaQ3cER0FdMq3qBi5ZlF5wCa736HsEaGdS8+lXKUQIp5VTPwSOOX0Lge1eSwIH1SJvYKKooGkjXCBHIqL7XL2pY+URPoI7nY6Q9kgCoNxfDmRMY9dPVqYK56treDccWTDemRWQmszEb7vsLRx1tzN0CA4MfWtCJt2EuT3RNfrNjFElYkaNjRnp6rXWXqxuDo58xEAS3IYc93UVMiRI40Du3HiwOW+Up7GkmIYuObAFaaFNVRTLHKXP1N+AiuMIslAu7qywlUhQt29SUlqY/81nbvIyYF0PoXNQ4Yi5R+DOyRtj5k+8PZx8mkbee8NKUfkS++j0ZagQUujxVFe0w205PaGmRlO29toEZwbViQyfU2hW0PbU2gSx7Z09JLk31CehuTC4/EKLc1tToWxQ3IeYT5Hkc1blrJEQc9Z3+OxpuuuajclJfY/fnhkN5d5gQTSNgS0DMo2VoFgCa5fG/sokGvFjaF7CSdXCgG81F0N2MQSV9Tt1xVqH+mXKYnDGeewDZ7y8g8eNyJbSoK2Oym+l98e/LV+sPN8NSlYxp22IG4/1OziJXEt6Tf8C1d4WPd/F8CYJHQc24eNdFAAwSiHZvORf0469vpHlWnv+Vn9yadDxEs/iYTX0xIllenssuzc6IH+gd5y1dCqMbX5vrd6qVkv0sDRM50+xocBJLFdmNIV/t0tvi0BUQlssH27zaHtpy1SMpgCGB1G9gd+ONMMOkxE7BohfYH2dtkHbya6NMtnjgxsNP7PEwja1s+42SjvXvLy4J4IFj9dWlN3wFxfnwHhga+hTQch7gOPZuW0XraBiBiGvj6u94XmYMHnhjnEEFn+VUahKdKlHZyEzCeH7N6OQGrptveUqdci8m6lLnnIDNrQDSnsNGQHcvP4KVo9QYlyA/NrD4RNiAMZhENgxW6c6qJiI4FU1iwoPTSW5rH25s2KZnL9Rjeek0uGjDS5nrFC301qQY40gutu8QkyXCZQizc1XGN2oTVKs2XE0+K42HHeC3w0wT8PtwyvneH3foAm3vSB+Sra6f0/lumhtEoD0bfUrONBx28SEDXljVWE9C6yPv819L/WeZ+acBiF3ROifyaQQ+nFz14r+FoulA7O2hdUKYH2iuRl106XbmhmlID7I3RtxPcNAnO5K9fe9QfYsw8ZC7NdFM6ioxwDHc269Gc8k9qhe7p2NZIOi9Bcxt4GPo5+IXNpaAnFjptlDudd5HA4Y/v6Ta55JObC59zjvmW8xF9gLt3ViBQYljEulVCDAZ8N6qd5ueKJhMI/mgpjvD8C1vfUgpxUDyEZafX7Ajnh34W4vSECXAo8FEjnirOVn1k5D3gzVq34yesJpoPLXUVdd16HaumYs2dx9vITnmtL9KgevTdeZQrLhudXYX5i9uVgARPTXVtV3lUouhAxMcilJ+Z8T6DN0CYyD/9QaJh2XiOEIK+oZQRRn81OQU9T+thb6wC5NDHaPm76y1+5pOer+pj4hi9r6ITbQybtaUQXC6P8m21IIFoVT0hbKDPoEVZcEWp8dmCl20IPsSOs7AkWOGU7AjS1j89NAtp/ANDsw6s1Ubmw9a0ClwxDdGLSqKo7w2yOH053wXpWobxn8cLJNUxD/snHV1kXOyNCDG2+6lKl+FrkkcpQPBnLqP2CAXa0DXhFXNzMjs2ciGZnH7znJgM9NgMTO1XJwUV4KlxL8jujqLLy3v9r0+8mKaQCXAxFuPEA/fF1tGTz3Z2ScNNYEoajFHbhsm4h3EEQAvdlLwmD5pXAg6h7oTeEea0y3CxZ2X8G3k+8pMLTJP18OTwWfEbrebzUONyApQSfDIllwCXpFfy7erFnL0Hd5yUkSxDt2RpIKRcI+jbKhfh/KMdjE5/A4OYAeJQBZx3YGk87gqEPD13ovdOInbiqW8vRjwiAIboVbsIuOtQtnJcPKKnhLYyRXCZSxhlGTWprlUNi3VUeltsRdnGGsu4IvjDM3pFIWcL0B3K9TFfi7h1Lr3rmAuB6079gNLbkE94kOMnP/ZQ3mx1ocRKO/n6KRLhidQBGiZvJMdm7iQmF/8eTyYo6yNvPeh9tBEKx939z1O2BAz3jl74Vz64OECFv0gtD0fpO904qf/v2aOGqFFGnEoC3JegRR92DSohlon/KL4LpE/UMFOOUjxEdqxRZa0JhX9kWyxSpxMfwzO1KZGv6FTeWjoubnBogVWB2tg3KoUNCVl7hqKnY9q56zhuIa6Ktt61OnKRRFgTd0glIlGPi3uqn7T78B3OAZTN1h1PIwb4Mrf+x+OHnPVYGtWZiwIqpUJpPO4ZzszcHk0D9r9if3wWfe9fprXmff4aEP8bvGqsnOjB6TbmMYwUM/N+slzYPSjrslqhJE4xJrX/gctCA4pipyfK/mWfENYkZa/PYPWcPgyWc+09Jl3B4o1TmZrONKR0kSYiiQU9/nswV7Dh+TZmqgikIFpYXtu2m9HS9YnRNpAeV3XA2vHl4HlDumEwTmr5vSqtRwyp7SLlfL9bogHXJoq3ApK9Khwodl0HRGgqANxuKUjiTf6vUj1NmUzCX/2x6PB+R6i+LOw8FIrxCfYENjKzDRq7QqrLm1iWuQzDsuBEirpLvkE5NFjBYfZ3fFSspzVYa2Pu9IFD70+21IBF/Ty8VkBSstQSAUhV0U2rju+E0Ie1TAGQF09tbiSE9B+owlcbM3KKgq9LXFaFf3LCzkc+w4btr4orxe36tQBRL5ZzPnJ8D7p/bIQuCsqwI/0NcRtQ4gcabfhLpQJ8/yarM/4pJkwiNQuK3kn/R5RTRf8vlBk6+t5uyzwhtRCYhSjeog06OBGCq/vPp5Gr/W0X7weQV1At7qgvqGShHEk4SQbGkUvjsO9gLLfgQtg/VQCVdzTO4YEVVeAvhoRZQHMEeb9yeNKElIK7A3IUTHNoUjDtpgmc0L2+ipyqhOHMeU5Vnv9bxXQsy0bO+x8il1xEdjxGgYYiaAYfJRcPSXkdXPjlidjCm/SDjTdHDMhnNcPuglYWlextENKFxIrosizytRQEmsZVFyk9QcS0roIeS7Mota9UNIcO7a/Oyh+KP/SXN7J/V17ubGqCIWUIaVUP+YCSbj/3luzaygql/8hZzZb394nRcI96q6T8ns0ZGMnh/3alPrkAnTU1g2TtuNYopVktBgDPYzZCpLPew39ZifZcDu4wfExE5uyiL+/SiXMnHqJ65VN3HfpK6JCNsTQFIlSjCk27mHTKO+yz0oS06slescrEjVtUL+aIdgIjv/azw2xXRr63jZWkqBO2mVkj/KUMey73fmEnnFuN3J0GOehrC54HYDLJ5BaQ/St0brP5zhpUns1/GF56DCYVx9hELAuYGlBHuFTAYElztwbSZFnVkIP/gUBr8WOabjTjEtAMdMqx/5CYWroJfnP3V3noVA2ONzPyJ3FcbqCSAlu38oO0cxoIOKjqNrsALchXZdgMjM1KTUsSAUWhclqPW3dLLe85gB/WjPCPixRK5aisBDG8SqxGOPafhefLdCMfKVQIxyG8MVLut00b/JxvTuzW6iJepvFhcsmCSPC5W2eJ4ntE5ejS1h2sZVywQ0vMJGNPtT4JM3v3t4oKiFx6imVJ5y1aCMR48rHsRLmhJHq7ZsWqfI05UM5Byz2kPT/sjn08pU81pwJeNPj8wb4QHkztvw0FrUFevk1IEbVMq9Y+TLo2bdFy6qRUmKD1tpyMSw9UDhIBQildwQ8Xw6nbGSvOLC9YttkrvHLlaZpdV6W755cPot1z1bvWSI65e+kp50qAC7bV/UToAJjLOyqF34HeLo1sHJgytN8mpSP7puMjaT+R1gLbFEeGvibhG81wZx7LrA8ImKq4lUriMtkrkz2L2g+dXdvrIbFpqJnAWPcTnX0HqD0yLGmEE/B5g2aZw03AFv02n11wMqJxpxG9+75OZf7feF+jNYIwbfXym5JlmZz1zzAAHiZJa8kGtVX7D2cZikmYFh3qDS9XuzOKCWouG3Ldg1MWQIGPuRSlLgpCPwyBCaXBnOY+MpZHRH88Cn9JrdPLS0cmzl588K/KaGmJkpVDVPd/5IXJYtxKvNYNEosviOAK9wKE/Y/+IrI9pgPSTV8S4Qxl7LYKM/ZvhuLVR1VjZdFLKDu1ObzTGYaZMLLabvzn5EE1epRZE9DCG/7givpjwnr+Wh7sj7LXLxppYInRMPWBksdzkP15ET9QjG3xFuS2MAiD7c6pX/7frUB3uezFhErvjD1GmAhhAJupBirtUihRNu6lrydNfrhZ2KSs3AUznhkCSLrnlvH8oAcmS9z8ZsYiRehEquvz97tu0F5HZh/Un9zOEYeBd1iW0iB6rE0B5nNEOaNk3FvzJpBSs9/sKIYPU3vdcCmNpTgJsGPU1qUDqegulyyoz3o0IH2YdSPtz9SP4w2jP+ssl4Jy+9cUdSvAyzvzpbpd/UbVSHDWPAT+H83S5uJbcCptVkiaIRgmpuosleDW50Y5bFHaWsW5ma/VunOaz+udgL+8Z2J2CEsrWVqjrW+fpIuWGQIZeKHhoptDoUth8Byph7VgAW+/s8ca6bqzzb8JuFdsdhY7+0OlHRxBtUe4Ipj2sLOH89HMX8Yzaaod+HBJLAY1F626DANOtbCzp/J0Xy0fAcjy2GtxyyNYnTPxib1zy8erO6XO9W0Qmrgk62BoJTQvWSEiogQ4STWji3mFsIk8eE0KR5Sjq/jLTGtBj2STaYlRaIS+VOdR1Xj9WwkN+QmPHj+Um1s6eQWipJRVd22vO/8Fejt2+c5k0BiNnuAENqoQe1klDPBUjWiRoOMX7tVFdm/ZWqoK3S8kdCZEWzOCVduDLk4vHy+Gxr4Bn7eLejZRDWUq4Iw0pzTNFuFlo+2BRnAD94VVBOjssbqO39fPfH3Wny1WVxeIri24z03c7aRbzoTF+YjLa8QqemOmed7OuvtAHUMAFCX4Y21cFtya8nhOfqBPi9lmopLgq1HjkseVL9SHWphndS18AAARbOXwxNFx+z3GeExtw=="}]}]}, {"codeGareDepart": "206", "ordre": 4, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T08:10:00+01:00", "dateTimeArrivee": "2025-05-15T08:30:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 693, "numeroCommercial": "7", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:10:00+01:00", "dateHeureArrivee": "2025-05-15T08:30:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 5, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T08:35:00+01:00", "dateTimeArrivee": "2025-05-15T08:55:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 644, "numeroCommercial": "9", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:35:00+01:00", "dateHeureArrivee": "2025-05-15T08:55:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "COV", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 6, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T09:10:00+01:00", "dateTimeArrivee": "2025-05-15T09:30:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 667, "numeroCommercial": "11", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:10:00+01:00", "dateHeureArrivee": "2025-05-15T09:30:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 7, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T09:40:00+01:00", "dateTimeArrivee": "2025-05-15T10:01:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 6458, "numeroCommercial": "V10009", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:40:00+01:00", "dateHeureArrivee": "2025-05-15T10:01:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 8, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T10:10:00+01:00", "dateTimeArrivee": "2025-05-15T10:30:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 636, "numeroCommercial": "15", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:10:00+01:00", "dateHeureArrivee": "2025-05-15T10:30:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 9, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T10:35:00+01:00", "dateTimeArrivee": "2025-05-15T10:55:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 638, "numeroCommercial": "17", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:35:00+01:00", "dateHeureArrivee": "2025-05-15T10:55:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 10, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T11:10:00+01:00", "dateTimeArrivee": "2025-05-15T11:30:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 639, "numeroCommercial": "19", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:10:00+01:00", "dateHeureArrivee": "2025-05-15T11:30:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 11, "codeGareArrivee": "217", "dateTimeDepart": "2025-05-15T11:35:00+01:00", "dateTimeArrivee": "2025-05-15T11:55:00+01:00", "durationTrajet": "00:20:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 666, "numeroCommercial": "21", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:35:00+01:00", "dateHeureArrivee": "2025-05-15T11:55:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "217", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:20:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "CASA PORT", "to": "MOHAMMEDIA", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}