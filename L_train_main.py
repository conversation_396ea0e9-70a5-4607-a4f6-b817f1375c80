import os
import psycopg2
import json
import glob
import re
from rich import print
from configuration import db_params
from dateutil import parser

TRAIN_MODE_ID = 3
ONCF_PROVIDER_ID = 1
COUNTRY_ID = 1
ONCF_CITIES = 'oncf_cities.json'

TRAIN_TYPES = {
    'Al Boraq (High Speed)': {'name': 'AL BORAQ', 'line_number': 'BORAQ', 'color': '#d32f2f', 'train_type': 'high_speed'},
    'Al Atlas': {'name': 'AL ATLAS', 'line_number': 'ATLAS', 'color': '#1976d2', 'train_type': 'express'},
    'Regular Train': {'name': 'TNR', 'line_number': 'TNR', 'color': '#388e3c', 'train_type': 'regional'},
    'Unknown': {'name': 'Unknown', 'line_number': 'UNKNOWN', 'color': '#9e9e9e', 'train_type': 'regular'}
}

JOURNEY_FILES_DIR = 'processed_data4'

def get_db_connection():
    try:
        connection = psycopg2.connect(**db_params)
        return connection
    except Exception as e:
        print(f"Error connecting to the database: {e}")
        return None


def import_station(path=ONCF_CITIES):
    try:
        with open(path, 'r') as file:
            station_data = json.load(file)
    except FileNotFoundError:
        print(f"File {ONCF_CITIES} not found.")
        return None

    conn = get_db_connection()
    try:
        cur = conn.cursor()
    except Exception as e:
        print(f"Error creating cursor: {e}")
        return None

    #getting all the cities from the database
    cur.execute("SELECT id, name FROM city")
    cities = {city[1]: city[0] for city in cur.fetchall()}
    # print(cities)

    #getting existing stations and locationsfrom the database to avoid duplicatation
    cur.execute("SELECT station_code, id FROM station")
    existing_stations = {station[0]: station[1] for station in cur.fetchall()}

    existing_locations = {}
    cur.execute("SELECT lat, lng, id FROM location")
    for row in cur.fetchall():
        try:
            if row[0] is not None and row[1] is not None:
                lat_float = float(row[0])
                lng_float = float(row[1])
                existing_locations[(lat_float, lng_float)] = row[2]
        except (ValueError, TypeError):
            print(f"Invalid lat/lng values for id {row[2]}: {row[0]}, {row[1]}")

    #keep track of ids because we will need them to insert the stations
    station_code_to_id = {}

    #keep track of the ids of the stations that are already in the database
    station_counter = 0

    for station, details in station_data.items():
        try:
            # Check if the station already exists in the database
            station_code = details['codeGare']

            if station_code in existing_stations:
                station_code_to_id[station_code] = existing_stations[station_code]
                continue

            city_name = station.split()[0].upper()

            # Casablanca is a special case
            if city_name == 'CASA' or station == 'AIN SEBAA' or station == 'ENNASSIM':
                city_name = 'CASABLANCA'

            city_id = cities.get(city_name.capitalize())

            #validate the lat and lng values
            lat = details.get('lat')
            lng = details.get('lng')

            if lat is None or lng is None:
                continue

            try:
                lat_float = round(float(lat), 5)
                lng_float = round(float(lng), 5)
            except (ValueError, TypeError):
                print(f"Invalid lat/lng values for station {station}: {lat}, {lng}")
                continue

            location_key = (lat_float, lng_float)
            if location_key in existing_locations:
                location_id = existing_locations[location_key]
            else:
                cur.execute("""
                    INSERT INTO location (name, city_id, lat, lng, type)
                    VALUES (%s, %s, %s, %s, %s) RETURNING id
                """, (station, city_id, lat_float, lng_float, 'station'))
                location_id = cur.fetchone()[0]
                conn.commit()
                existing_locations[location_key] = location_id
                print(f"Inserted new location: {station} with id {location_id}")

            # Insert the station into the database
            cur.execute("""
                INSERT INTO station (name, location_id, station_type, is_transfer_point, station_code)
                VALUES (%s, %s, %s, %s, %s) RETURNING id
            """, (station, location_id, 'train_station', True, station_code))
            station_id = cur.fetchone()[0]
            conn.commit()

            station_code_to_id[station_code] = station_id
            existing_stations[station_code] = station_id
            station_counter += 1

            print(f"Inserted new station: {station} with id {station_id}")
        except Exception as e:
            # Rollback the transaction in case of error
            conn.rollback()
            print(f"Error processing station {station}: {e}")
            continue
    cur.close()
    conn.close()
    print(f"Inserted {station_counter} new stations.")
    return station_code_to_id


def create_train_lines():
    """Create train line types if they don't exist"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get existing line numbers to avoid duplicates
        cursor.execute("SELECT line_number, id FROM transport_line WHERE provider_id = %s", (ONCF_PROVIDER_ID,))
        existing_lines = {row[0]: row[1] for row in cursor.fetchall()}

        train_line_ids = {}

        # Create train lines for each train type
        for train_type_name, train_info in TRAIN_TYPES.items():
            if train_info["line_number"] in existing_lines:
                line_id = existing_lines[train_info["line_number"]]
            else:
                cursor.execute("""
                            INSERT INTO transport_line
                            (provider_id, mode_id, line_number, name, train_type, color, fare_type, currency)
                            VALUES (%s, %s, %s, %s, %s, %s, 'variable', 'MAD')
                            RETURNING id
                            """, (ONCF_PROVIDER_ID, TRAIN_MODE_ID, train_info["line_number"], train_info["name"],
                                  train_info["train_type"], train_info["color"]))
                line_id = cursor.fetchone()[0]
                conn.commit()

            # Store the line ID by both name and train type name for lookup
            train_line_ids[train_info["name"]] = line_id
            train_line_ids[train_type_name] = line_id
            train_line_ids[train_info["line_number"]] = line_id

            # Add explicit mapping for JSON format names
            if train_type_name == "Al Boraq (High Speed)":
                train_line_ids["Al Boraq (High Speed)"] = line_id
            elif train_type_name == "Regular Train":
                train_line_ids["Regular Train"] = line_id
            elif train_type_name == "Al Atlas":
                train_line_ids["Al Atlas"] = line_id

        print("Train line IDs created:", train_line_ids)
        cursor.close()
        conn.close()
        return train_line_ids
    except Exception as e:
        print(f"Error creating train lines: {e}")
        raise


def process_journey_files(station_code_to_id, train_line_ids):
    """Process JSON journey files to extract fare and transfer information"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get all stations with city info for better referencing
        cursor.execute("""
                       SELECT s.id, TRIM(s.station_code), s.name, l.city_id
                       FROM station s
                       JOIN location l ON s.location_id = l.id
                       WHERE s.station_type = 'train_station'
                       """)
        stations = {row[1]: {"id": row[0], "name": row[2], "city_id": row[3]} for row in cursor.fetchall()}

        # Find all JSON files in the specified directory
        journey_file_pattern = os.path.join(JOURNEY_FILES_DIR, "*.json")
        journey_files = glob.glob(journey_file_pattern)
        print(f"Found {len(journey_files)} journey files: {journey_files[:5]} ...")
        if len(journey_files) == 0:
            print(f"WARNING: No JSON files found in {JOURNEY_FILES_DIR}")
            return

        fares_created = 0
        transfers_created = 0

        for file_path in journey_files:
            try:
                filename = os.path.basename(file_path)

                # Skip special files
                if filename in ["all_train_journeys.json", "retry_http_results.json"]:
                    continue

                # Extract metadata from filename
                match = re.match(r"(.+)_to_(.+)_(\d{4}-\d{2}-\d{2})_comfort(\d)_(.+)\.json", filename)
                if not match:
                    print(f"Could not parse filename: {filename}, skipping")
                    continue

                from_city, to_city, date, comfort_class, time_of_day = match.groups()
                print(f"Processing {from_city} to {to_city}, comfort {comfort_class}")

                # Load journey data
                with open(file_path, 'r', encoding='utf-8') as file:
                    journey_data = json.load(file)

                # Process each journey option
                for journey_idx, journey in enumerate(journey_data.get("journeys", [])):
                    # Get the standard and premium prices for this journey
                    standard_price = None
                    premium_price = None
                    currency = "MAD"

                    for price_option in journey.get("prices", []):
                        if price_option.get("is_premium", False):
                            premium_price = price_option.get("price")
                        else:
                            standard_price = price_option.get("price")
                        currency = price_option.get("currency", "MAD")

                    # If no prices found, skip this journey
                    if standard_price is None and premium_price is None:
                        continue

                    # Store both prices - we'll need them for each segment
                    journey_prices = {
                        "standard": standard_price,
                        "premium": premium_price,
                        "currency": currency
                    }

                    # Parse full journey times for transfer calculations
                    try:
                        journey_departure = parser.parse(journey.get("departure_time"))
                        journey_arrival = parser.parse(journey.get("arrival_time"))
                    except Exception as e:
                        print(f"Error parsing journey times: {e}")
                        continue

                    # Track segments for transfer creation
                    journey_segments = []

                    # Process each segment of the journey
                    for seg_idx, segment in enumerate(journey.get("segments", [])):
                        from_station_code = segment.get("from_station_code")
                        to_station_code = segment.get("to_station_code")

                        # Individual station checks
                        if from_station_code not in stations:
                            print(f"Skipping segment: from_station_code {from_station_code} not found")
                            continue
                        if to_station_code not in stations:
                            print(f"Skipping segment: to_station_code {to_station_code} not found")
                            continue

                        from_station_id = stations[from_station_code]["id"]
                        to_station_id = stations[to_station_code]["id"]
                        city_id = stations[from_station_code]["city_id"]  # Use origin city for fare

                        # Get train type with correct mapping
                        train_type = segment.get("train_type", "Unknown")
                        train_number = segment.get("train_number", "Unknown")

                        # Map the train type to match our train_line_ids keys
                        if train_type in train_line_ids:
                            line_id = train_line_ids[train_type]
                        else:
                            print(f"Warning: Train type '{train_type}' not found in train_line_ids, using Unknown")
                            line_id = train_line_ids.get("Unknown")

                        # Parse times
                        try:
                            departure_time = parser.parse(segment.get("departure_time"))
                            arrival_time = parser.parse(segment.get("arrival_time"))
                        except Exception as e:
                            print(f"Error parsing times for segment: {e}")
                            continue

                        # Calculate duration in minutes
                        duration = int((arrival_time - departure_time).total_seconds() / 60)

                        # Add to journey segments for later transfer processing
                        journey_segments.append({
                            "from_station_id": from_station_id,
                            "to_station_id": to_station_id,
                            "from_station_code": from_station_code,
                            "to_station_code": to_station_code,
                            "departure_time": departure_time,
                            "arrival_time": arrival_time
                        })

                        # Process standard fare if available
                        if standard_price is not None:
                            # Use full journey price for direct trains, keep as-is
                            # Don't distribute prices proportionally - keep original values as requested
                            cursor.execute("""
                                SELECT id
                                FROM transport_fare
                                WHERE mode_id = %s
                                AND provider_id = %s
                                AND from_station_id = %s
                                AND to_station_id = %s
                                AND service_class = %s
                                AND line_id = %s
                                AND departure_time = %s
                                """, (TRAIN_MODE_ID, ONCF_PROVIDER_ID, from_station_id, to_station_id,
                                    f"comfort{comfort_class}", line_id, departure_time))
                            fare = cursor.fetchone()

                            if not fare:
                                try:
                                    cursor.execute("""
                                                INSERT INTO transport_fare
                                                (mode_id, provider_id, from_station_id, to_station_id, city_id, line_id,
                                                service_class, price, currency, duration_minutes, departure_time)
                                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                                """,
                                                (TRAIN_MODE_ID, ONCF_PROVIDER_ID, from_station_id, to_station_id, city_id,
                                                line_id, f"comfort{comfort_class}", standard_price, currency, duration,
                                                departure_time))
                                    conn.commit()
                                    fares_created += 1
                                except Exception as e:
                                    print(f"Error creating standard fare: {e}")
                                    conn.rollback()

                        # Process premium fare if available as a separate class
                        if premium_price is not None:
                            cursor.execute("""
                                SELECT id
                                FROM transport_fare
                                WHERE mode_id = %s
                                AND provider_id = %s
                                AND from_station_id = %s
                                AND to_station_id = %s
                                AND service_class = %s
                                AND line_id = %s
                                AND departure_time = %s
                                """, (TRAIN_MODE_ID, ONCF_PROVIDER_ID, from_station_id, to_station_id,
                                    f"comfort{comfort_class}_premium", line_id, departure_time))
                            fare = cursor.fetchone()

                            if not fare:
                                try:
                                    cursor.execute("""
                                                INSERT INTO transport_fare
                                                (mode_id, provider_id, from_station_id, to_station_id, city_id, line_id,
                                                service_class, price, currency, duration_minutes, departure_time)
                                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                                """,
                                                (TRAIN_MODE_ID, ONCF_PROVIDER_ID, from_station_id, to_station_id, city_id,
                                                line_id, f"comfort{comfort_class}_premium", premium_price, currency, duration,
                                                departure_time))
                                    conn.commit()
                                    fares_created += 1
                                except Exception as e:
                                    print(f"Error creating premium fare: {e}")
                                    conn.rollback()

                    # Create station transfers for segments with connection times
                    for i in range(len(journey_segments) - 1):
                        current_segment = journey_segments[i]
                        next_segment = journey_segments[i + 1]

                        # Check if segments connect (current destination is next origin)
                        if current_segment["to_station_code"] == next_segment["from_station_code"]:
                            transfer_station_id = current_segment["to_station_id"]

                            # Calculate transfer time
                            try:
                                arrival = current_segment["arrival_time"]
                                departure = next_segment["departure_time"]
                                transfer_minutes = int((departure - arrival).total_seconds() / 60)
                            except Exception as e:
                                print(f"Error calculating transfer time: {e}")
                                continue

                            # Only create transfer if time is reasonable
                            if 1 <= transfer_minutes <= 180:
                                station_name = stations[current_segment["to_station_code"]]["name"]

                                try:
                                    # Handle transfer creation differently based on if it's at the same station
                                    cursor.execute("""
                                        INSERT INTO station_transfer
                                        (from_station_id, to_station_id, transfer_time_minutes,
                                         transfer_type, is_free, transfer_instructions)
                                        VALUES (%s, %s, %s, %s, %s, %s)
                                        ON CONFLICT (from_station_id, to_station_id) DO UPDATE
                                        SET transfer_time_minutes = EXCLUDED.transfer_time_minutes
                                        """,
                                        (transfer_station_id, transfer_station_id, transfer_minutes,
                                         'internal', True, f'Change platforms at {station_name}'))
                                    conn.commit()
                                    transfers_created += 1
                                except Exception as e:
                                    print(f"Error creating transfer: {e}")
                                    conn.rollback()

            except Exception as e:
                print(f"Error processing file {file_path}: {e}")
                # Continue with next file instead of aborting the whole process
                continue

        cursor.close()
        conn.close()
        print(f"Created {fares_created} new fare entries and {transfers_created} station transfers")
    except Exception as e:
        print(f"Error in process_journey_files: {e}")
        raise

def create_line_station_junctions(train_line_ids):
    """Create line-station junctions based on fare data"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get all fares to determine line-station relationships
        cursor.execute("""
                       SELECT DISTINCT tf.line_id, tf.from_station_id, tf.to_station_id
                       FROM transport_fare tf
                       WHERE tf.provider_id = %s
                         AND tf.mode_id = %s
                         AND tf.line_id IS NOT NULL
                       """, (ONCF_PROVIDER_ID, TRAIN_MODE_ID))

        line_connections = cursor.fetchall()

        # Get existing junctions to avoid duplicates
        cursor.execute("SELECT line_id, station_id FROM line_station_junction")
        existing_junctions = set((row[0], row[1]) for row in cursor.fetchall())

        # Get station names for error handling
        cursor.execute("SELECT id, name FROM station")
        station_names = {row[0]: row[1] for row in cursor.fetchall()}

        junctions_created = 0

        # Create a dictionary to track stations for each line
        line_stations = {}
        for line_id, from_station_id, to_station_id in line_connections:
            if line_id not in line_stations:
                line_stations[line_id] = set()
            line_stations[line_id].add(from_station_id)
            line_stations[line_id].add(to_station_id)

        # For each line, build a more complete path of stations
        for line_id, stations in line_stations.items():
            # Create a dictionary of connections for this line
            connections = {}
            station_counts = {station_id: 0 for station_id in stations}

            # Count connections for each station based on fare data
            for conn_line_id, from_id, to_id in line_connections:
                if conn_line_id == line_id:
                    if from_id not in connections:
                        connections[from_id] = set()
                    connections[from_id].add(to_id)
                    station_counts[from_id] += 1
                    station_counts[to_id] += 1

            # Find potential starting stations (with fewer incoming connections)
            potential_starts = [station_id for station_id, count in station_counts.items()
                              if count == min(station_counts.values())]

            # Start from the first potential start station
            if potential_starts:
                start_station = potential_starts[0]

                # Build an ordered path through stations
                path = [start_station]
                visited = {start_station}

                # Try to build a path through connected stations
                current = start_station
                while connections.get(current) and (next_station := next(iter(connections[current] - visited), None)):
                    path.append(next_station)
                    visited.add(next_station)
                    current = next_station

                # Add any remaining stations that weren't visited
                for station in stations:
                    if station not in visited:
                        path.append(station)
                        visited.add(station)

                # Add junctions for the ordered path
                sequence = 1
                for station_id in path:
                    if (line_id, station_id) not in existing_junctions:
                        try:
                            # Determine if this is a terminus station
                            is_terminus = (sequence == 1 or sequence == len(path))

                            cursor.execute("""
                                           INSERT INTO line_station_junction
                                               (line_id, station_id, sequence, is_terminus)
                                           VALUES (%s, %s, %s, %s)
                                           """, (line_id, station_id, sequence, is_terminus))

                            conn.commit()
                            junctions_created += 1
                            existing_junctions.add((line_id, station_id))
                        except Exception as e:
                            print(f"Error creating junction for station {station_id}: {e}")
                            conn.rollback()

                    sequence += 1
            else:
                # Fallback: just add stations without trying to order them
                sequence = 1
                for station_id in stations:
                    if (line_id, station_id) not in existing_junctions:
                        try:
                            cursor.execute("""
                                           INSERT INTO line_station_junction
                                               (line_id, station_id, sequence, is_terminus)
                                           VALUES (%s, %s, %s, %s)
                                           """, (line_id, station_id, sequence, False))

                            conn.commit()
                            junctions_created += 1
                            existing_junctions.add((line_id, station_id))
                        except Exception as e:
                            print(f"Error creating junction for station {station_id}: {e}")
                            conn.rollback()

                        sequence += 1

        cursor.close()
        conn.close()
        print(f"Created {junctions_created} line-station junctions")
    except Exception as e:
        print(f"Error creating line-station junctions: {e}")
        raise


def main():
    print("ONCF Train Data Import Tool")

    try:
        # Import stations from oncf_cities.json
        station_code_to_id = import_station()

        # Create train lines if they don't exist
        train_line_ids = create_train_lines()

        # Process journey files
        process_journey_files(station_code_to_id, train_line_ids)

        # Create line-station junctions
        create_line_station_junctions(train_line_ids)

        print("ONCF data import completed successfully!")
    except Exception as e:
        print(f"Error in main function: {e}")
        print("Import process failed!")


if __name__ == "__main__":
    main()
