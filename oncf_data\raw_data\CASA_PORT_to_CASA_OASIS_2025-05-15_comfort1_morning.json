{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "206", "ordre": 1, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T05:50:00+01:00", "dateTimeArrivee": "2025-05-15T06:11:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 767, "numeroCommercial": "A5", "hasPlacement": false, "dateHeureDepart": "2025-05-15T05:50:00+01:00", "dateHeureArrivee": "2025-05-15T06:11:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 2, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T06:40:00+01:00", "dateTimeArrivee": "2025-05-15T07:01:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 736, "numeroCommercial": "651", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:40:00+01:00", "dateHeureArrivee": "2025-05-15T07:01:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 3, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T06:50:00+01:00", "dateTimeArrivee": "2025-05-15T07:11:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 765, "numeroCommercial": "A7", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:50:00+01:00", "dateHeureArrivee": "2025-05-15T07:11:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 4, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T07:50:00+01:00", "dateTimeArrivee": "2025-05-15T08:11:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 764, "numeroCommercial": "A9", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:50:00+01:00", "dateHeureArrivee": "2025-05-15T08:11:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 5, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T08:30:00+01:00", "dateTimeArrivee": "2025-05-15T08:53:00+01:00", "durationTrajet": "00:23:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 4313, "numeroCommercial": "803", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:30:00+01:00", "dateHeureArrivee": "2025-05-15T08:53:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:23:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 6, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T08:40:00+01:00", "dateTimeArrivee": "2025-05-15T09:01:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 731, "numeroCommercial": "653", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:40:00+01:00", "dateHeureArrivee": "2025-05-15T09:01:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 7, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T08:50:00+01:00", "dateTimeArrivee": "2025-05-15T09:11:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 727, "numeroCommercial": "A11", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:50:00+01:00", "dateHeureArrivee": "2025-05-15T09:11:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 8, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T09:50:00+01:00", "dateTimeArrivee": "2025-05-15T10:11:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 724, "numeroCommercial": "A13", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:50:00+01:00", "dateHeureArrivee": "2025-05-15T10:11:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 9, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T10:30:00+01:00", "dateTimeArrivee": "2025-05-15T10:53:00+01:00", "durationTrajet": "00:23:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 1556, "numeroCommercial": "805", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:30:00+01:00", "dateHeureArrivee": "2025-05-15T10:53:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "EJ", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:23:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 10, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T10:40:00+01:00", "dateTimeArrivee": "2025-05-15T11:01:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 733, "numeroCommercial": "655", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:40:00+01:00", "dateHeureArrivee": "2025-05-15T11:01:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "206", "ordre": 11, "codeGareArrivee": "191", "dateTimeDepart": "2025-05-15T10:50:00+01:00", "dateTimeArrivee": "2025-05-15T11:11:00+01:00", "durationTrajet": "00:21:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 719, "numeroCommercial": "A15", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:50:00+01:00", "dateHeureArrivee": "2025-05-15T11:11:00+01:00", "codeGareDepart": "206", "codeGareArrivee": "191", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:21:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 30, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 30, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "CASA PORT", "to": "CASA OASIS", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}