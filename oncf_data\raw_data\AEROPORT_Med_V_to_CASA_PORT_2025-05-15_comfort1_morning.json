{"head": {"namespace": "Oncf.ma/DataContracts/2019/04/Ecommerce/Gateway/v1", "version": "v1.0", "returnedLines": 0, "errorCode": 0, "errorMessage": "Success", "errorMessage_Ar": "Success", "errorMessage_En": "Success"}, "body": {"departurePath": [{"codeGareDepart": "190", "ordre": 1, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T05:56:00+01:00", "dateTimeArrivee": "2025-05-15T06:39:00+01:00", "durationTrajet": "00:43:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 6668, "numeroCommercial": "V90004", "hasPlacement": false, "dateHeureDepart": "2025-05-15T05:56:00+01:00", "dateHeureArrivee": "2025-05-15T06:39:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:43:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 2, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T06:50:00+01:00", "dateTimeArrivee": "2025-05-15T07:35:00+01:00", "durationTrajet": "00:45:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 766, "numeroCommercial": "A8", "hasPlacement": false, "dateHeureDepart": "2025-05-15T06:50:00+01:00", "dateHeureArrivee": "2025-05-15T07:35:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:45:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 3, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T07:50:00+01:00", "dateTimeArrivee": "2025-05-15T08:36:00+01:00", "durationTrajet": "00:46:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 723, "numeroCommercial": "A10", "hasPlacement": false, "dateHeureDepart": "2025-05-15T07:50:00+01:00", "dateHeureArrivee": "2025-05-15T08:36:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:46:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 4, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T08:50:00+01:00", "dateTimeArrivee": "2025-05-15T09:36:00+01:00", "durationTrajet": "00:46:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 722, "numeroCommercial": "A12", "hasPlacement": false, "dateHeureDepart": "2025-05-15T08:50:00+01:00", "dateHeureArrivee": "2025-05-15T09:36:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:46:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 5, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T09:50:00+01:00", "dateTimeArrivee": "2025-05-15T10:36:00+01:00", "durationTrajet": "00:46:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 717, "numeroCommercial": "A14", "hasPlacement": false, "dateHeureDepart": "2025-05-15T09:50:00+01:00", "dateHeureArrivee": "2025-05-15T10:36:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:46:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 6, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T10:50:00+01:00", "dateTimeArrivee": "2025-05-15T11:36:00+01:00", "durationTrajet": "00:46:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 713, "numeroCommercial": "A16", "hasPlacement": false, "dateHeureDepart": "2025-05-15T10:50:00+01:00", "dateHeureArrivee": "2025-05-15T11:36:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:46:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 7, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T11:50:00+01:00", "dateTimeArrivee": "2025-05-15T12:36:00+01:00", "durationTrajet": "00:46:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 728, "numeroCommercial": "A18", "hasPlacement": false, "dateHeureDepart": "2025-05-15T11:50:00+01:00", "dateHeureArrivee": "2025-05-15T12:36:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:46:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 8, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T12:50:00+01:00", "dateTimeArrivee": "2025-05-15T13:36:00+01:00", "durationTrajet": "00:46:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 737, "numeroCommercial": "A20", "hasPlacement": false, "dateHeureDepart": "2025-05-15T12:50:00+01:00", "dateHeureArrivee": "2025-05-15T13:36:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:46:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 9, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T13:50:00+01:00", "dateTimeArrivee": "2025-05-15T14:36:00+01:00", "durationTrajet": "00:46:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 742, "numeroCommercial": "A22", "hasPlacement": false, "dateHeureDepart": "2025-05-15T13:50:00+01:00", "dateHeureArrivee": "2025-05-15T14:36:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:46:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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**************************/ZptKh/I9z7s5QDe8t7ykDxbAj85GYaQHTzJG+z6G3rUFjdeyLTRM8W7gMHH9MOOmZrQG19teyTEH9sne8nqWTL4uQXSVVfPPzA/2h1pcasLFzu73/Q7KBTC/TBur4OLE0h4VHQ8y7cV6Bp+QnHLd6G5YnQLT6brO6MsnnZQK10zL0aNuMg7Adbvmq/lN0MYp1JJkj6r3H87GwltxxZA3GMyFjb1PXt3pU4o6EX9fwkF+fh7LdIgfqpbCP4qxTaF5R3Wv+N9LzIFakzaCOYDH6aWI5L9VNsv2puhjOuFLvxx3aTgkVGtOeGMe/4T33LFkrSvgzKam1ssoCtagvZAtmoGIuaRIeRZMfqU+K0tkCG02LWKl6KqEORlYKvWichJBYPwSRgTfyuDf+32dkX0lNqpDuV+drkOiQ/LEhx8pByS2lQTIUd14sflX5X1cEt5LPCoaGxyY3A2MAwi8+ViGAZxFNGgMOHaYAPnDJBnWB2PMxL1o6xMgdsIU7CbeKo37b0PhIw9k+ki6FvReQhvq0nd5axw2qHnZOvhQLWjKfAqvoe51ROp0vnOEIs1liLvEFipyhpmDdHbZhnC+6Ly8/ae1qbYCsW/BgvWI7u/9GSOx3VKQRWFCuELdYOgeZxdBK7Y0YwCRIJHOwhOqHrv9/pOVRiwPcfCXHtU5ubQlbXGeoW7DpbwdjRkhiHIoPg630zyhfmLi6+gQFmWBP629dyRqRyyU789GinV/hmNrQArULf5UyPavooFuAmz0Q4nMQVqec1ekt7qmEHrtMFRhKMY+V+JrltKnyTs3T59YgJwgucua04O4Be+0B5j4WwGip00mjydsymqoo7GTM+FOPWRLaoXYk00xhCCR5JHPeqifamLACNP2Gynk+kOhdYjDKVgPw8jQDrzcuVNQz+GAsG2CHGOpWJt9LWNuvPfwyQz8jVz6NWK9CywzLLbxWOpAMvYIzZE6g2V520OPhAkpwoXKdVTkUcDaVD0bcggVC+0EJNcnrDArnHIObQO3bNAuskG/LyWZG8/8g4SKLMhWbd5vOLR7cvlHoLkvdzouaqwR/RYVrxszX+1bUEakoiTfTX5lmtzFK3Uq/mWbvjEcgDkqYR5jTxc4r3nL1bm3QFiP7EL13F/H9wMR/cvFuzoFtqmlG09qWKWtpRgHLP8em15/5Zj8vMQaI6LzBM9A02fY/GOQnduAybsbHPBNVkBIb3pB41m3O52APhj3AMl3xX9HlwcWXoW68V/ZFxmwOf73hG+cqXC8Gs6U+SCtIFZ2EIxq9XZxAh9PrnB4n5UZZAaLbkpXzyppN+ZXqlUHQlJK9iCB4tia0C1jgMbsDdMaPTJjzeXgQYoTwr7aFnhovuJcKDbbDNBH5T7sbjjqIAk4rr1s/WmtsmHjfR7LVs8oIQg2u0WxWk3gQp6QAFyu7X4D8vXX5/pPBSrtUY/Dd0uxBzEfvehnG+cCPpL1dLFRC4saL8J23X7Q0hkNwOuyPbCLKuP85M/2EM2TVdMb1hEBg4SRex6anqGQpfuv7gGL6Mry6Rrm+r+kUQfnM3YzB70R6UAcTNQ0Yutnii8WxUek7XR4HhGgWS2PkeUVxcR/IEJeB2dzA2pRfkoX8pxH1ezpnVm56u8ukkJmQLGhTmDwD52rEessBsIxaOYRzhlidoy7WLd7BaqFubLkgjZknknXcFAWt9cHcQpm+6LSCL+VXbax6wbwSaJtERejaBppyFXR5noFDzHiuMuTJtA9O4zWiuyVuPxR1mQ3Ixln7kszOmp13Y3OyQuBmrMIazgQGNNEs+cmQ7rWZEv76qA3TMgogtGK8npW3VSrkun4l9P8unLxigqqrPuj0eY7hipFlgdGfuAgtudWOnExWWRm+qbm2THe2hhcThH9x5O7/paiYIeou3gBW0pE9EK4AlWOCMtDXT7eWIPLD0HwQopjTjBMLlgVeBcIeI0E="}]}]}, {"codeGareDepart": "190", "ordre": 10, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T14:50:00+01:00", "dateTimeArrivee": "2025-05-15T15:36:00+01:00", "durationTrajet": "00:46:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 745, "numeroCommercial": "A24", "hasPlacement": false, "dateHeureDepart": "2025-05-15T14:50:00+01:00", "dateHeureArrivee": "2025-05-15T15:36:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:46:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}, {"codeGareDepart": "190", "ordre": 11, "codeGareArrivee": "206", "dateTimeDepart": "2025-05-15T15:50:00+01:00", "dateTimeArrivee": "2025-05-15T16:36:00+01:00", "durationTrajet": "00:46:00", "typeVente": "1", "express": false, "listSegments": [{"codeTrainAutoCar": 743, "numeroCommercial": "A26", "hasPlacement": false, "dateHeureDepart": "2025-05-15T15:50:00+01:00", "dateHeureArrivee": "2025-05-15T16:36:00+01:00", "codeGareDepart": "190", "codeGareArrivee": "206", "codeGamme": "48", "codeClassification": "TNR", "codeNiveauConfort": "1", "codeNiveauConfortSup": "3", "duree": "00:46:00", "order": "1", "jourApres": null, "jourAvant": null, "dateCirculation": "2025-05-15T00:00:00+01:00", "listeCritere": [], "configurationVenteEnum": 2, "configurationVenteSupEnum": 0}], "voyageurs": [{"prenom": null, "nom": null, "prenomAr": null, "nomAr": null, "codeCivilite": "0", "numeroClient": null, "codeConvention": null, "numeroTitre": null, "tarifRevendique": null, "codeProfilDemographique": "3", "email": null, "telephone": null, "typeTarifRevendique": null, "codeClient": null, "codeTiers": null}], "listPrixFlexibilite": [{"typeFexibilite": "1", "prixFlexibilite": [{"prix": 80, "toolTipConditionVenteApresVenteAr": "قابل للاستبدال مجانًا 5 مرات قبل مغادرة قطارك. قابل للاسترداد دون خصم إلى حدود اليوم السابق لرحلتك وبخصم 20٪ يوم السفر.", "toolTipConditionVenteApresVenteEn": "Exchangeable free of charge 5 times before the departure of your train. Refundable without deduction up to the day before your journey and with a 20% price deduction on the travel day.", "toolTipConditionVenteApresVenteFr": "Echangeable gratuitement 5 fois avant le départ de votre train. Remboursable sans aucune retenue jusqu'à la veille de votre voyage et avec une retenue de 20% le jour de voyage.", "disponibilite": null, "typeVente": 1, "sup": false, "tarif": [{"tarifId": "802", "codeTarif": "GF", "typeRevendicationId": 1, "price": 80, "profilInfoId": "3"}], "encodedAttributs": "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"}]}]}], "arrivalPath": [], "nextCta": {"next": true, "prev": true}, "backCta": {"next": false, "prev": false}}, "route": {"from": "AEROPORT Med V", "to": "CASA PORT", "date": "2025-05-15", "comfort": 1, "time_period": "morning"}, "no_trains_available": true}